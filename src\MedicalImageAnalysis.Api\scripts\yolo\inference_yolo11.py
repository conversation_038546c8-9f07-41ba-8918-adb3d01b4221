#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11 模型推理脚本
支持单张图片和批量推理
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any
import cv2
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from ultralytics import YOLO
    from ultralytics.utils import LOGGER
    LOGGER.setLevel(logging.WARNING)
except ImportError:
    logger.error("请安装ultralytics库: pip install ultralytics>=8.3.0")
    sys.exit(1)

class YOLOv11Inference:
    """YOLOv11 推理器"""
    
    def __init__(self, model_path: str, config: Dict[str, Any]):
        """初始化推理器
        
        Args:
            model_path: 模型文件路径
            config: 推理配置
        """
        self.model_path = model_path
        self.config = config
        
        # 加载模型
        try:
            self.model = YOLO(model_path)
            logger.info(f"加载模型: {model_path}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def predict_single(self, image_path: str, output_dir: str = None) -> Dict[str, Any]:
        """单张图片推理
        
        Args:
            image_path: 图片路径
            output_dir: 输出目录
            
        Returns:
            推理结果
        """
        try:
            # 推理参数
            conf = self.config.get('confidence_threshold', 0.5)
            iou = self.config.get('iou_threshold', 0.45)
            imgsz = self.config.get('image_size', 640)
            max_det = self.config.get('max_detections', 300)
            
            # 执行推理
            results = self.model.predict(
                source=image_path,
                conf=conf,
                iou=iou,
                imgsz=imgsz,
                max_det=max_det,
                save=False,
                verbose=False
            )
            
            # 解析结果
            detections = []
            if results and len(results) > 0:
                result = results[0]
                
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()  # x1, y1, x2, y2
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy().astype(int)
                    
                    # 获取图片尺寸
                    img = cv2.imread(image_path)
                    img_height, img_width = img.shape[:2]
                    
                    for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                        x1, y1, x2, y2 = box
                        
                        # 转换为YOLO格式 (center_x, center_y, width, height) 相对坐标
                        center_x = (x1 + x2) / 2 / img_width
                        center_y = (y1 + y2) / 2 / img_height
                        width = (x2 - x1) / img_width
                        height = (y2 - y1) / img_height
                        
                        detection = {
                            'label': self.model.names.get(cls_id, f'class_{cls_id}'),
                            'confidence': float(conf),
                            'class_id': int(cls_id),
                            'bounding_box': {
                                'center_x': float(center_x),
                                'center_y': float(center_y),
                                'width': float(width),
                                'height': float(height)
                            },
                            'absolute_box': {
                                'x1': float(x1),
                                'y1': float(y1),
                                'x2': float(x2),
                                'y2': float(y2)
                            }
                        }
                        detections.append(detection)
            
            result_data = {
                'image_path': image_path,
                'detections': detections,
                'num_detections': len(detections),
                'model_path': self.model_path,
                'config': self.config
            }
            
            # 保存结果
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                result_file = os.path.join(output_dir, 'inference_results.json')
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(result_data, f, indent=2, ensure_ascii=False)
                logger.info(f"推理结果保存到: {result_file}")
            
            return result_data
            
        except Exception as e:
            logger.error(f"推理失败: {e}")
            return {
                'image_path': image_path,
                'detections': [],
                'num_detections': 0,
                'error': str(e)
            }
    
    def predict_batch(self, image_paths: List[str], output_dir: str = None) -> List[Dict[str, Any]]:
        """批量推理
        
        Args:
            image_paths: 图片路径列表
            output_dir: 输出目录
            
        Returns:
            推理结果列表
        """
        results = []
        
        for i, image_path in enumerate(image_paths):
            logger.info(f"处理图片 {i+1}/{len(image_paths)}: {image_path}")
            result = self.predict_single(image_path, output_dir)
            results.append(result)
        
        # 保存批量结果
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            batch_result_file = os.path.join(output_dir, 'batch_inference_results.json')
            with open(batch_result_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"批量推理结果保存到: {batch_result_file}")
        
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            info = {
                'model_path': self.model_path,
                'model_type': 'YOLOv11',
                'class_names': list(self.model.names.values()) if hasattr(self.model, 'names') else [],
                'num_classes': len(self.model.names) if hasattr(self.model, 'names') else 0,
                'input_size': self.config.get('image_size', 640),
                'confidence_threshold': self.config.get('confidence_threshold', 0.5),
                'iou_threshold': self.config.get('iou_threshold', 0.45)
            }
            return info
        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return {'error': str(e)}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLOv11 模型推理')
    parser.add_argument('--model', required=True, help='模型文件路径')
    parser.add_argument('--image', help='单张图片路径')
    parser.add_argument('--images', nargs='+', help='多张图片路径')
    parser.add_argument('--config', required=True, help='推理配置文件路径')
    parser.add_argument('--output', help='输出目录')
    parser.add_argument('--info', action='store_true', help='获取模型信息')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.model):
        logger.error(f"模型文件不存在: {args.model}")
        sys.exit(1)
    
    # 加载配置
    if not os.path.exists(args.config):
        logger.error(f"配置文件不存在: {args.config}")
        sys.exit(1)
    
    with open(args.config, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 创建推理器
    inference = YOLOv11Inference(args.model, config)
    
    if args.info:
        # 获取模型信息
        info = inference.get_model_info()
        print(json.dumps(info, indent=2, ensure_ascii=False))
        return
    
    if args.image:
        # 单张图片推理
        if not os.path.exists(args.image):
            logger.error(f"图片文件不存在: {args.image}")
            sys.exit(1)
        
        result = inference.predict_single(args.image, args.output)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    elif args.images:
        # 批量推理
        valid_images = []
        for img_path in args.images:
            if os.path.exists(img_path):
                valid_images.append(img_path)
            else:
                logger.warning(f"图片文件不存在: {img_path}")
        
        if not valid_images:
            logger.error("没有有效的图片文件")
            sys.exit(1)
        
        results = inference.predict_batch(valid_images, args.output)
        print(json.dumps(results, indent=2, ensure_ascii=False))
        
    else:
        logger.error("请指定 --image 或 --images 参数")
        sys.exit(1)

if __name__ == '__main__':
    main()
