using Microsoft.AspNetCore.Mvc;
using MedicalImageAnalysis.Application.Services;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;

namespace MedicalImageAnalysis.Api.Controllers;

/// <summary>
/// 研究处理控制器，提供 DICOM 研究的处理和管理 API
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class StudyController : ControllerBase
{
    private readonly StudyProcessingService _studyProcessingService;
    private readonly IDicomService _dicomService;
    private readonly ILogger<StudyController> _logger;

    public StudyController(
        StudyProcessingService studyProcessingService,
        IDicomService dicomService,
        ILogger<StudyController> logger)
    {
        _studyProcessingService = studyProcessingService;
        _dicomService = dicomService;
        _logger = logger;
    }

    /// <summary>
    /// 上传并处理 DICOM 文件
    /// </summary>
    /// <param name="files">DICOM 文件集合</param>
    /// <param name="config">处理配置</param>
    /// <returns>处理结果</returns>
    [HttpPost("upload")]
    [ProducesResponseType(typeof(StudyProcessingResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<StudyProcessingResult>> UploadAndProcessStudy(
        [FromForm] IFormFileCollection files,
        [FromForm] StudyProcessingConfig? config = null)
    {
        try
        {
            if (!files.Any())
            {
                return BadRequest("没有上传任何文件");
            }

            // 验证文件类型
            var invalidFiles = files.Where(f => !f.FileName.EndsWith(".dcm", StringComparison.OrdinalIgnoreCase)).ToList();
            if (invalidFiles.Any())
            {
                return BadRequest($"包含非 DICOM 文件: {string.Join(", ", invalidFiles.Select(f => f.FileName))}");
            }

            // 保存上传的文件
            var tempDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDirectory);

            var filePaths = new List<string>();
            foreach (var file in files)
            {
                var filePath = Path.Combine(tempDirectory, file.FileName);
                using var stream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(stream);
                filePaths.Add(filePath);
            }

            // 使用默认配置如果没有提供
            config ??= new StudyProcessingConfig
            {
                EnableImagePreprocessing = true,
                EnableAIDetection = false, // 默认关闭 AI 检测，因为可能没有模型
                EnableAnnotationValidation = true,
                EnableDatasetExport = false
            };

            // 处理研究
            var result = await _studyProcessingService.ProcessStudyAsync(filePaths, config);

            // 清理临时文件
            try
            {
                Directory.Delete(tempDirectory, true);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清理临时文件失败: {TempDirectory}", tempDirectory);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理 DICOM 研究失败");
            return StatusCode(500, new { error = "处理失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 验证 DICOM 文件
    /// </summary>
    /// <param name="file">DICOM 文件</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(DicomValidationResult), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<DicomValidationResult>> ValidateDicomFile(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("没有上传文件");
            }

            // 保存临时文件
            var tempFilePath = Path.GetTempFileName();
            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // 验证文件
            var result = await _dicomService.ValidateDicomFileAsync(tempFilePath);

            // 清理临时文件
            System.IO.File.Delete(tempFilePath);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证 DICOM 文件失败");
            return StatusCode(500, new { error = "验证失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 提取 DICOM 文件元数据
    /// </summary>
    /// <param name="file">DICOM 文件</param>
    /// <returns>元数据</returns>
    [HttpPost("metadata")]
    [ProducesResponseType(typeof(Dictionary<string, object>), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<Dictionary<string, object>>> ExtractMetadata(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("没有上传文件");
            }

            // 保存临时文件
            var tempFilePath = Path.GetTempFileName();
            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // 提取元数据
            var metadata = await _dicomService.ExtractMetadataAsync(tempFilePath);

            // 清理临时文件
            System.IO.File.Delete(tempFilePath);

            return Ok(metadata);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取元数据失败");
            return StatusCode(500, new { error = "提取失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取研究统计信息
    /// </summary>
    /// <param name="studyId">研究ID</param>
    /// <returns>统计信息</returns>
    [HttpGet("{studyId}/statistics")]
    [ProducesResponseType(typeof(StudyStatistics), 200)]
    [ProducesResponseType(404)]
    public Task<ActionResult<StudyStatistics>> GetStudyStatistics(Guid studyId)
    {
        try
        {
            // 这里应该从数据库或缓存中获取研究
            // 为了演示，我们返回一个示例统计信息
            var statistics = new StudyStatistics
            {
                TotalSeries = 1,
                TotalInstances = 10,
                TotalFileSize = 1024 * 1024 * 50, // 50MB
                AverageSliceThickness = 5.0,
                PixelSpacingRange = (0.5, 1.0, 0.5, 1.0),
                ImageSizeRange = (512, 512, 512, 512),
                Modalities = { "CT" },
                BodyParts = { "HEAD" },
                OrientationDistribution = { { ImageOrientation.Axial, 10 } }
            };

            return Task.FromResult<ActionResult<StudyStatistics>>(Ok(statistics));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取研究统计信息失败: {StudyId}", studyId);
            return Task.FromResult<ActionResult<StudyStatistics>>(StatusCode(500, new { error = "获取失败", message = ex.Message }));
        }
    }

    /// <summary>
    /// 获取系统健康状态
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "1.0.0",
            services = new
            {
                dicom = "available",
                imageProcessing = "available",
                annotation = "available"
            }
        });
    }

    /// <summary>
    /// 获取支持的图像格式
    /// </summary>
    /// <returns>支持的格式列表</returns>
    [HttpGet("supported-formats")]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetSupportedFormats()
    {
        return Ok(new
        {
            input = new[] { "dcm", "dicom" },
            output = new[] { "png", "jpeg", "bmp", "tiff", "webp" },
            annotation = new[] { "yolo", "coco", "pascal_voc", "cvat", "labelme" }
        });
    }

    /// <summary>
    /// 获取默认处理配置
    /// </summary>
    /// <returns>默认配置</returns>
    [HttpGet("default-config")]
    [ProducesResponseType(typeof(StudyProcessingConfig), 200)]
    public ActionResult<StudyProcessingConfig> GetDefaultConfig()
    {
        var config = new StudyProcessingConfig
        {
            EnableImagePreprocessing = true,
            PreprocessingOptions = new ImagePreprocessingOptions
            {
                Normalize = true,
                NormalizationRange = (0.0, 1.0),
                Resize = false,
                TargetSize = (512, 512),
                HistogramEqualization = false,
                ApplyClahe = false
            },
            EnableAIDetection = false,
            InferenceConfig = new YoloInferenceConfig
            {
                ConfidenceThreshold = 0.5,
                IouThreshold = 0.45,
                MaxDetections = 300,
                ImageSize = 640
            },
            EnableAnnotationValidation = true,
            ValidationRules = new AnnotationValidationRules
            {
                MinConfidence = 0.1,
                MaxConfidence = 1.0,
                MinBoundingBoxArea = 0.0001,
                MaxBoundingBoxArea = 0.9,
                CheckBoundingBoxBounds = true,
                CheckDuplicateAnnotations = true,
                DuplicateIouThreshold = 0.8
            },
            EnableDatasetExport = false,
            ExportConfig = new DatasetExportConfig
            {
                Format = DatasetFormat.YOLO,
                TrainRatio = 0.8,
                ValidationRatio = 0.15,
                TestRatio = 0.05,
                ImageFormat = ImageFormat.Png,
                ImageQuality = 95,
                MaintainAspectRatio = true,
                IncludeNegativeSamples = false
            }
        };

        return Ok(config);
    }
}
