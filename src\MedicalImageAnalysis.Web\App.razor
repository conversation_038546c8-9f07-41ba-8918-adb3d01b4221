@using Microsoft.AspNetCore.Components.Routing

<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)" />
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>页面未找到</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <div class="d-flex justify-content-center align-items-center" style="height: 50vh;">
                <div class="text-center">
                    <h1 class="display-1 text-muted">404</h1>
                    <h2>页面未找到</h2>
                    <p class="text-muted">抱歉，您访问的页面不存在。</p>
                    <a href="/" class="btn btn-primary">返回首页</a>
                </div>
            </div>
        </LayoutView>
    </NotFound>
</Router>
