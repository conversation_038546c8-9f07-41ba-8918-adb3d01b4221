/* 医学影像解析系统 - 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* 页面布局 */
.page {
    position: relative;
    display: flex;
    width: 100%;
}

.sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.content {
    flex: 1;
}

.top-row {
    background-color: #f5f5f5;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

/* 卡片样式增强 */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* 按钮样式增强 */
.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* 特色图标 */
.feature-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    border-radius: 50%;
    margin: 0 auto;
}

/* 状态指示器 */
.status-item {
    padding: 1rem 0;
    text-align: center;
}

.status-value {
    margin-bottom: 0.5rem;
}

.status-label {
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-weight: 500;
}

/* 目录卡片样式 */
.directory-card {
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: var(--transition);
    height: 100%;
    display: flex;
    align-items: center;
    position: relative;
    background: white;
}

.directory-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.15);
    transform: translateY(-2px);
}

.directory-card:hover .directory-actions {
    opacity: 1;
}

.directory-icon {
    font-size: 2rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.directory-info {
    flex-grow: 1;
}

.directory-path {
    margin-top: 0.25rem;
    font-family: 'Courier New', monospace;
    background-color: var(--light-color);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #e9ecef;
    font-size: 0.8rem;
}

.directory-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* 进度条样式 */
.progress {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* 表格样式 */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: var(--light-color);
}

/* 导航样式 */
.nav-link {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    border-radius: var(--border-radius);
}

/* 输入框样式 */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
    background-color: #fafafa;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: var(--primary-color);
    background-color: #f0f8ff;
}

.upload-area.drag-over {
    border-color: var(--primary-color);
    background-color: #e7f1ff;
    transform: scale(1.02);
}

/* 加载动画 */
.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .directory-card {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem 1rem;
    }

    .directory-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .directory-actions {
        position: static;
        opacity: 1;
        margin-top: 1rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* 自定义颜色类 */
.text-purple {
    color: #6f42c1 !important;
}

.bg-purple {
    background-color: #6f42c1 !important;
}

.border-purple {
    border-color: #6f42c1 !important;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 打印样式 */
@media print {
    .sidebar,
    .top-row,
    .btn,
    .alert {
        display: none !important;
    }

    .content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}
