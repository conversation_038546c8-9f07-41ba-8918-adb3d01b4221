using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 智能标注服务，提供高级的自动标注和标注优化功能
/// </summary>
public class SmartAnnotationService : ISmartAnnotationService
{
    private readonly ILogger<SmartAnnotationService> _logger;
    private readonly IAnnotationService _baseAnnotationService;
    private readonly IYoloService _yoloService;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IAdvancedImageProcessingService _advancedImageProcessingService;

    public SmartAnnotationService(
        ILogger<SmartAnnotationService> logger,
        IAnnotationService baseAnnotationService,
        IYoloService yoloService,
        IImageProcessingService imageProcessingService,
        IAdvancedImageProcessingService advancedImageProcessingService)
    {
        _logger = logger;
        _baseAnnotationService = baseAnnotationService;
        _yoloService = yoloService;
        _imageProcessingService = imageProcessingService;
        _advancedImageProcessingService = advancedImageProcessingService;
    }

    /// <summary>
    /// 智能标注生成
    /// </summary>
    public async Task<SmartAnnotationResult> GenerateSmartAnnotationsAsync(
        DicomInstance instance, 
        SmartAnnotationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始智能标注生成，实例: {InstanceUid}", instance.SopInstanceUid);

        var result = new SmartAnnotationResult
        {
            InstanceId = instance.Id,
            StartTime = DateTime.UtcNow
        };

        try
        {
            // 1. 多模型集成推理
            var multiModelAnnotations = await MultiModelInferenceAsync(instance, config, cancellationToken);
            result.RawAnnotations = multiModelAnnotations;

            // 2. 标注融合
            var fusedAnnotations = await FuseAnnotationsAsync(multiModelAnnotations, config.FusionConfig);
            result.FusedAnnotations = fusedAnnotations;

            // 3. 后处理优化
            var optimizedAnnotations = await PostProcessAnnotationsAsync(fusedAnnotations, instance, config.PostProcessingConfig);
            result.OptimizedAnnotations = optimizedAnnotations;

            // 4. 质量评估
            var qualityScores = await EvaluateAnnotationQualityAsync(optimizedAnnotations, instance);
            result.QualityScores = qualityScores;

            // 5. 智能过滤
            var filteredAnnotations = await SmartFilterAnnotationsAsync(optimizedAnnotations, qualityScores, config.FilterConfig);
            result.FinalAnnotations = filteredAnnotations;

            result.Success = true;
            result.ProcessingTimeMs = (DateTime.UtcNow - result.StartTime).TotalMilliseconds;

            _logger.LogInformation("智能标注生成完成，生成 {Count} 个标注", filteredAnnotations.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "智能标注生成失败");
            result.Success = false;
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    /// <summary>
    /// 自适应标注优化
    /// </summary>
    public async Task<List<Annotation>> AdaptiveAnnotationOptimizationAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        AdaptiveOptimizationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始自适应标注优化，标注数量: {Count}", annotations.Count);

        try
        {
            var optimizedAnnotations = new List<Annotation>();

            foreach (var annotation in annotations)
            {
                var optimized = await OptimizeSingleAnnotationAsync(annotation, instance, config, cancellationToken);
                optimizedAnnotations.Add(optimized);
            }

            // 全局优化
            optimizedAnnotations = await GlobalOptimizationAsync(optimizedAnnotations, config);

            _logger.LogInformation("自适应标注优化完成");
            return optimizedAnnotations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自适应标注优化失败");
            throw;
        }
    }

    /// <summary>
    /// 标注质量自动评估
    /// </summary>
    public async Task<AnnotationQualityReport> AutoEvaluateAnnotationQualityAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        QualityEvaluationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始标注质量自动评估");

        try
        {
            var report = new AnnotationQualityReport
            {
                InstanceId = instance.Id,
                EvaluationTime = DateTime.UtcNow,
                TotalAnnotations = annotations.Count
            };

            // 1. 几何质量评估
            var geometricScores = await EvaluateGeometricQualityAsync(annotations, instance);
            report.GeometricQualityScores = geometricScores;

            // 2. 语义质量评估
            var semanticScores = await EvaluateSemanticQualityAsync(annotations, instance, config);
            report.SemanticQualityScores = semanticScores;

            // 3. 一致性评估
            var consistencyScores = await EvaluateConsistencyAsync(annotations);
            report.ConsistencyScores = consistencyScores;

            // 4. 完整性评估
            var completenessScore = await EvaluateCompletenessAsync(annotations, instance, config);
            report.CompletenessScore = completenessScore;

            // 5. 计算总体质量分数
            report.OverallQualityScore = CalculateOverallQualityScore(report);

            // 6. 生成改进建议
            report.ImprovementSuggestions = await GenerateImprovementSuggestionsAsync(report, annotations);

            _logger.LogInformation("标注质量评估完成，总体质量分数: {Score:F2}", report.OverallQualityScore);
            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标注质量评估失败");
            throw;
        }
    }

    /// <summary>
    /// 智能标注推荐
    /// </summary>
    public async Task<List<AnnotationRecommendation>> GenerateSmartRecommendationsAsync(
        DicomInstance instance, 
        List<Annotation> existingAnnotations,
        SmartRecommendationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始生成智能标注推荐");

        try
        {
            var recommendations = new List<AnnotationRecommendation>();

            // 1. 基于上下文的推荐
            var contextRecommendations = await GenerateContextBasedRecommendationsAsync(instance, existingAnnotations, config);
            recommendations.AddRange(contextRecommendations);

            // 2. 基于模式识别的推荐
            var patternRecommendations = await GeneratePatternBasedRecommendationsAsync(instance, existingAnnotations, config);
            recommendations.AddRange(patternRecommendations);

            // 3. 基于解剖结构的推荐
            var anatomyRecommendations = await GenerateAnatomyBasedRecommendationsAsync(instance, existingAnnotations, config);
            recommendations.AddRange(anatomyRecommendations);

            // 4. 基于历史数据的推荐
            var historyRecommendations = await GenerateHistoryBasedRecommendationsAsync(instance, existingAnnotations, config);
            recommendations.AddRange(historyRecommendations);

            // 5. 推荐排序和过滤
            recommendations = await RankAndFilterRecommendationsAsync(recommendations, config);

            _logger.LogInformation("智能推荐生成完成，推荐数量: {Count}", recommendations.Count);
            return recommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "智能推荐生成失败");
            throw;
        }
    }

    /// <summary>
    /// 标注数据增强
    /// </summary>
    public async Task<AnnotationAugmentationResult> AugmentAnnotationDataAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        AnnotationAugmentationConfig config,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始标注数据增强");

        try
        {
            var result = new AnnotationAugmentationResult
            {
                OriginalAnnotations = annotations,
                AugmentationConfig = config
            };

            var augmentedData = new List<MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData>();

            // 1. 几何变换增强
            if (config.EnableGeometricAugmentation)
            {
                var geometricAugmented = await ApplyGeometricAugmentationAsync(annotations, instance, config.GeometricConfig);
                augmentedData.AddRange(geometricAugmented.Select(x => new MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData
                {
                    OriginalAnnotationId = x.OriginalAnnotation.Id,
                    AugmentedAnnotation = x.AugmentedAnnotation,
                    AugmentationType = x.AugmentationType,
                    AugmentationParameters = x.Parameters
                }));
            }

            // 2. 图像增强
            if (config.EnableImageAugmentation)
            {
                var imageAugmented = await ApplyImageAugmentationAsync(annotations, instance, config.ImageConfig);
                augmentedData.AddRange(imageAugmented.Select(x => new MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData
                {
                    OriginalAnnotationId = x.OriginalAnnotation.Id,
                    AugmentedAnnotation = x.AugmentedAnnotation,
                    AugmentationType = x.AugmentationType,
                    AugmentationParameters = x.Parameters
                }));
            }

            // 3. 噪声增强
            if (config.EnableNoiseAugmentation)
            {
                var noiseAugmented = await ApplyNoiseAugmentationAsync(annotations, instance, config.NoiseConfig);
                augmentedData.AddRange(noiseAugmented.Select(x => new MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData
                {
                    OriginalAnnotationId = x.OriginalAnnotation.Id,
                    AugmentedAnnotation = x.AugmentedAnnotation,
                    AugmentationType = x.AugmentationType,
                    AugmentationParameters = x.Parameters
                }));
            }

            // 4. 合成数据生成
            if (config.EnableSyntheticGeneration)
            {
                var syntheticData = await GenerateSyntheticAnnotationsAsync(annotations, instance, config.SyntheticConfig);
                augmentedData.AddRange(syntheticData.Select(x => new MedicalImageAnalysis.Core.Interfaces.AugmentedAnnotationData
                {
                    OriginalAnnotationId = x.OriginalAnnotation.Id,
                    AugmentedAnnotation = x.AugmentedAnnotation,
                    AugmentationType = x.AugmentationType,
                    AugmentationParameters = x.Parameters
                }));
            }

            result.AugmentedData = augmentedData;
            result.TotalAugmentedSamples = augmentedData.Count;
            result.Success = true;

            _logger.LogInformation("标注数据增强完成，生成 {Count} 个增强样本", augmentedData.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标注数据增强失败");
            return new AnnotationAugmentationResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                OriginalAnnotations = annotations,
                AugmentationConfig = config
            };
        }
    }

    #region 私有方法

    /// <summary>
    /// 优化单个标注
    /// </summary>
    private async Task<Annotation> OptimizeSingleAnnotationAsync(Annotation annotation, DicomInstance instance, AdaptiveOptimizationConfig config, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var optimized = new Annotation
        {
            Id = annotation.Id,
            Type = annotation.Type,
            Label = annotation.Label,
            Description = annotation.Description + " (已优化)",
            Confidence = annotation.Confidence,
            BoundingBox = annotation.BoundingBox,
            PolygonPoints = annotation.PolygonPoints,
            Source = annotation.Source,
            CreatedBy = annotation.CreatedBy,
            InstanceId = annotation.InstanceId,
            Instance = annotation.Instance,
            CreatedAt = annotation.CreatedAt,
            UpdatedAt = DateTime.UtcNow
        };

        // 边缘细化
        if (config.EnableEdgeRefinement)
        {
            optimized = await RefineAnnotationEdgesAsync(optimized, instance);
        }

        // 形状优化
        if (config.EnableShapeOptimization)
        {
            optimized = await OptimizeAnnotationShapeAsync(optimized, instance);
        }

        // 上下文调整
        if (config.EnableContextualAdjustment)
        {
            optimized = await AdjustAnnotationContextuallyAsync(optimized, instance);
        }

        return optimized;
    }

    /// <summary>
    /// 全局优化
    /// </summary>
    private async Task<List<Annotation>> GlobalOptimizationAsync(List<Annotation> annotations, AdaptiveOptimizationConfig config)
    {
        await Task.CompletedTask;

        // 去除重叠标注
        var nonOverlapping = RemoveOverlappingAnnotations(annotations);

        // 合并相邻标注
        var merged = MergeAdjacentAnnotations(nonOverlapping);

        return merged;
    }

    /// <summary>
    /// 应用几何增强
    /// </summary>
    private async Task<List<AugmentedAnnotationData>> ApplyGeometricAugmentationAsync(List<Annotation> annotations, DicomInstance instance, GeometricAugmentationConfig config)
    {
        await Task.CompletedTask;

        var augmentedData = new List<AugmentedAnnotationData>();

        foreach (var annotation in annotations)
        {
            // 旋转增强
            if (config.EnableRotation)
            {
                for (int i = 0; i < config.RotationSamples; i++)
                {
                    var angle = Random.Shared.NextDouble() * (config.RotationRange.Max - config.RotationRange.Min) + config.RotationRange.Min;
                    var rotatedAnnotation = RotateAnnotation(annotation, angle);
                    augmentedData.Add(new AugmentedAnnotationData
                    {
                        OriginalAnnotation = annotation,
                        AugmentedAnnotation = rotatedAnnotation,
                        AugmentationType = "Rotation",
                        Parameters = new Dictionary<string, object> { { "angle", angle } }
                    });
                }
            }

            // 缩放增强
            if (config.EnableScaling)
            {
                for (int i = 0; i < config.ScalingSamples; i++)
                {
                    var scale = Random.Shared.NextDouble() * (config.ScaleRange.Max - config.ScaleRange.Min) + config.ScaleRange.Min;
                    var scaledAnnotation = ScaleAnnotation(annotation, scale);
                    augmentedData.Add(new AugmentedAnnotationData
                    {
                        OriginalAnnotation = annotation,
                        AugmentedAnnotation = scaledAnnotation,
                        AugmentationType = "Scaling",
                        Parameters = new Dictionary<string, object> { { "scale", scale } }
                    });
                }
            }
        }

        return augmentedData;
    }

    /// <summary>
    /// 应用图像增强
    /// </summary>
    private async Task<List<AugmentedAnnotationData>> ApplyImageAugmentationAsync(List<Annotation> annotations, DicomInstance instance, ImageAugmentationConfig config)
    {
        await Task.CompletedTask;

        var augmentedData = new List<AugmentedAnnotationData>();

        foreach (var annotation in annotations)
        {
            // 亮度调整
            if (config.EnableBrightnessAdjustment)
            {
                var brightness = Random.Shared.NextDouble() * (config.BrightnessRange.Max - config.BrightnessRange.Min) + config.BrightnessRange.Min;
                augmentedData.Add(new AugmentedAnnotationData
                {
                    OriginalAnnotation = annotation,
                    AugmentedAnnotation = annotation, // 图像增强不改变标注本身
                    AugmentationType = "Brightness",
                    Parameters = new Dictionary<string, object> { { "brightness", brightness } }
                });
            }

            // 对比度调整
            if (config.EnableContrastAdjustment)
            {
                var contrast = Random.Shared.NextDouble() * (config.ContrastRange.Max - config.ContrastRange.Min) + config.ContrastRange.Min;
                augmentedData.Add(new AugmentedAnnotationData
                {
                    OriginalAnnotation = annotation,
                    AugmentedAnnotation = annotation,
                    AugmentationType = "Contrast",
                    Parameters = new Dictionary<string, object> { { "contrast", contrast } }
                });
            }
        }

        return augmentedData;
    }

    private async Task<List<List<Annotation>>> MultiModelInferenceAsync(DicomInstance instance, SmartAnnotationConfig config, CancellationToken cancellationToken)
    {
        var results = new List<List<Annotation>>();

        foreach (var modelConfig in config.ModelConfigs)
        {
            try
            {
                var annotations = await _baseAnnotationService.GenerateAnnotationsAsync(instance, modelConfig, cancellationToken);
                results.Add(annotations);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "模型 {ModelPath} 推理失败", modelConfig.ModelPath);
            }
        }

        return results;
    }

    private async Task<List<Annotation>> FuseAnnotationsAsync(List<List<Annotation>> multiModelAnnotations, AnnotationFusionConfig config)
    {
        await Task.CompletedTask;

        var fusedAnnotations = new List<Annotation>();

        // 简化的标注融合实现
        foreach (var modelAnnotations in multiModelAnnotations)
        {
            fusedAnnotations.AddRange(modelAnnotations);
        }

        // 去重和合并重叠标注
        fusedAnnotations = await RemoveDuplicateAnnotationsAsync(fusedAnnotations, config.OverlapThreshold);

        return fusedAnnotations;
    }

    private async Task<List<Annotation>> PostProcessAnnotationsAsync(List<Annotation> annotations, DicomInstance instance, PostProcessingConfig config)
    {
        await Task.CompletedTask;

        var processedAnnotations = new List<Annotation>();

        foreach (var annotation in annotations)
        {
            // 边界框优化
            if (config.OptimizeBoundingBoxes)
            {
                // 这里可以实现边界框优化逻辑
            }

            // 置信度校准
            if (config.CalibrateConfidence)
            {
                annotation.Confidence = CalibrateConfidence(annotation.Confidence, config.CalibrationParameters);
            }

            processedAnnotations.Add(annotation);
        }

        return processedAnnotations;
    }

    private async Task<List<AnnotationQualityScore>> EvaluateAnnotationQualityAsync(List<Annotation> annotations, DicomInstance instance)
    {
        await Task.CompletedTask;

        var qualityScores = new List<AnnotationQualityScore>();

        foreach (var annotation in annotations)
        {
            var score = new AnnotationQualityScore
            {
                AnnotationId = annotation.Id,
                GeometricScore = EvaluateGeometricQuality(annotation),
                SemanticScore = EvaluateSemanticQuality(annotation),
                ConfidenceScore = annotation.Confidence,
                OverallScore = 0.0
            };

            score.OverallScore = (score.GeometricScore + score.SemanticScore + score.ConfidenceScore) / 3.0;
            qualityScores.Add(score);
        }

        return qualityScores;
    }

    private async Task<List<Annotation>> SmartFilterAnnotationsAsync(List<Annotation> annotations, List<AnnotationQualityScore> qualityScores, SmartFilterConfig config)
    {
        await Task.CompletedTask;

        var filteredAnnotations = new List<Annotation>();

        for (int i = 0; i < annotations.Count && i < qualityScores.Count; i++)
        {
            var annotation = annotations[i];
            var qualityScore = qualityScores[i];

            if (qualityScore.OverallScore >= config.MinQualityThreshold &&
                annotation.Confidence >= config.MinConfidenceThreshold)
            {
                filteredAnnotations.Add(annotation);
            }
        }

        return filteredAnnotations;
    }

    private double EvaluateGeometricQuality(Annotation annotation)
    {
        // 简化的几何质量评估
        var area = annotation.BoundingBox.Width * annotation.BoundingBox.Height;
        var aspectRatio = annotation.BoundingBox.Width / annotation.BoundingBox.Height;

        // 基于面积和宽高比的质量评估
        var areaScore = Math.Min(area * 10, 1.0); // 面积越大质量越高（到一定程度）
        var aspectScore = Math.Min(aspectRatio, 1.0 / aspectRatio) * 2; // 宽高比越接近1质量越高

        return (areaScore + aspectScore) / 2.0;
    }

    private double EvaluateSemanticQuality(Annotation annotation)
    {
        // 简化的语义质量评估
        // 基于标签的完整性和描述的详细程度
        var labelScore = string.IsNullOrEmpty(annotation.Label) ? 0.0 : 0.8;
        var descriptionScore = string.IsNullOrEmpty(annotation.Description) ? 0.0 : 0.2;

        return labelScore + descriptionScore;
    }

    /// <summary>
    /// 细化标注边缘
    /// </summary>
    private async Task<Annotation> RefineAnnotationEdgesAsync(Annotation annotation, DicomInstance instance)
    {
        await Task.CompletedTask;
        // 简化实现，实际应该使用图像处理算法
        return annotation;
    }

    /// <summary>
    /// 优化标注形状
    /// </summary>
    private async Task<Annotation> OptimizeAnnotationShapeAsync(Annotation annotation, DicomInstance instance)
    {
        await Task.CompletedTask;
        // 简化实现，实际应该使用形状优化算法
        return annotation;
    }

    /// <summary>
    /// 上下文调整标注
    /// </summary>
    private async Task<Annotation> AdjustAnnotationContextuallyAsync(Annotation annotation, DicomInstance instance)
    {
        await Task.CompletedTask;
        // 简化实现，实际应该考虑周围组织结构
        return annotation;
    }

    /// <summary>
    /// 移除重叠标注
    /// </summary>
    private List<Annotation> RemoveOverlappingAnnotations(List<Annotation> annotations)
    {
        var result = new List<Annotation>();

        foreach (var annotation in annotations.OrderByDescending(a => a.Confidence))
        {
            bool hasOverlap = false;
            foreach (var existing in result)
            {
                if (CalculateIoU(annotation.BoundingBox, existing.BoundingBox) > 0.5)
                {
                    hasOverlap = true;
                    break;
                }
            }

            if (!hasOverlap)
            {
                result.Add(annotation);
            }
        }

        return result;
    }

    /// <summary>
    /// 合并相邻标注
    /// </summary>
    private List<Annotation> MergeAdjacentAnnotations(List<Annotation> annotations)
    {
        // 简化实现，实际应该实现更复杂的合并逻辑
        return annotations;
    }

    /// <summary>
    /// 旋转标注
    /// </summary>
    private Annotation RotateAnnotation(Annotation annotation, double angle)
    {
        // 简化实现，实际应该实现坐标变换
        var rotated = new Annotation
        {
            Id = Guid.NewGuid(),
            Type = annotation.Type,
            Label = annotation.Label,
            Description = annotation.Description,
            Confidence = annotation.Confidence,
            BoundingBox = annotation.BoundingBox, // 简化：不改变边界框
            Source = annotation.Source,
            CreatedBy = annotation.CreatedBy,
            InstanceId = annotation.InstanceId
        };

        return rotated;
    }

    /// <summary>
    /// 缩放标注
    /// </summary>
    private Annotation ScaleAnnotation(Annotation annotation, double scale)
    {
        var scaled = new Annotation
        {
            Id = Guid.NewGuid(),
            Type = annotation.Type,
            Label = annotation.Label,
            Description = annotation.Description,
            Confidence = annotation.Confidence,
            BoundingBox = new BoundingBox
            {
                CenterX = annotation.BoundingBox.CenterX,
                CenterY = annotation.BoundingBox.CenterY,
                Width = annotation.BoundingBox.Width * scale,
                Height = annotation.BoundingBox.Height * scale
            },
            Source = annotation.Source,
            CreatedBy = annotation.CreatedBy,
            InstanceId = annotation.InstanceId
        };

        return scaled;
    }

    /// <summary>
    /// 计算IoU
    /// </summary>
    private double CalculateIoU(BoundingBox box1, BoundingBox box2)
    {
        var x1 = Math.Max(box1.Left, box2.Left);
        var y1 = Math.Max(box1.Top, box2.Top);
        var x2 = Math.Min(box1.Right, box2.Right);
        var y2 = Math.Min(box1.Bottom, box2.Bottom);

        if (x2 <= x1 || y2 <= y1) return 0.0;

        var intersection = (x2 - x1) * (y2 - y1);
        var area1 = box1.Width * box1.Height;
        var area2 = box2.Width * box2.Height;
        var union = area1 + area2 - intersection;

        return intersection / union;
    }

    private double CalibrateConfidence(double originalConfidence, Dictionary<string, object> calibrationParameters)
    {
        // 简化的置信度校准
        var alpha = calibrationParameters.GetValueOrDefault("alpha", 1.0);
        var beta = calibrationParameters.GetValueOrDefault("beta", 0.0);

        return Math.Max(0.0, Math.Min(1.0, (double)alpha * originalConfidence + (double)beta));
    }

    private async Task<List<Annotation>> RemoveDuplicateAnnotationsAsync(List<Annotation> annotations, double overlapThreshold)
    {
        await Task.CompletedTask;

        var uniqueAnnotations = new List<Annotation>();

        foreach (var annotation in annotations)
        {
            bool isDuplicate = false;

            foreach (var existing in uniqueAnnotations)
            {
                var overlap = CalculateOverlap(annotation.BoundingBox, existing.BoundingBox);
                if (overlap > overlapThreshold)
                {
                    isDuplicate = true;
                    // 保留置信度更高的标注
                    if (annotation.Confidence > existing.Confidence)
                    {
                        uniqueAnnotations.Remove(existing);
                        uniqueAnnotations.Add(annotation);
                    }
                    break;
                }
            }

            if (!isDuplicate)
            {
                uniqueAnnotations.Add(annotation);
            }
        }

        return uniqueAnnotations;
    }

    private double CalculateOverlap(BoundingBox box1, BoundingBox box2)
    {
        var x1 = Math.Max(box1.CenterX - box1.Width / 2, box2.CenterX - box2.Width / 2);
        var y1 = Math.Max(box1.CenterY - box1.Height / 2, box2.CenterY - box2.Height / 2);
        var x2 = Math.Min(box1.CenterX + box1.Width / 2, box2.CenterX + box2.Width / 2);
        var y2 = Math.Min(box1.CenterY + box1.Height / 2, box2.CenterY + box2.Height / 2);

        if (x2 <= x1 || y2 <= y1) return 0.0;

        var intersectionArea = (x2 - x1) * (y2 - y1);
        var unionArea = box1.Width * box1.Height + box2.Width * box2.Height - intersectionArea;

        return intersectionArea / unionArea;
    }

    // 添加缺失的方法实现
    private async Task<List<AnnotationQualityScore>> EvaluateGeometricQualityAsync(List<Annotation> annotations, DicomInstance instance)
    {
        await Task.CompletedTask;
        return annotations.Select(a => new AnnotationQualityScore
        {
            AnnotationId = a.Id,
            GeometricScore = EvaluateGeometricQuality(a),
            SemanticScore = 0.8,
            ConfidenceScore = a.Confidence,
            OverallScore = EvaluateGeometricQuality(a)
        }).ToList();
    }

    private async Task<List<AnnotationQualityScore>> EvaluateSemanticQualityAsync(List<Annotation> annotations, DicomInstance instance, QualityEvaluationConfig config)
    {
        await Task.CompletedTask;
        return annotations.Select(a => new AnnotationQualityScore
        {
            AnnotationId = a.Id,
            GeometricScore = 0.8,
            SemanticScore = EvaluateSemanticQuality(a),
            ConfidenceScore = a.Confidence,
            OverallScore = EvaluateSemanticQuality(a)
        }).ToList();
    }

    private async Task<List<AnnotationQualityScore>> EvaluateConsistencyAsync(List<Annotation> annotations)
    {
        await Task.CompletedTask;
        return annotations.Select(a => new AnnotationQualityScore
        {
            AnnotationId = a.Id,
            GeometricScore = 0.8,
            SemanticScore = 0.8,
            ConfidenceScore = a.Confidence,
            OverallScore = 0.8
        }).ToList();
    }

    private async Task<double> EvaluateCompletenessAsync(List<Annotation> annotations, DicomInstance instance, QualityEvaluationConfig config)
    {
        await Task.CompletedTask;
        return annotations.Count > 0 ? 0.8 : 0.0;
    }

    private double CalculateOverallQualityScore(AnnotationQualityReport report)
    {
        var scores = new List<double>();

        if (report.GeometricQualityScores.Any())
            scores.Add(report.GeometricQualityScores.Average(s => s.OverallScore));

        if (report.SemanticQualityScores.Any())
            scores.Add(report.SemanticQualityScores.Average(s => s.OverallScore));

        if (report.ConsistencyScores.Any())
            scores.Add(report.ConsistencyScores.Average(s => s.OverallScore));

        scores.Add(report.CompletenessScore);

        return scores.Any() ? scores.Average() : 0.0;
    }

    private async Task<List<string>> GenerateImprovementSuggestionsAsync(AnnotationQualityReport report, List<Annotation> annotations)
    {
        await Task.CompletedTask;
        var suggestions = new List<string>();

        if (report.OverallQualityScore < 0.7)
        {
            suggestions.Add("建议提高标注精度");
        }

        if (report.CompletenessScore < 0.8)
        {
            suggestions.Add("建议增加标注覆盖范围");
        }

        return suggestions;
    }

    private async Task<List<AnnotationRecommendation>> GenerateContextBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return new List<AnnotationRecommendation>();
    }

    private async Task<List<AnnotationRecommendation>> GeneratePatternBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return new List<AnnotationRecommendation>();
    }

    private async Task<List<AnnotationRecommendation>> GenerateAnatomyBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return new List<AnnotationRecommendation>();
    }

    private async Task<List<AnnotationRecommendation>> GenerateHistoryBasedRecommendationsAsync(DicomInstance instance, List<Annotation> existingAnnotations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return new List<AnnotationRecommendation>();
    }

    private async Task<List<AnnotationRecommendation>> RankAndFilterRecommendationsAsync(List<AnnotationRecommendation> recommendations, SmartRecommendationConfig config)
    {
        await Task.CompletedTask;
        return recommendations.OrderByDescending(r => r.Confidence).Take(config.MaxRecommendations).ToList();
    }

    private async Task<List<AugmentedAnnotationData>> ApplyGeometricTransformationsAsync(List<Annotation> annotations, DicomInstance instance, object config)
    {
        await Task.CompletedTask;
        return new List<AugmentedAnnotationData>();
    }

    private async Task<List<AugmentedAnnotationData>> ApplyColorAugmentationAsync(List<Annotation> annotations, DicomInstance instance, object config)
    {
        await Task.CompletedTask;
        return new List<AugmentedAnnotationData>();
    }

    private async Task<List<AugmentedAnnotationData>> ApplyNoiseAugmentationAsync(List<Annotation> annotations, DicomInstance instance, NoiseAugmentationConfig config)
    {
        await Task.CompletedTask;

        var augmentedData = new List<AugmentedAnnotationData>();

        foreach (var annotation in annotations)
        {
            // 高斯噪声
            if (config.EnableGaussianNoise)
            {
                for (int i = 0; i < config.GaussianNoiseSamples; i++)
                {
                    augmentedData.Add(new AugmentedAnnotationData
                    {
                        OriginalAnnotation = annotation,
                        AugmentedAnnotation = annotation, // 噪声增强不改变标注本身
                        AugmentationType = "GaussianNoise",
                        Parameters = new Dictionary<string, object> { { "std", config.GaussianNoiseStd } }
                    });
                }
            }
        }

        return augmentedData;
    }

    private async Task<List<AugmentedAnnotationData>> GenerateSyntheticAnnotationsAsync(List<Annotation> annotations, DicomInstance instance, SyntheticGenerationConfig config)
    {
        await Task.CompletedTask;

        var augmentedData = new List<AugmentedAnnotationData>();

        foreach (var annotation in annotations)
        {
            // 模板匹配生成
            if (config.EnableTemplateMatching)
            {
                for (int i = 0; i < config.SyntheticSamplesPerOriginal; i++)
                {
                    var syntheticAnnotation = new Annotation
                    {
                        Id = Guid.NewGuid(),
                        Type = annotation.Type,
                        Label = annotation.Label,
                        Description = annotation.Description + " (合成)",
                        Confidence = annotation.Confidence * 0.9, // 略微降低置信度
                        BoundingBox = annotation.BoundingBox,
                        Source = AnnotationSource.AI,
                        CreatedBy = "Synthetic Generator",
                        InstanceId = annotation.InstanceId
                    };

                    augmentedData.Add(new AugmentedAnnotationData
                    {
                        OriginalAnnotation = annotation,
                        AugmentedAnnotation = syntheticAnnotation,
                        AugmentationType = "Synthetic",
                        Parameters = new Dictionary<string, object> { { "method", "template_matching" } }
                    });
                }
            }
        }

        return augmentedData;
    }

    #endregion
}

/// <summary>
/// 增强标注数据
/// </summary>
public class AugmentedAnnotationData
{
    public Annotation OriginalAnnotation { get; set; } = null!;
    public Annotation AugmentedAnnotation { get; set; } = null!;
    public string AugmentationType { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
