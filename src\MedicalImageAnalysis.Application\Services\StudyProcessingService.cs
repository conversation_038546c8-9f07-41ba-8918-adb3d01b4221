using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Application.Services;

/// <summary>
/// 研究处理服务，协调 DICOM 解析、图像处理和 AI 分析的完整流程
/// </summary>
public class StudyProcessingService
{
    private readonly IDicomService _dicomService;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IYoloService _yoloService;
    private readonly IAnnotationService _annotationService;
    private readonly ILogger<StudyProcessingService> _logger;

    /// <summary>
    /// 初始化研究处理服务
    /// </summary>
    /// <param name="dicomService">DICOM服务</param>
    /// <param name="imageProcessingService">图像处理服务</param>
    /// <param name="yoloService">YOLO服务</param>
    /// <param name="annotationService">标注服务</param>
    /// <param name="logger">日志记录器</param>
    public StudyProcessingService(
        IDicomService dicomService,
        IImageProcessingService imageProcessingService,
        IYoloService yoloService,
        IAnnotationService annotationService,
        ILogger<StudyProcessingService> logger)
    {
        _dicomService = dicomService;
        _imageProcessingService = imageProcessingService;
        _yoloService = yoloService;
        _annotationService = annotationService;
        _logger = logger;
    }

    /// <summary>
    /// 处理完整的研究流程
    /// </summary>
    /// <param name="filePaths">DICOM 文件路径集合</param>
    /// <param name="processingConfig">处理配置</param>
    /// <param name="progressCallback">进度回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    public async Task<StudyProcessingResult> ProcessStudyAsync(
        IEnumerable<string> filePaths,
        StudyProcessingConfig processingConfig,
        IProgress<StudyProcessingProgress>? progressCallback = null,
        CancellationToken cancellationToken = default)
    {
        var result = new StudyProcessingResult();
        var progress = new StudyProcessingProgress();

        try
        {
            _logger.LogInformation("开始处理研究，文件数量: {Count}", filePaths.Count());

            // 步骤 1: 解析 DICOM 文件
            progress.CurrentStep = ProcessingStep.DicomParsing;
            progress.StepProgress = 0;
            progressCallback?.Report(progress);

            var study = await _dicomService.ParseDicomFilesAsync(filePaths, cancellationToken);
            result.Study = study;

            progress.StepProgress = 100;
            progressCallback?.Report(progress);

            // 步骤 2: 图像预处理
            if (processingConfig.EnableImagePreprocessing)
            {
                progress.CurrentStep = ProcessingStep.ImagePreprocessing;
                progress.StepProgress = 0;
                progressCallback?.Report(progress);

                await PreprocessImagesAsync(study, processingConfig.PreprocessingOptions, progress, progressCallback, cancellationToken);
            }

            // 步骤 3: AI 检测和标注
            if (processingConfig.EnableAIDetection && !string.IsNullOrEmpty(processingConfig.ModelPath))
            {
                progress.CurrentStep = ProcessingStep.AIDetection;
                progress.StepProgress = 0;
                progressCallback?.Report(progress);

                await PerformAIDetectionAsync(study, processingConfig, progress, progressCallback, cancellationToken);
            }

            // 步骤 4: 标注验证和优化
            if (processingConfig.EnableAnnotationValidation)
            {
                progress.CurrentStep = ProcessingStep.AnnotationValidation;
                progress.StepProgress = 0;
                progressCallback?.Report(progress);

                await ValidateAndOptimizeAnnotationsAsync(study, processingConfig.ValidationRules, progress, progressCallback, cancellationToken);
            }

            // 步骤 5: 生成统计信息
            progress.CurrentStep = ProcessingStep.StatisticsGeneration;
            progress.StepProgress = 0;
            progressCallback?.Report(progress);

            result.Statistics = await _dicomService.CalculateStudyStatisticsAsync(study);
            result.AnnotationStatistics = await GenerateAnnotationStatisticsAsync(study);

            progress.StepProgress = 100;
            progressCallback?.Report(progress);

            // 步骤 6: 导出结果
            if (processingConfig.EnableDatasetExport && !string.IsNullOrEmpty(processingConfig.ExportPath))
            {
                progress.CurrentStep = ProcessingStep.DatasetExport;
                progress.StepProgress = 0;
                progressCallback?.Report(progress);

                result.ExportResult = await _annotationService.ExportTrainingDatasetAsync(
                    study, 
                    processingConfig.ExportConfig, 
                    processingConfig.ExportPath,
                    new Progress<ExportProgress>(ep => 
                    {
                        progress.StepProgress = (int)ep.ProgressPercentage;
                        progressCallback?.Report(progress);
                    }),
                    cancellationToken);
            }

            result.Success = true;
            result.ProcessingTimeMs = (long)(DateTime.UtcNow - progress.StartTime).TotalMilliseconds;

            progress.CurrentStep = ProcessingStep.Completed;
            progress.StepProgress = 100;
            progressCallback?.Report(progress);

            _logger.LogInformation("研究处理完成，耗时: {Time}ms", result.ProcessingTimeMs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "研究处理失败");
            result.Success = false;
            result.ErrorMessage = ex.Message;
            
            progress.CurrentStep = ProcessingStep.Failed;
            progress.ErrorMessage = ex.Message;
            progressCallback?.Report(progress);
        }

        return result;
    }

    /// <summary>
    /// 预处理图像
    /// </summary>
    private async Task PreprocessImagesAsync(
        DicomStudy study,
        ImagePreprocessingOptions? options,
        StudyProcessingProgress progress,
        IProgress<StudyProcessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        if (options == null) return;

        var allInstances = study.Series.SelectMany(s => s.Instances).ToList();
        var processedCount = 0;

        foreach (var instance in allInstances)
        {
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                var pixelData = await _dicomService.GetPixelDataAsync(instance, true, cancellationToken);
                var processedData = await _imageProcessingService.PreprocessImageAsync(pixelData, options, cancellationToken);
                
                // 这里可以保存预处理后的数据或更新实例信息
                
                processedCount++;
                progress.StepProgress = (int)((double)processedCount / allInstances.Count * 100);
                progressCallback?.Report(progress);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "预处理实例失败: {InstanceUid}", instance.SopInstanceUid);
            }
        }
    }

    /// <summary>
    /// 执行 AI 检测
    /// </summary>
    private async Task PerformAIDetectionAsync(
        DicomStudy study,
        StudyProcessingConfig config,
        StudyProcessingProgress progress,
        IProgress<StudyProcessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        var allInstances = study.Series.SelectMany(s => s.Instances).ToList();
        var processedCount = 0;

        foreach (var instance in allInstances)
        {
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                // 获取像素数据并转换为图像格式
                var pixelData = await _dicomService.GetPixelDataAsync(instance, true, cancellationToken);
                var imageData = await _imageProcessingService.ConvertImageFormatAsync(pixelData, ImageFormat.Png, 95, cancellationToken);

                // 执行 YOLO 推理
                var detections = await _yoloService.InferAsync(config.ModelPath!, imageData, config.InferenceConfig, cancellationToken);

                // 转换检测结果为标注
                foreach (var detection in detections)
                {
                    var annotation = new Annotation
                    {
                        Type = AnnotationType.BoundingBox,
                        Label = detection.Label,
                        Confidence = detection.Confidence,
                        BoundingBox = new BoundingBox
                        {
                            CenterX = detection.BoundingBox.CenterX,
                            CenterY = detection.BoundingBox.CenterY,
                            Width = detection.BoundingBox.Width,
                            Height = detection.BoundingBox.Height
                        },
                        Source = AnnotationSource.AI,
                        InstanceId = instance.Id
                    };

                    instance.Annotations.Add(annotation);
                }

                processedCount++;
                progress.StepProgress = (int)((double)processedCount / allInstances.Count * 100);
                progressCallback?.Report(progress);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "AI 检测失败: {InstanceUid}", instance.SopInstanceUid);
            }
        }
    }

    /// <summary>
    /// 验证和优化标注
    /// </summary>
    private async Task ValidateAndOptimizeAnnotationsAsync(
        DicomStudy study,
        AnnotationValidationRules? validationRules,
        StudyProcessingProgress progress,
        IProgress<StudyProcessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        if (validationRules == null) return;

        var allAnnotations = study.Series.SelectMany(s => s.Instances).SelectMany(i => i.Annotations).ToList();
        
        if (!allAnnotations.Any())
        {
            progress.StepProgress = 100;
            progressCallback?.Report(progress);
            return;
        }

        // 验证标注
        var validationResult = await _annotationService.ValidateAnnotationsAsync(allAnnotations, validationRules);
        
        // 移除无效标注
        foreach (var error in validationResult.Errors)
        {
            var annotation = allAnnotations.FirstOrDefault(a => a.Id == error.AnnotationId);
            if (annotation != null)
            {
                var instance = study.Series.SelectMany(s => s.Instances).FirstOrDefault(i => i.Id == annotation.InstanceId);
                instance?.Annotations.Remove(annotation);
            }
        }

        progress.StepProgress = 100;
        progressCallback?.Report(progress);
    }

    /// <summary>
    /// 生成标注统计信息
    /// </summary>
    private async Task<AnnotationStatistics> GenerateAnnotationStatisticsAsync(DicomStudy study)
    {
        var allAnnotations = study.Series.SelectMany(s => s.Instances).SelectMany(i => i.Annotations).ToList();
        return await _annotationService.GenerateAnnotationStatisticsAsync(allAnnotations);
    }
}

/// <summary>
/// 研究处理配置
/// </summary>
public class StudyProcessingConfig
{
    /// <summary>
    /// 是否启用图像预处理
    /// </summary>
    public bool EnableImagePreprocessing { get; set; } = true;

    /// <summary>
    /// 预处理选项
    /// </summary>
    public ImagePreprocessingOptions? PreprocessingOptions { get; set; }

    /// <summary>
    /// 是否启用 AI 检测
    /// </summary>
    public bool EnableAIDetection { get; set; } = true;

    /// <summary>
    /// 模型路径
    /// </summary>
    public string? ModelPath { get; set; }

    /// <summary>
    /// 推理配置
    /// </summary>
    public YoloInferenceConfig InferenceConfig { get; set; } = new();

    /// <summary>
    /// 是否启用标注验证
    /// </summary>
    public bool EnableAnnotationValidation { get; set; } = true;

    /// <summary>
    /// 验证规则
    /// </summary>
    public AnnotationValidationRules? ValidationRules { get; set; }

    /// <summary>
    /// 是否启用数据集导出
    /// </summary>
    public bool EnableDatasetExport { get; set; } = false;

    /// <summary>
    /// 导出路径
    /// </summary>
    public string? ExportPath { get; set; }

    /// <summary>
    /// 导出配置
    /// </summary>
    public DatasetExportConfig ExportConfig { get; set; } = new();
}

/// <summary>
/// 研究处理进度
/// </summary>
public class StudyProcessingProgress
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 当前步骤
    /// </summary>
    public ProcessingStep CurrentStep { get; set; } = ProcessingStep.DicomParsing;

    /// <summary>
    /// 当前步骤进度 (0-100)
    /// </summary>
    public int StepProgress { get; set; } = 0;

    /// <summary>
    /// 总体进度 (0-100)
    /// </summary>
    public int OverallProgress
    {
        get
        {
            var stepWeight = 100.0 / Enum.GetValues<ProcessingStep>().Length;
            var completedSteps = (int)CurrentStep;
            return (int)(completedSteps * stepWeight + StepProgress * stepWeight / 100);
        }
    }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 研究处理结果
/// </summary>
public class StudyProcessingResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理的研究
    /// </summary>
    public DicomStudy? Study { get; set; }

    /// <summary>
    /// 研究统计信息
    /// </summary>
    public StudyStatistics? Statistics { get; set; }

    /// <summary>
    /// 标注统计信息
    /// </summary>
    public AnnotationStatistics? AnnotationStatistics { get; set; }

    /// <summary>
    /// 导出结果
    /// </summary>
    public DatasetExportResult? ExportResult { get; set; }

    /// <summary>
    /// 处理耗时 (毫秒)
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// 处理步骤枚举
/// </summary>
public enum ProcessingStep
{
    /// <summary>
    /// DICOM 解析
    /// </summary>
    DicomParsing = 1,

    /// <summary>
    /// 图像预处理
    /// </summary>
    ImagePreprocessing = 2,

    /// <summary>
    /// AI 检测
    /// </summary>
    AIDetection = 3,

    /// <summary>
    /// 标注验证
    /// </summary>
    AnnotationValidation = 4,

    /// <summary>
    /// 统计信息生成
    /// </summary>
    StatisticsGeneration = 5,

    /// <summary>
    /// 数据集导出
    /// </summary>
    DatasetExport = 6,

    /// <summary>
    /// 完成
    /// </summary>
    Completed = 7,

    /// <summary>
    /// 失败
    /// </summary>
    Failed = 8
}
