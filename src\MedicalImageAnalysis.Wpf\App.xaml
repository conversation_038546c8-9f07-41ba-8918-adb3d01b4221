﻿<Application x:Class="MedicalImageAnalysis.Wpf.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:MedicalImageAnalysis.Wpf"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             ShutdownMode="OnMainWindowClose">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 暂时注释掉MaterialDesign主题，使用自定义样式 -->
                <!-- <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="LightBlue" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" /> -->
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- 自定义转换器 -->
            <local:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
            <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>

            <!-- 自定义样式 -->

            <!-- 自定义样式 -->
            <Style x:Key="PageTitleStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="28"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Margin" Value="0,0,0,24"/>
                <Setter Property="Foreground" Value="Black"/>
            </Style>

            <!-- 导航按钮样式 -->
            <Style x:Key="NavigationButtonStyle" TargetType="Button">
                <Setter Property="Margin" Value="4"/>
                <Setter Property="Padding" Value="12,8"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="Background" Value="LightBlue"/>
                <Setter Property="BorderBrush" Value="Blue"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="Blue"/>
                        <Setter Property="Foreground" Value="White"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- 标题文本样式 -->
            <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="20"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Margin" Value="0,0,0,16"/>
            </Style>

            <!-- MaterialDesign 样式替代 -->
            <Style x:Key="MaterialDesignRaisedButton" TargetType="Button">
                <Setter Property="Background" Value="DodgerBlue"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="RoyalBlue"/>
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="MediumBlue"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="MaterialDesignOutlinedButton" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="DodgerBlue"/>
                <Setter Property="BorderBrush" Value="DodgerBlue"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="LightBlue"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="MaterialDesignIconButton" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="8"/>
                <Setter Property="Margin" Value="2"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="LightGray"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="MaterialDesignFlatButton" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="12,8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="LightGray"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- 添加缺失的MaterialDesign样式 -->
            <Style x:Key="MaterialDesignLinearProgressBar" TargetType="ProgressBar">
                <Setter Property="Height" Value="4"/>
                <Setter Property="Background" Value="LightGray"/>
                <Setter Property="Foreground" Value="DodgerBlue"/>
                <Setter Property="BorderThickness" Value="0"/>
            </Style>

            <Style x:Key="MaterialDesignFlatToggleButton" TargetType="ToggleButton">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="12,8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="LightGray"/>
                    </Trigger>
                    <Trigger Property="IsChecked" Value="True">
                        <Setter Property="Background" Value="DodgerBlue"/>
                        <Setter Property="Foreground" Value="White"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="MaterialDesignTextBox" TargetType="TextBox">
                <Setter Property="Padding" Value="8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="BorderBrush" Value="Gray"/>
                <Setter Property="BorderThickness" Value="1"/>
            </Style>

            <Style x:Key="MaterialDesignComboBox" TargetType="ComboBox">
                <Setter Property="Padding" Value="8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="BorderBrush" Value="Gray"/>
                <Setter Property="BorderThickness" Value="1"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
