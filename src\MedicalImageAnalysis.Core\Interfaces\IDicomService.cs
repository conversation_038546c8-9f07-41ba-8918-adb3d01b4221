using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// DICOM 服务接口，提供 DICOM 文件处理的核心功能
/// </summary>
public interface IDicomService
{
    /// <summary>
    /// 解析 DICOM 文件并创建研究
    /// </summary>
    /// <param name="filePaths">DICOM 文件路径集合</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析后的研究实体</returns>
    Task<DicomStudy> ParseDicomFilesAsync(IEnumerable<string> filePaths, CancellationToken cancellationToken = default);

    /// <summary>
    /// 解析单个 DICOM 文件
    /// </summary>
    /// <param name="filePath">DICOM 文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析后的实例实体</returns>
    Task<DicomInstance> ParseDicomFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证 DICOM 文件的有效性
    /// </summary>
    /// <param name="filePath">DICOM 文件路径</param>
    /// <returns>验证结果</returns>
    Task<DicomValidationResult> ValidateDicomFileAsync(string filePath);

    /// <summary>
    /// 提取 DICOM 文件的元数据
    /// </summary>
    /// <param name="filePath">DICOM 文件路径</param>
    /// <returns>元数据字典</returns>
    Task<Dictionary<string, object>> ExtractMetadataAsync(string filePath);

    /// <summary>
    /// 获取 DICOM 实例的像素数据
    /// </summary>
    /// <param name="instance">DICOM 实例</param>
    /// <param name="applyModalityLut">是否应用模态 LUT</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>像素数据数组</returns>
    Task<PixelData> GetPixelDataAsync(DicomInstance instance, bool applyModalityLut = true, CancellationToken cancellationToken = default);

    /// <summary>
    /// 将像素数据转换为 Hounsfield 单位
    /// </summary>
    /// <param name="pixelData">原始像素数据</param>
    /// <param name="instance">DICOM 实例</param>
    /// <returns>HU 值数组</returns>
    Task<double[]> ConvertToHounsfieldUnitsAsync(PixelData pixelData, DicomInstance instance);

    /// <summary>
    /// 应用窗宽窗位调整
    /// </summary>
    /// <param name="pixelData">像素数据</param>
    /// <param name="windowWidth">窗宽</param>
    /// <param name="windowCenter">窗位</param>
    /// <returns>调整后的像素数据</returns>
    Task<byte[]> ApplyWindowLevelAsync(double[] pixelData, double windowWidth, double windowCenter);

    /// <summary>
    /// 检测影像方向
    /// </summary>
    /// <param name="instances">实例集合</param>
    /// <returns>检测到的影像方向</returns>
    Task<ImageOrientation> DetectImageOrientationAsync(IEnumerable<DicomInstance> instances);

    /// <summary>
    /// 排序 DICOM 实例
    /// </summary>
    /// <param name="instances">实例集合</param>
    /// <returns>排序后的实例集合</returns>
    Task<IEnumerable<DicomInstance>> SortInstancesAsync(IEnumerable<DicomInstance> instances);

    /// <summary>
    /// 计算研究的统计信息
    /// </summary>
    /// <param name="study">研究实体</param>
    /// <returns>统计信息</returns>
    Task<StudyStatistics> CalculateStudyStatisticsAsync(DicomStudy study);
}

/// <summary>
/// DICOM 验证结果
/// </summary>
public class DicomValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息集合
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息集合
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 是否包含像素数据
    /// </summary>
    public bool HasPixelData { get; set; }

    /// <summary>
    /// SOP 类 UID
    /// </summary>
    public string SopClassUid { get; set; } = string.Empty;

    /// <summary>
    /// 传输语法 UID
    /// </summary>
    public string TransferSyntaxUid { get; set; } = string.Empty;
}

/// <summary>
/// 像素数据
/// </summary>
public class PixelData
{
    /// <summary>
    /// 像素数据数组
    /// </summary>
    public Array Data { get; set; } = null!;

    /// <summary>
    /// 数据类型
    /// </summary>
    public Type DataType { get; set; } = typeof(ushort);

    /// <summary>
    /// 图像宽度
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// 图像高度
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// 每像素位数
    /// </summary>
    public int BitsPerPixel { get; set; }

    /// <summary>
    /// 是否为有符号数据
    /// </summary>
    public bool IsSigned { get; set; }

    /// <summary>
    /// 光度解释
    /// </summary>
    public string PhotometricInterpretation { get; set; } = string.Empty;

    /// <summary>
    /// 获取指定位置的像素值
    /// </summary>
    public T GetPixel<T>(int x, int y) where T : struct
    {
        if (x < 0 || x >= Width || y < 0 || y >= Height)
            throw new ArgumentOutOfRangeException("坐标超出图像范围");

        var index = y * Width + x;
        return (T)Data.GetValue(index)!;
    }

    /// <summary>
    /// 设置指定位置的像素值
    /// </summary>
    public void SetPixel<T>(int x, int y, T value) where T : struct
    {
        if (x < 0 || x >= Width || y < 0 || y >= Height)
            throw new ArgumentOutOfRangeException("坐标超出图像范围");

        var index = y * Width + x;
        Data.SetValue(value, index);
    }

    /// <summary>
    /// 转换为指定类型的数组
    /// </summary>
    public T[] ToArray<T>() where T : struct
    {
        var result = new T[Width * Height];
        for (int i = 0; i < result.Length; i++)
        {
            result[i] = (T)Data.GetValue(i)!;
        }
        return result;
    }
}

/// <summary>
/// 研究统计信息
/// </summary>
public class StudyStatistics
{
    /// <summary>
    /// 总序列数
    /// </summary>
    public int TotalSeries { get; set; }

    /// <summary>
    /// 总实例数
    /// </summary>
    public int TotalInstances { get; set; }

    /// <summary>
    /// 总文件大小 (字节)
    /// </summary>
    public long TotalFileSize { get; set; }

    /// <summary>
    /// 平均切片厚度
    /// </summary>
    public double AverageSliceThickness { get; set; }

    /// <summary>
    /// 像素间距范围
    /// </summary>
    public (double MinX, double MaxX, double MinY, double MaxY) PixelSpacingRange { get; set; }

    /// <summary>
    /// 图像尺寸范围
    /// </summary>
    public (int MinWidth, int MaxWidth, int MinHeight, int MaxHeight) ImageSizeRange { get; set; }

    /// <summary>
    /// 模态类型集合
    /// </summary>
    public HashSet<string> Modalities { get; set; } = new();

    /// <summary>
    /// 检查部位集合
    /// </summary>
    public HashSet<string> BodyParts { get; set; } = new();

    /// <summary>
    /// 影像方向分布
    /// </summary>
    public Dictionary<ImageOrientation, int> OrientationDistribution { get; set; } = new();
}
