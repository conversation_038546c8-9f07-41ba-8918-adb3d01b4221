// 医学影像解析系统 - 主应用 JavaScript

window.medicalImageApp = {
    // 应用初始化
    init: function() {
        console.log('医学影像解析系统已加载');
        this.setupGlobalErrorHandler();
        this.setupTooltips();
        this.setupProgressBars();
    },

    // 设置全局错误处理
    setupGlobalErrorHandler: function() {
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的 Promise 拒绝:', event.reason);
        });
    },

    // 设置工具提示
    setupTooltips: function() {
        // 初始化 Bootstrap 工具提示
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },

    // 设置进度条动画
    setupProgressBars: function() {
        // 为进度条添加动画效果
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });
    },

    // 显示加载状态
    showLoading: function(element, text = '加载中...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.innerHTML = `
                <div class="d-flex justify-content-center align-items-center py-3">
                    <div class="spinner-border text-primary me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>${text}</span>
                </div>
            `;
        }
    },

    // 隐藏加载状态
    hideLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.innerHTML = '';
        }
    },

    // 显示成功消息
    showSuccess: function(message, duration = 3000) {
        this.showToast(message, 'success', duration);
    },

    // 显示错误消息
    showError: function(message, duration = 5000) {
        this.showToast(message, 'danger', duration);
    },

    // 显示警告消息
    showWarning: function(message, duration = 4000) {
        this.showToast(message, 'warning', duration);
    },

    // 显示信息消息
    showInfo: function(message, duration = 3000) {
        this.showToast(message, 'info', duration);
    },

    // 显示 Toast 消息
    showToast: function(message, type = 'info', duration = 3000) {
        // 创建 toast 容器（如果不存在）
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        // 创建 toast 元素
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // 显示 toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: duration
        });
        
        toast.show();

        // 清理已隐藏的 toast
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    },

    // 确认对话框
    confirm: function(message, title = '确认') {
        return new Promise((resolve) => {
            // 创建模态对话框
            const modalId = 'confirm-modal-' + Date.now();
            const modalHtml = `
                <div class="modal fade" id="${modalId}" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${message}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary confirm-btn">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            const modalElement = document.getElementById(modalId);
            const modal = new bootstrap.Modal(modalElement);

            // 绑定事件
            modalElement.querySelector('.confirm-btn').addEventListener('click', function() {
                modal.hide();
                resolve(true);
            });

            modalElement.addEventListener('hidden.bs.modal', function() {
                modalElement.remove();
                resolve(false);
            });

            modal.show();
        });
    },

    // 格式化文件大小
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 格式化日期时间
    formatDateTime: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },

    // 复制文本到剪贴板
    copyToClipboard: async function(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showSuccess('已复制到剪贴板');
            return true;
        } catch (err) {
            console.error('复制失败:', err);
            this.showError('复制失败');
            return false;
        }
    },

    // 下载文件
    downloadFile: function(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    },

    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.medicalImageApp.init();
});

// 为 Blazor 提供的全局函数
window.blazorHelpers = {
    showSuccess: (message) => window.medicalImageApp.showSuccess(message),
    showError: (message) => window.medicalImageApp.showError(message),
    showWarning: (message) => window.medicalImageApp.showWarning(message),
    showInfo: (message) => window.medicalImageApp.showInfo(message),
    confirm: (message, title) => window.medicalImageApp.confirm(message, title),
    copyToClipboard: (text) => window.medicalImageApp.copyToClipboard(text),
    formatFileSize: (bytes) => window.medicalImageApp.formatFileSize(bytes),
    formatDateTime: (dateString) => window.medicalImageApp.formatDateTime(dateString)
};
