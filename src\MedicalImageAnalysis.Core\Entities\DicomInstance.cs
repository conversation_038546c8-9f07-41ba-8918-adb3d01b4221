using System.ComponentModel.DataAnnotations;

namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// DICOM 实例实体，表示单个影像切片
/// </summary>
public class DicomInstance
{
    /// <summary>
    /// 实例唯一标识符
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// DICOM SOP 实例 UID
    /// </summary>
    [Required]
    [StringLength(64)]
    public string SopInstanceUid { get; set; } = string.Empty;

    /// <summary>
    /// SOP 类 UID
    /// </summary>
    [StringLength(64)]
    public string SopClassUid { get; set; } = string.Empty;

    /// <summary>
    /// 传输语法 UID
    /// </summary>
    [StringLength(64)]
    public string TransferSyntaxUid { get; set; } = string.Empty;

    /// <summary>
    /// 实例号
    /// </summary>
    public int InstanceNumber { get; set; }

    /// <summary>
    /// 图像行数
    /// </summary>
    public int Rows { get; set; }

    /// <summary>
    /// 图像列数
    /// </summary>
    public int Columns { get; set; }

    /// <summary>
    /// 像素间距 (X, Y)
    /// </summary>
    public (double X, double Y) PixelSpacing { get; set; } = (1.0, 1.0);

    /// <summary>
    /// 切片厚度
    /// </summary>
    public double SliceThickness { get; set; } = 1.0;

    /// <summary>
    /// 切片位置
    /// </summary>
    public double SliceLocation { get; set; }

    /// <summary>
    /// 图像位置 (X, Y, Z)
    /// </summary>
    public (double X, double Y, double Z) ImagePosition { get; set; }

    /// <summary>
    /// 图像方向余弦
    /// </summary>
    public double[] ImageOrientationPatient { get; set; } = new double[6];

    /// <summary>
    /// 窗宽
    /// </summary>
    public double WindowWidth { get; set; } = 400;

    /// <summary>
    /// 窗位
    /// </summary>
    public double WindowCenter { get; set; } = 40;

    /// <summary>
    /// 重缩放斜率
    /// </summary>
    public double RescaleSlope { get; set; } = 1.0;

    /// <summary>
    /// 重缩放截距
    /// </summary>
    public double RescaleIntercept { get; set; } = 0.0;

    /// <summary>
    /// 像素数据类型
    /// </summary>
    public PixelDataType PixelDataType { get; set; } = PixelDataType.Unknown;

    /// <summary>
    /// 每像素位数
    /// </summary>
    public int BitsAllocated { get; set; } = 16;

    /// <summary>
    /// 存储位数
    /// </summary>
    public int BitsStored { get; set; } = 16;

    /// <summary>
    /// 高位
    /// </summary>
    public int HighBit { get; set; } = 15;

    /// <summary>
    /// 像素表示 (0=无符号, 1=有符号)
    /// </summary>
    public int PixelRepresentation { get; set; } = 0;

    /// <summary>
    /// 光度解释
    /// </summary>
    [StringLength(32)]
    public string PhotometricInterpretation { get; set; } = "MONOCHROME2";

    /// <summary>
    /// 原始文件路径
    /// </summary>
    [StringLength(512)]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小 (字节)
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件哈希值 (用于去重)
    /// </summary>
    [StringLength(64)]
    public string FileHash { get; set; } = string.Empty;

    /// <summary>
    /// 所属序列ID
    /// </summary>
    public Guid SeriesId { get; set; }

    /// <summary>
    /// 所属序列
    /// </summary>
    public DicomSeries Series { get; set; } = null!;

    /// <summary>
    /// 标注集合
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 获取像素数据的字节大小
    /// </summary>
    public int PixelDataSize => Rows * Columns * (BitsAllocated / 8);

    /// <summary>
    /// 检查是否为有符号像素数据
    /// </summary>
    public bool IsSignedPixelData => PixelRepresentation == 1;

    /// <summary>
    /// 获取 Hounsfield 单位转换参数
    /// </summary>
    public (double Slope, double Intercept) HounsfieldConversion => (RescaleSlope, RescaleIntercept);

    /// <summary>
    /// 计算实际的像素值范围
    /// </summary>
    public (double Min, double Max) GetPixelValueRange()
    {
        var maxStoredValue = Math.Pow(2, BitsStored) - 1;
        
        if (IsSignedPixelData)
        {
            var halfRange = maxStoredValue / 2;
            return (-halfRange * RescaleSlope + RescaleIntercept, 
                    halfRange * RescaleSlope + RescaleIntercept);
        }
        else
        {
            return (RescaleIntercept, 
                    maxStoredValue * RescaleSlope + RescaleIntercept);
        }
    }

    /// <summary>
    /// 获取推荐的窗宽窗位设置
    /// </summary>
    public (double Width, double Center) GetRecommendedWindow()
    {
        // 根据不同的检查部位和模态返回推荐的窗宽窗位
        var bodyPart = Series?.Study?.BodyPart?.ToUpperInvariant() ?? "";
        var modality = Series?.Modality?.ToUpperInvariant() ?? "";

        return (bodyPart, modality) switch
        {
            (var part, "CT") when part.Contains("BRAIN") || part.Contains("HEAD") => (80, 40),
            (var part, "CT") when part.Contains("LUNG") || part.Contains("CHEST") => (1500, -600),
            (var part, "CT") when part.Contains("ABDOMEN") => (400, 50),
            (var part, "CT") when part.Contains("BONE") => (2000, 300),
            _ => (WindowWidth, WindowCenter)
        };
    }
}
