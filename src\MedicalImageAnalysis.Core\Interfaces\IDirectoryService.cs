using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 目录管理服务接口
/// </summary>
public interface IDirectoryService
{
    /// <summary>
    /// 获取系统目录信息
    /// </summary>
    /// <returns>系统目录信息</returns>
    Task<SystemDirectories> GetSystemDirectoriesAsync();

    /// <summary>
    /// 打开指定目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>是否成功打开</returns>
    Task<bool> OpenDirectoryAsync(string directoryPath);

    /// <summary>
    /// 获取目录内容
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="includeSubdirectories">是否包含子目录</param>
    /// <returns>目录内容</returns>
    Task<DirectoryContent> GetDirectoryContentAsync(string directoryPath, bool includeSubdirectories = false);

    /// <summary>
    /// 创建目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>是否成功创建</returns>
    Task<bool> CreateDirectoryAsync(string directoryPath);

    /// <summary>
    /// 删除目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="recursive">是否递归删除</param>
    /// <returns>是否成功删除</returns>
    Task<bool> DeleteDirectoryAsync(string directoryPath, bool recursive = false);

    /// <summary>
    /// 获取目录大小
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>目录大小（字节）</returns>
    Task<long> GetDirectorySizeAsync(string directoryPath);

    /// <summary>
    /// 清理临时文件
    /// </summary>
    /// <param name="olderThanDays">清理多少天前的文件</param>
    /// <returns>清理的文件数量</returns>
    Task<int> CleanupTempFilesAsync(int olderThanDays = 7);

    /// <summary>
    /// 获取磁盘使用情况
    /// </summary>
    /// <returns>磁盘使用情况</returns>
    Task<DiskUsage> GetDiskUsageAsync();
}

/// <summary>
/// 系统目录信息
/// </summary>
public class SystemDirectories
{
    /// <summary>
    /// 数据目录
    /// </summary>
    public string DataDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 日志目录
    /// </summary>
    public string LogsDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 临时文件目录
    /// </summary>
    public string TempDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 模型目录
    /// </summary>
    public string ModelsDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 示例数据目录
    /// </summary>
    public string SampleDataDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 配置文件目录
    /// </summary>
    public string ConfigDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 备份目录
    /// </summary>
    public string BackupDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 缓存目录
    /// </summary>
    public string CacheDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 脚本目录
    /// </summary>
    public string ScriptsDirectory { get; set; } = string.Empty;
}

/// <summary>
/// 目录内容
/// </summary>
public class DirectoryContent
{
    /// <summary>
    /// 目录路径
    /// </summary>
    public string DirectoryPath { get; set; } = string.Empty;

    /// <summary>
    /// 文件列表
    /// </summary>
    public List<FileInfo> Files { get; set; } = new();

    /// <summary>
    /// 子目录列表
    /// </summary>
    public List<DirectoryInfo> Subdirectories { get; set; } = new();

    /// <summary>
    /// 总文件数
    /// </summary>
    public int TotalFiles { get; set; }

    /// <summary>
    /// 总目录数
    /// </summary>
    public int TotalDirectories { get; set; }

    /// <summary>
    /// 总大小（字节）
    /// </summary>
    public long TotalSize { get; set; }

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModified { get; set; }
}

/// <summary>
/// 文件信息
/// </summary>
public class FileInfo
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FullPath { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string Extension { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModified { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 是否为只读
    /// </summary>
    public bool IsReadOnly { get; set; }
}

/// <summary>
/// 目录信息
/// </summary>
public class DirectoryInfo
{
    /// <summary>
    /// 目录名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 目录路径
    /// </summary>
    public string FullPath { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModified { get; set; }

    /// <summary>
    /// 子文件数量
    /// </summary>
    public int FileCount { get; set; }

    /// <summary>
    /// 子目录数量
    /// </summary>
    public int SubdirectoryCount { get; set; }

    /// <summary>
    /// 目录大小（字节）
    /// </summary>
    public long Size { get; set; }
}

/// <summary>
/// 磁盘使用情况
/// </summary>
public class DiskUsage
{
    /// <summary>
    /// 总空间（字节）
    /// </summary>
    public long TotalSpace { get; set; }

    /// <summary>
    /// 已使用空间（字节）
    /// </summary>
    public long UsedSpace { get; set; }

    /// <summary>
    /// 可用空间（字节）
    /// </summary>
    public long FreeSpace { get; set; }

    /// <summary>
    /// 使用百分比
    /// </summary>
    public double UsagePercentage => TotalSpace > 0 ? (double)UsedSpace / TotalSpace * 100 : 0;

    /// <summary>
    /// 驱动器信息
    /// </summary>
    public List<DriveInfo> Drives { get; set; } = new();
}

/// <summary>
/// 驱动器信息
/// </summary>
public class DriveInfo
{
    /// <summary>
    /// 驱动器名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 驱动器类型
    /// </summary>
    public string DriveType { get; set; } = string.Empty;

    /// <summary>
    /// 文件系统
    /// </summary>
    public string FileSystem { get; set; } = string.Empty;

    /// <summary>
    /// 总空间（字节）
    /// </summary>
    public long TotalSpace { get; set; }

    /// <summary>
    /// 可用空间（字节）
    /// </summary>
    public long FreeSpace { get; set; }

    /// <summary>
    /// 使用百分比
    /// </summary>
    public double UsagePercentage => TotalSpace > 0 ? (double)(TotalSpace - FreeSpace) / TotalSpace * 100 : 0;
}
