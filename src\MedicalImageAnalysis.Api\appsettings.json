{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/medical-image-analysis-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*", "Kestrel": {"Limits": {"MaxRequestBodySize": 524288000}}, "MedicalImageAnalysis": {"MaxFileSize": 524288000, "SupportedFormats": ["dcm", "dicom"], "TempDirectory": "temp", "OutputDirectory": "output", "Models": {"DefaultYoloModel": "models/yolo11n.pt", "ModelDirectory": "models"}, "Processing": {"MaxConcurrentProcessing": 4, "DefaultImageSize": 640, "DefaultConfidenceThreshold": 0.5, "DefaultIouThreshold": 0.45}, "Export": {"DefaultFormat": "YOLO", "DefaultImageFormat": "PNG", "DefaultImageQuality": 95, "DefaultTrainRatio": 0.8, "DefaultValidationRatio": 0.15, "DefaultTestRatio": 0.05}}}