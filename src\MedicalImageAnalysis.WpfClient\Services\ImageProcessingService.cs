using Microsoft.Extensions.Logging;
using System.IO;
using System.Windows.Media.Imaging;
using System.Windows.Media;
using FellowOakDicom;
using FellowOakDicom.Imaging;
using System.Drawing;
using System.Drawing.Imaging;

namespace MedicalImageAnalysis.WpfClient.Services;

/// <summary>
/// 图像处理服务接口
/// </summary>
public interface IImageProcessingService
{
    Task<BitmapImage?> LoadDicomImageAsync(string filePath);
    Task<BitmapImage?> ApplyWindowLevelAsync(string filePath, double windowWidth, double windowCenter);
    Task<Dictionary<string, object>> ExtractDicomMetadataAsync(string filePath);
    Task<BitmapImage?> ConvertToDisplayImageAsync(byte[] pixelData, int width, int height, double windowWidth, double windowCenter);
}

/// <summary>
/// 图像处理服务实现
/// </summary>
public class ImageProcessingService : IImageProcessingService
{
    private readonly ILogger<ImageProcessingService> _logger;

    public ImageProcessingService(ILogger<ImageProcessingService> logger)
    {
        _logger = logger;
        
        // 配置 fo-dicom
        try
        {
            new DicomSetupBuilder()
                .RegisterServices(s => s.AddFellowOakDicom())
                .Build();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "DICOM设置可能已经初始化");
        }
    }

    /// <summary>
    /// 加载DICOM图像
    /// </summary>
    public async Task<BitmapImage?> LoadDicomImageAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogError("DICOM文件不存在: {FilePath}", filePath);
                return null;
            }

            _logger.LogInformation("开始加载DICOM图像: {FilePath}", filePath);

            // 在后台线程中处理DICOM文件
            return await Task.Run(() =>
            {
                try
                {
                    var dicomFile = DicomFile.Open(filePath);
                    var dicomImage = new DicomImage(dicomFile.Dataset);

                    // 获取第一帧图像
                    var image = dicomImage.RenderImage();

                    // 转换为WPF可用的BitmapImage
                    using var memoryStream = new MemoryStream();

                    // 转换为Bitmap并保存为PNG
                    var bitmap = image.As<System.Drawing.Bitmap>();
                    bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
                    memoryStream.Position = 0;

                    var bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                    bitmapImage.StreamSource = memoryStream;
                    bitmapImage.EndInit();
                    bitmapImage.Freeze(); // 使其可以跨线程使用

                    _logger.LogInformation("DICOM图像加载成功: {FilePath}", filePath);
                    return bitmapImage;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加载DICOM图像失败: {FilePath}", filePath);
                    return null;
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载DICOM图像时发生错误: {FilePath}", filePath);
            return null;
        }
    }

    /// <summary>
    /// 应用窗宽窗位
    /// </summary>
    public async Task<BitmapImage?> ApplyWindowLevelAsync(string filePath, double windowWidth, double windowCenter)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogError("DICOM文件不存在: {FilePath}", filePath);
                return null;
            }

            _logger.LogInformation("应用窗宽窗位: {FilePath}, W={WindowWidth}, C={WindowCenter}", 
                filePath, windowWidth, windowCenter);

            return await Task.Run(() =>
            {
                try
                {
                    var dicomFile = DicomFile.Open(filePath);
                    var dicomImage = new DicomImage(dicomFile.Dataset);

                    // 应用窗宽窗位
                    dicomImage.WindowWidth = windowWidth;
                    dicomImage.WindowCenter = windowCenter;

                    var image = dicomImage.RenderImage();

                    using var memoryStream = new MemoryStream();
                    var bitmap = image.As<System.Drawing.Bitmap>();
                    bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
                    memoryStream.Position = 0;

                    var bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                    bitmapImage.StreamSource = memoryStream;
                    bitmapImage.EndInit();
                    bitmapImage.Freeze();

                    _logger.LogInformation("窗宽窗位应用成功: {FilePath}, W={WindowWidth}, C={WindowCenter}",
                        filePath, windowWidth, windowCenter);
                    return bitmapImage;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "应用窗宽窗位失败: {FilePath}", filePath);
                    return null;
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用窗宽窗位时发生错误: {FilePath}", filePath);
            return null;
        }
    }

    /// <summary>
    /// 提取DICOM元数据
    /// </summary>
    public async Task<Dictionary<string, object>> ExtractDicomMetadataAsync(string filePath)
    {
        var metadata = new Dictionary<string, object>();

        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogError("DICOM文件不存在: {FilePath}", filePath);
                return metadata;
            }

            await Task.Run(() =>
            {
                try
                {
                    var dicomFile = DicomFile.Open(filePath);
                    var dataset = dicomFile.Dataset;

                    // 提取基本信息
                    metadata["PatientName"] = GetDicomValue(dataset, DicomTag.PatientName, "未知");
                    metadata["PatientID"] = GetDicomValue(dataset, DicomTag.PatientID, "未知");
                    metadata["PatientSex"] = GetDicomValue(dataset, DicomTag.PatientSex, "未知");
                    metadata["PatientAge"] = GetDicomValue(dataset, DicomTag.PatientAge, "未知");
                    
                    // 检查信息
                    metadata["StudyDate"] = GetDicomValue(dataset, DicomTag.StudyDate, "未知");
                    metadata["StudyTime"] = GetDicomValue(dataset, DicomTag.StudyTime, "未知");
                    metadata["StudyDescription"] = GetDicomValue(dataset, DicomTag.StudyDescription, "未知");
                    metadata["SeriesDescription"] = GetDicomValue(dataset, DicomTag.SeriesDescription, "未知");
                    
                    // 图像信息
                    metadata["Modality"] = GetDicomValue(dataset, DicomTag.Modality, "未知");
                    metadata["Rows"] = GetDicomValue(dataset, DicomTag.Rows, 0);
                    metadata["Columns"] = GetDicomValue(dataset, DicomTag.Columns, 0);
                    metadata["BitsAllocated"] = GetDicomValue(dataset, DicomTag.BitsAllocated, 0);
                    metadata["BitsStored"] = GetDicomValue(dataset, DicomTag.BitsStored, 0);
                    metadata["PixelSpacing"] = GetDicomValue(dataset, DicomTag.PixelSpacing, "未知");
                    metadata["SliceThickness"] = GetDicomValue(dataset, DicomTag.SliceThickness, "未知");
                    
                    // 窗宽窗位
                    metadata["WindowWidth"] = GetDicomValue(dataset, DicomTag.WindowWidth, 400.0);
                    metadata["WindowCenter"] = GetDicomValue(dataset, DicomTag.WindowCenter, 200.0);
                    
                    // 实例信息
                    metadata["InstanceNumber"] = GetDicomValue(dataset, DicomTag.InstanceNumber, 0);
                    metadata["SliceLocation"] = GetDicomValue(dataset, DicomTag.SliceLocation, "未知");
                    
                    _logger.LogInformation("DICOM元数据提取成功: {FilePath}", filePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "提取DICOM元数据失败: {FilePath}", filePath);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取DICOM元数据时发生错误: {FilePath}", filePath);
        }

        return metadata;
    }

    /// <summary>
    /// 转换像素数据为显示图像
    /// </summary>
    public async Task<BitmapImage?> ConvertToDisplayImageAsync(byte[] pixelData, int width, int height, double windowWidth, double windowCenter)
    {
        try
        {
            return await Task.Run(() =>
            {
                try
                {
                    // 创建WriteableBitmap
                    var writeableBitmap = new WriteableBitmap(width, height, 96, 96, PixelFormats.Gray8, null);
                    
                    // 应用窗宽窗位变换
                    var transformedData = ApplyWindowLevelTransform(pixelData, windowWidth, windowCenter);
                    
                    // 写入像素数据
                    writeableBitmap.WritePixels(new System.Windows.Int32Rect(0, 0, width, height), 
                        transformedData, width, 0);
                    
                    // 转换为BitmapImage
                    using var memoryStream = new MemoryStream();
                    var encoder = new PngBitmapEncoder();
                    encoder.Frames.Add(BitmapFrame.Create(writeableBitmap));
                    encoder.Save(memoryStream);
                    memoryStream.Position = 0;

                    var bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                    bitmapImage.StreamSource = memoryStream;
                    bitmapImage.EndInit();
                    bitmapImage.Freeze();

                    return bitmapImage;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "转换像素数据为显示图像失败");
                    return null;
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换像素数据为显示图像时发生错误");
            return null;
        }
    }

    /// <summary>
    /// 获取DICOM标签值
    /// </summary>
    private T GetDicomValue<T>(DicomDataset dataset, DicomTag tag, T defaultValue)
    {
        try
        {
            if (dataset.Contains(tag))
            {
                return dataset.GetValue<T>(tag, 0);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取DICOM标签值失败: {Tag}", tag);
        }
        
        return defaultValue;
    }

    /// <summary>
    /// 应用窗宽窗位变换
    /// </summary>
    private byte[] ApplyWindowLevelTransform(byte[] pixelData, double windowWidth, double windowCenter)
    {
        var transformedData = new byte[pixelData.Length];
        
        var windowMin = windowCenter - windowWidth / 2.0;
        var windowMax = windowCenter + windowWidth / 2.0;
        
        for (int i = 0; i < pixelData.Length; i++)
        {
            var pixelValue = pixelData[i];
            
            if (pixelValue <= windowMin)
            {
                transformedData[i] = 0;
            }
            else if (pixelValue >= windowMax)
            {
                transformedData[i] = 255;
            }
            else
            {
                var normalizedValue = (pixelValue - windowMin) / windowWidth;
                transformedData[i] = (byte)(normalizedValue * 255);
            }
        }
        
        return transformedData;
    }
}
