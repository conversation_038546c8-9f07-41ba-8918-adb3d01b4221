using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Interfaces;
using System.Collections.Concurrent;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 连接管理服务
/// </summary>
public class ConnectionManagerService : IConnectionManagerService
{
    private readonly IHubContext<ProcessingHub> _hubContext;
    private readonly ILogger<ConnectionManagerService> _logger;
    private readonly ConcurrentDictionary<string, ConnectionInfo> _connections;
    private readonly ConcurrentDictionary<string, HashSet<string>> _userConnections;

    public ConnectionManagerService(
        IHubContext<ProcessingHub> hubContext,
        ILogger<ConnectionManagerService> logger)
    {
        _hubContext = hubContext;
        _logger = logger;
        _connections = new ConcurrentDictionary<string, ConnectionInfo>();
        _userConnections = new ConcurrentDictionary<string, HashSet<string>>();
    }

    /// <summary>
    /// 添加连接
    /// </summary>
    public void AddConnection(string connectionId, string userId, string userAgent, string ipAddress)
    {
        var connectionInfo = new ConnectionInfo
        {
            ConnectionId = connectionId,
            UserId = userId,
            UserAgent = userAgent,
            IpAddress = ipAddress,
            ConnectedAt = DateTime.UtcNow,
            LastActivity = DateTime.UtcNow
        };

        _connections.TryAdd(connectionId, connectionInfo);

        // 添加到用户连接映射
        _userConnections.AddOrUpdate(userId,
            new HashSet<string> { connectionId },
            (key, existing) =>
            {
                existing.Add(connectionId);
                return existing;
            });

        _logger.LogInformation("连接已添加: {ConnectionId}, 用户: {UserId}, IP: {IpAddress}",
            connectionId, userId, ipAddress);
    }

    /// <summary>
    /// 移除连接
    /// </summary>
    public void RemoveConnection(string connectionId)
    {
        if (_connections.TryRemove(connectionId, out var connectionInfo))
        {
            // 从用户连接映射中移除
            if (_userConnections.TryGetValue(connectionInfo.UserId, out var userConnections))
            {
                userConnections.Remove(connectionId);
                if (userConnections.Count == 0)
                {
                    _userConnections.TryRemove(connectionInfo.UserId, out _);
                }
            }

            _logger.LogInformation("连接已移除: {ConnectionId}, 用户: {UserId}",
                connectionId, connectionInfo.UserId);
        }
    }

    /// <summary>
    /// 更新连接活动时间
    /// </summary>
    public void UpdateConnectionActivity(string connectionId)
    {
        if (_connections.TryGetValue(connectionId, out var connectionInfo))
        {
            connectionInfo.LastActivity = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 添加连接到组
    /// </summary>
    public void AddConnectionToGroup(string connectionId, string groupName)
    {
        if (_connections.TryGetValue(connectionId, out var connectionInfo))
        {
            if (!connectionInfo.Groups.Contains(groupName))
            {
                connectionInfo.Groups.Add(groupName);
                _logger.LogDebug("连接 {ConnectionId} 已加入组 {GroupName}", connectionId, groupName);
            }
        }
    }

    /// <summary>
    /// 从组中移除连接
    /// </summary>
    public void RemoveConnectionFromGroup(string connectionId, string groupName)
    {
        if (_connections.TryGetValue(connectionId, out var connectionInfo))
        {
            connectionInfo.Groups.Remove(groupName);
            _logger.LogDebug("连接 {ConnectionId} 已离开组 {GroupName}", connectionId, groupName);
        }
    }

    public int GetOnlineUserCount()
    {
        return _userConnections.Count;
    }

    public int GetActiveConnectionCount()
    {
        return _connections.Count;
    }

    public async Task<List<ConnectionInfo>> GetUserConnectionsAsync(string userId)
    {
        await Task.CompletedTask;

        var userConnections = new List<ConnectionInfo>();

        if (_userConnections.TryGetValue(userId, out var connectionIds))
        {
            foreach (var connectionId in connectionIds)
            {
                if (_connections.TryGetValue(connectionId, out var connectionInfo))
                {
                    userConnections.Add(connectionInfo);
                }
            }
        }

        return userConnections;
    }

    public async Task DisconnectUserAsync(string userId)
    {
        try
        {
            if (_userConnections.TryGetValue(userId, out var connectionIds))
            {
                var tasks = connectionIds.Select(async connectionId =>
                {
                    try
                    {
                        await _hubContext.Clients.Client(connectionId)
                            .SendAsync("ForceDisconnect", "管理员强制断开连接");
                        
                        // 移除连接
                        RemoveConnection(connectionId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "断开连接失败: {ConnectionId}", connectionId);
                    }
                });

                await Task.WhenAll(tasks);
                _logger.LogInformation("用户 {UserId} 的所有连接已断开", userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开用户连接失败: {UserId}", userId);
        }
    }

    public async Task BroadcastMaintenanceNotificationAsync(string message, DateTime scheduledTime)
    {
        try
        {
            var notification = new
            {
                type = "maintenance",
                title = "系统维护通知",
                message = message,
                scheduledTime = scheduledTime,
                timestamp = DateTime.UtcNow
            };

            await _hubContext.Clients.All.SendAsync("MaintenanceNotification", notification);
            _logger.LogInformation("系统维护通知已广播: {Message}, 计划时间: {ScheduledTime}", message, scheduledTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "广播维护通知失败");
        }
    }

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    public ConnectionStatistics GetConnectionStatistics()
    {
        var now = DateTime.UtcNow;
        var activeConnections = _connections.Values.Where(c => (now - c.LastActivity).TotalMinutes < 5).ToList();
        var idleConnections = _connections.Values.Where(c => (now - c.LastActivity).TotalMinutes >= 5).ToList();

        return new ConnectionStatistics
        {
            TotalConnections = _connections.Count,
            ActiveConnections = activeConnections.Count,
            IdleConnections = idleConnections.Count,
            UniqueUsers = _userConnections.Count,
            AverageConnectionsPerUser = _userConnections.Count > 0 ? (double)_connections.Count / _userConnections.Count : 0,
            ConnectionsByGroup = GetConnectionsByGroup(),
            Timestamp = now
        };
    }

    /// <summary>
    /// 清理过期连接
    /// </summary>
    public async Task CleanupExpiredConnectionsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredConnections = _connections.Values
                .Where(c => (now - c.LastActivity).TotalHours > 24)
                .ToList();

            foreach (var connection in expiredConnections)
            {
                RemoveConnection(connection.ConnectionId);
            }

            if (expiredConnections.Any())
            {
                _logger.LogInformation("清理了 {Count} 个过期连接", expiredConnections.Count);
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期连接失败");
        }
    }

    /// <summary>
    /// 发送连接统计更新
    /// </summary>
    public async Task SendConnectionStatisticsAsync()
    {
        try
        {
            var statistics = GetConnectionStatistics();
            await _hubContext.Clients.All.SendAsync("ConnectionStatistics", statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送连接统计失败");
        }
    }

    private Dictionary<string, int> GetConnectionsByGroup()
    {
        var groupCounts = new Dictionary<string, int>();

        foreach (var connection in _connections.Values)
        {
            foreach (var group in connection.Groups)
            {
                groupCounts[group] = groupCounts.GetValueOrDefault(group, 0) + 1;
            }
        }

        return groupCounts;
    }
}

/// <summary>
/// 连接统计信息
/// </summary>
public class ConnectionStatistics
{
    public int TotalConnections { get; set; }
    public int ActiveConnections { get; set; }
    public int IdleConnections { get; set; }
    public int UniqueUsers { get; set; }
    public double AverageConnectionsPerUser { get; set; }
    public Dictionary<string, int> ConnectionsByGroup { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 增强的ProcessingHub，集成连接管理
/// </summary>
public class EnhancedProcessingHub : Hub
{
    private readonly ILogger<EnhancedProcessingHub> _logger;
    private readonly ConnectionManagerService _connectionManager;

    public EnhancedProcessingHub(
        ILogger<EnhancedProcessingHub> logger,
        ConnectionManagerService connectionManager)
    {
        _logger = logger;
        _connectionManager = connectionManager;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = Context.User?.Identity?.Name ?? "anonymous";
        var userAgent = Context.GetHttpContext()?.Request.Headers["User-Agent"].ToString() ?? "";
        var ipAddress = Context.GetHttpContext()?.Connection.RemoteIpAddress?.ToString() ?? "";

        _connectionManager.AddConnection(Context.ConnectionId, userId, userAgent, ipAddress);
        await Groups.AddToGroupAsync(Context.ConnectionId, "AllClients");

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _connectionManager.RemoveConnection(Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }

    public async Task JoinGroup(string groupName)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _connectionManager.AddConnectionToGroup(Context.ConnectionId, groupName);
    }

    public async Task LeaveGroup(string groupName)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _connectionManager.RemoveConnectionFromGroup(Context.ConnectionId, groupName);
    }

    public async Task UpdateActivity()
    {
        _connectionManager.UpdateConnectionActivity(Context.ConnectionId);
        await Task.CompletedTask;
    }
}
