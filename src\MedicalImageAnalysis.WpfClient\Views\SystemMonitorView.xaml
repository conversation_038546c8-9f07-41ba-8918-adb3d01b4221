<UserControl x:Class="MedicalImageAnalysis.WpfClient.Views.SystemMonitorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,8">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Monitor" Width="24" Height="24" VerticalAlignment="Center"/>
                <TextBlock Text="系统监控" 
                         FontSize="18" 
                         FontWeight="Medium"
                         VerticalAlignment="Center"
                         Margin="12,0,24,0"/>

                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding RefreshCommand}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"/>
                        <TextBlock Text="刷新" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>

                <ToggleButton Style="{StaticResource MaterialDesignSwitchToggleButton}"
                            IsChecked="{Binding AutoRefresh}"
                            ToolTip="自动刷新"/>
                <TextBlock Text="自动刷新" VerticalAlignment="Center" Margin="8,0,0,0"/>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="24">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 系统概览卡片 -->
                <UniformGrid Grid.Row="0" Columns="4" Margin="0,0,0,24">
                    <!-- CPU使用率 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="CPU使用率" Style="{StaticResource CaptionText}"/>
                                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0:F1}%}" 
                                             FontSize="28" 
                                             FontWeight="Bold" 
                                             Foreground="{StaticResource PrimaryBrush}"/>
                                </StackPanel>
                                
                                <materialDesign:PackIcon Grid.Column="1" 
                                                       Kind="Cpu64Bit" 
                                                       Width="32" Height="32"
                                                       Foreground="{StaticResource PrimaryBrush}"
                                                       VerticalAlignment="Center"/>
                            </Grid>
                            <ProgressBar Value="{Binding CpuUsage}" 
                                       Maximum="100"
                                       Style="{StaticResource CustomProgressBar}"
                                       Margin="0,8,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 内存使用率 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="内存使用率" Style="{StaticResource CaptionText}"/>
                                    <TextBlock Text="{Binding MemoryUsage, StringFormat={}{0:F1}%}" 
                                             FontSize="28" 
                                             FontWeight="Bold" 
                                             Foreground="{StaticResource SecondaryBrush}"/>
                                </StackPanel>
                                
                                <materialDesign:PackIcon Grid.Column="1" 
                                                       Kind="Memory" 
                                                       Width="32" Height="32"
                                                       Foreground="{StaticResource SecondaryBrush}"
                                                       VerticalAlignment="Center"/>
                            </Grid>
                            <ProgressBar Value="{Binding MemoryUsage}" 
                                       Maximum="100"
                                       Style="{StaticResource CustomProgressBar}"
                                       Margin="0,8,0,0"/>
                            <TextBlock Text="{Binding MemoryInfo}" 
                                     Style="{StaticResource CaptionText}"
                                     Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 磁盘使用率 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="磁盘使用率" Style="{StaticResource CaptionText}"/>
                                    <TextBlock Text="{Binding DiskUsage, StringFormat={}{0:F1}%}" 
                                             FontSize="28" 
                                             FontWeight="Bold" 
                                             Foreground="{StaticResource AccentBrush}"/>
                                </StackPanel>
                                
                                <materialDesign:PackIcon Grid.Column="1" 
                                                       Kind="Harddisk" 
                                                       Width="32" Height="32"
                                                       Foreground="{StaticResource AccentBrush}"
                                                       VerticalAlignment="Center"/>
                            </Grid>
                            <ProgressBar Value="{Binding DiskUsage}" 
                                       Maximum="100"
                                       Style="{StaticResource CustomProgressBar}"
                                       Margin="0,8,0,0"/>
                            <TextBlock Text="{Binding DiskInfo}" 
                                     Style="{StaticResource CaptionText}"
                                     Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- GPU使用率 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="GPU使用率" Style="{StaticResource CaptionText}"/>
                                    <TextBlock Text="{Binding GpuUsage, StringFormat={}{0:F1}%}" 
                                             FontSize="28" 
                                             FontWeight="Bold" 
                                             Foreground="{StaticResource WarningBrush}"/>
                                </StackPanel>
                                
                                <materialDesign:PackIcon Grid.Column="1" 
                                                       Kind="Gpu" 
                                                       Width="32" Height="32"
                                                       Foreground="{StaticResource WarningBrush}"
                                                       VerticalAlignment="Center"/>
                            </Grid>
                            <ProgressBar Value="{Binding GpuUsage}" 
                                       Maximum="100"
                                       Style="{StaticResource CustomProgressBar}"
                                       Margin="0,8,0,0"/>
                            <TextBlock Text="{Binding GpuInfo}" 
                                     Style="{StaticResource CaptionText}"
                                     Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>
                </UniformGrid>

                <!-- 任务状态和网络信息 -->
                <Grid Grid.Row="1" Margin="0,0,0,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 任务状态 -->
                    <materialDesign:Card Grid.Column="0" Style="{StaticResource InfoCard}" Margin="0,0,12,0">
                        <StackPanel>
                            <TextBlock Text="任务状态" Style="{StaticResource SubtitleText}"/>
                            
                            <Grid Margin="0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="运行中" FontWeight="Medium" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding ActiveTasks}" 
                                             FontSize="24" 
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             Foreground="{StaticResource SuccessBrush}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="队列中" FontWeight="Medium" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding QueuedTasks}" 
                                             FontSize="24" 
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             Foreground="{StaticResource WarningBrush}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="已完成" FontWeight="Medium" HorizontalAlignment="Center"/>
                                    <TextBlock Text="{Binding CompletedTasks}" 
                                             FontSize="24" 
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             Foreground="{StaticResource PrimaryBrush}"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 网络状态 -->
                    <materialDesign:Card Grid.Column="1" Style="{StaticResource InfoCard}" Margin="12,0,0,0">
                        <StackPanel>
                            <TextBlock Text="网络状态" Style="{StaticResource SubtitleText}"/>
                            
                            <Grid Margin="0,16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="API连接:" FontWeight="Medium"/>
                                <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="8,0,0,0">
                                    <Ellipse Width="12" Height="12" 
                                           Fill="{Binding ApiConnectionStatus, Converter={StaticResource ConnectionStatusToBrushConverter}}"
                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding ApiConnectionText}" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="SignalR:" FontWeight="Medium" Margin="0,4,0,0"/>
                                <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="8,4,0,0">
                                    <Ellipse Width="12" Height="12" 
                                           Fill="{Binding SignalRConnectionStatus, Converter={StaticResource ConnectionStatusToBrushConverter}}"
                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding SignalRConnectionText}" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="延迟:" FontWeight="Medium" Margin="0,4,0,0"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding NetworkLatency}" Margin="8,4,0,0"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>

                <!-- 详细信息区域 -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 进程列表 -->
                    <materialDesign:Card Grid.Column="0" Style="{StaticResource InfoCard}" Margin="0,0,12,0">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="活动进程" Style="{StaticResource SubtitleText}"/>
                                <Button Grid.Column="1" 
                                      Style="{StaticResource MaterialDesignToolButton}"
                                      Command="{Binding RefreshProcessesCommand}"
                                      ToolTip="刷新进程列表">
                                    <materialDesign:PackIcon Kind="Refresh"/>
                                </Button>
                            </Grid>

                            <DataGrid ItemsSource="{Binding Processes}"
                                    Style="{StaticResource CustomDataGrid}"
                                    Height="200"
                                    Margin="0,8">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="进程名" Binding="{Binding Name}" Width="*"/>
                                    <DataGridTextColumn Header="PID" Binding="{Binding Id}" Width="60"/>
                                    <DataGridTextColumn Header="CPU%" Binding="{Binding CpuUsage, StringFormat=F1}" Width="60"/>
                                    <DataGridTextColumn Header="内存" Binding="{Binding MemoryUsage, Converter={StaticResource ByteSizeConverter}}" Width="80"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 系统日志 -->
                    <materialDesign:Card Grid.Column="1" Style="{StaticResource InfoCard}" Margin="12,0,0,0">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="系统日志" Style="{StaticResource SubtitleText}"/>
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button Style="{StaticResource MaterialDesignToolButton}"
                                          Command="{Binding ClearLogsCommand}"
                                          ToolTip="清空日志">
                                        <materialDesign:PackIcon Kind="Delete"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignToolButton}"
                                          Command="{Binding ExportLogsCommand}"
                                          ToolTip="导出日志">
                                        <materialDesign:PackIcon Kind="Export"/>
                                    </Button>
                                </StackPanel>
                            </Grid>

                            <ScrollViewer Height="200" 
                                        VerticalScrollBarVisibility="Auto"
                                        Margin="0,8">
                                <ItemsControl ItemsSource="{Binding SystemLogs}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Margin="0,2">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <TextBlock Grid.Column="0" 
                                                         Text="{Binding Timestamp, StringFormat=HH:mm:ss}" 
                                                         Style="{StaticResource CaptionText}"
                                                         Margin="0,0,8,0"/>
                                                
                                                <Rectangle Grid.Column="1" 
                                                         Width="8" Height="8" 
                                                         Fill="{Binding Level, Converter={StaticResource LogLevelToBrushConverter}}"
                                                         Margin="0,0,8,0"
                                                         VerticalAlignment="Center"/>
                                                
                                                <TextBlock Grid.Column="2" 
                                                         Text="{Binding Message}" 
                                                         TextWrapping="Wrap"
                                                         FontSize="11"/>
                                            </Grid>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>
            </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
