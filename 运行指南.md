# 医学影像解析系统 - 运行指南

## 🚀 快速开始

### 方法一：使用批处理脚本（推荐）

1. **双击运行 `run-project.bat`**
   - 自动检查环境
   - 恢复依赖包
   - 构建项目
   - 启动服务

2. **或者双击运行 `run-project.ps1`**
   - PowerShell 版本，功能相同
   - 更好的错误提示和颜色显示

### 方法二：手动运行

1. **打开命令提示符或 PowerShell**
2. **进入项目目录**
   ```bash
   cd "C:\Users\<USER>\Desktop\医学影像解析"
   ```

3. **恢复依赖包**
   ```bash
   dotnet restore
   ```

4. **构建项目**
   ```bash
   dotnet build
   ```

5. **运行 API 服务**
   ```bash
   cd src\MedicalImageAnalysis.Api
   dotnet run
   ```

6. **或运行 Web 界面**
   ```bash
   cd src\MedicalImageAnalysis.Web
   dotnet run
   ```

## 🌐 访问地址

### API 服务
- **HTTP**: http://localhost:5000
- **HTTPS**: https://localhost:5001
- **Swagger 文档**: http://localhost:5000/swagger
- **健康检查**: http://localhost:5000/health

### Web 界面
- **HTTP**: http://localhost:5002
- **HTTPS**: https://localhost:5003

## 🔧 故障排除

### 问题 1: "dotnet" 不是内部或外部命令

**原因**: .NET SDK 未正确安装或环境变量未设置

**解决方案**:
1. 重新安装 .NET 8 SDK
2. 重启命令提示符
3. 检查环境变量 PATH 中是否包含 .NET 路径

### 问题 2: NuGet 包恢复失败

**解决方案**:
```bash
# 清理 NuGet 缓存
dotnet nuget locals all --clear

# 重新恢复包
dotnet restore
```

### 问题 3: 编译错误

**常见错误及解决方案**:

1. **CS0246: 找不到类型或命名空间**
   - 检查项目引用是否正确
   - 运行 `dotnet restore`

2. **CS1061: 不包含定义**
   - 检查 using 语句
   - 确保引用了正确的 NuGet 包

3. **端口被占用**
   - 修改 `appsettings.json` 中的端口配置
   - 或关闭占用端口的程序

### 问题 4: 权限问题

**解决方案**:
- 以管理员身份运行命令提示符
- 确保对项目目录有读写权限

## 📁 项目结构

```
医学影像解析/
├── src/                          # 源代码
│   ├── MedicalImageAnalysis.Api/          # Web API
│   ├── MedicalImageAnalysis.Web/          # Web 界面
│   ├── MedicalImageAnalysis.Core/         # 核心库
│   ├── MedicalImageAnalysis.Infrastructure/ # 基础设施
│   └── MedicalImageAnalysis.Application/  # 应用服务
├── tests/                        # 测试项目
├── data/                         # 数据目录（自动创建）
│   ├── logs/                     # 日志文件
│   ├── temp/                     # 临时文件
│   ├── output/                   # 输出文件
│   └── models/                   # AI 模型
├── Brain/                        # 示例 DICOM 文件
├── run-project.bat               # Windows 启动脚本
├── run-project.ps1               # PowerShell 启动脚本
└── test-build.bat                # 构建测试脚本
```

## 🧪 测试功能

### 1. 健康检查
```bash
curl http://localhost:5000/health
```

### 2. 目录管理
```bash
# 获取系统目录
curl http://localhost:5000/api/directory/system-directories

# 打开数据目录
curl -X POST http://localhost:5000/api/directory/open-system/data
```

### 3. DICOM 文件处理
- 访问 Web 界面: http://localhost:5002
- 上传 Brain 目录中的示例 DICOM 文件

## 📝 开发说明

### 添加新功能
1. 在 Core 项目中定义接口和实体
2. 在 Infrastructure 项目中实现服务
3. 在 Application 项目中添加应用逻辑
4. 在 API 项目中添加控制器
5. 在 Web 项目中添加页面

### 调试模式
```bash
# 以调试模式运行
dotnet run --configuration Debug

# 查看详细日志
dotnet run --verbosity diagnostic
```

### 生产部署
```bash
# 发布应用
dotnet publish --configuration Release --output ./publish

# 运行发布版本
cd publish
dotnet MedicalImageAnalysis.Api.dll
```

## 🆘 获取帮助

如果遇到问题：

1. **检查日志文件**: `data/logs/` 目录
2. **查看控制台输出**: 运行时的错误信息
3. **使用测试脚本**: `test-build.bat` 检查构建问题
4. **重新安装依赖**: 删除 `bin` 和 `obj` 目录后重新构建

## 🎯 下一步

1. 访问 Swagger 文档了解 API
2. 上传示例 DICOM 文件测试功能
3. 探索目录管理功能
4. 查看系统日志和监控信息
