using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.WpfClient.Models;
using MedicalImageAnalysis.WpfClient.Services;
using System.Collections.ObjectModel;
using System.IO;

namespace MedicalImageAnalysis.WpfClient.ViewModels;

/// <summary>
/// 数据管理ViewModel
/// </summary>
public partial class DataManagementViewModel : ObservableObject
{
    private readonly ILogger<DataManagementViewModel> _logger;
    private readonly IApiService _apiService;
    private readonly IDialogService _dialogService;
    private readonly INotificationService _notificationService;
    private readonly IFileService _fileService;

    [ObservableProperty]
    private ObservableCollection<DatasetProject> _projects = new();

    [ObservableProperty]
    private DatasetProject? _selectedProject;

    [ObservableProperty]
    private ObservableCollection<Models.FileInfo> _files = new();

    [ObservableProperty]
    private Models.FileInfo? _selectedFile;

    [ObservableProperty]
    private bool _isLoading = false;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private double _progressValue = 0;

    [ObservableProperty]
    private bool _isProgressVisible = false;

    [ObservableProperty]
    private string _searchText = "";

    [ObservableProperty]
    private string _selectedFileType = "全部";

    [ObservableProperty]
    private ObservableCollection<string> _fileTypes = new() 
    { 
        "全部", "DICOM", "JPEG", "PNG", "标注文件" 
    };

    [ObservableProperty]
    private string _currentPath = "";

    [ObservableProperty]
    private ObservableCollection<string> _pathHistory = new();

    [ObservableProperty]
    private long _totalFileSize = 0;

    [ObservableProperty]
    private int _totalFileCount = 0;

    [ObservableProperty]
    private string _selectedSortBy = "名称";

    [ObservableProperty]
    private ObservableCollection<string> _sortOptions = new() 
    { 
        "名称", "大小", "创建时间", "修改时间", "类型" 
    };

    [ObservableProperty]
    private bool _isAscending = true;

    public DataManagementViewModel(
        ILogger<DataManagementViewModel> logger,
        IApiService apiService,
        IDialogService dialogService,
        INotificationService notificationService,
        IFileService fileService)
    {
        _logger = logger;
        _apiService = apiService;
        _dialogService = dialogService;
        _notificationService = notificationService;
        _fileService = fileService;

        InitializeDefaultData();

        // 监听属性变化
        PropertyChanged += (s, e) =>
        {
            switch (e.PropertyName)
            {
                case nameof(SelectedProject):
                    _ = LoadProjectFiles();
                    break;
                case nameof(SearchText):
                case nameof(SelectedFileType):
                case nameof(SelectedSortBy):
                case nameof(IsAscending):
                    FilterAndSortFiles();
                    break;
            }
        };
    }

    /// <summary>
    /// 创建新项目
    /// </summary>
    [RelayCommand]
    private async Task CreateProject()
    {
        try
        {
            // 这里应该显示创建项目对话框
            var projectName = "新项目_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");
            
            var project = new DatasetProject
            {
                Id = Guid.NewGuid(),
                Name = projectName,
                Description = "新创建的数据集项目",
                CreatedTime = DateTime.Now,
                ModifiedTime = DateTime.Now,
                Path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
                                   "MedicalImageAnalysis", "Projects", projectName)
            };

            Projects.Add(project);
            SelectedProject = project;

            StatusMessage = $"项目 '{projectName}' 创建成功";
            await _notificationService.ShowSuccessAsync("成功", "项目创建成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建项目失败");
            await _notificationService.ShowErrorAsync("错误", "创建项目失败");
        }
    }

    /// <summary>
    /// 打开项目
    /// </summary>
    [RelayCommand]
    private async Task OpenProject()
    {
        try
        {
            var folderPath = await _dialogService.SelectFolderDialogAsync("选择项目文件夹");
            if (!string.IsNullOrEmpty(folderPath))
            {
                await LoadProjectFromPath(folderPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开项目失败");
            await _notificationService.ShowErrorAsync("错误", "打开项目失败");
        }
    }

    /// <summary>
    /// 删除项目
    /// </summary>
    [RelayCommand]
    private async Task DeleteProject()
    {
        try
        {
            if (SelectedProject == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择要删除的项目");
                return;
            }

            var confirmed = await _dialogService.ShowConfirmationAsync(
                "确认删除", $"确定要删除项目 '{SelectedProject.Name}' 吗？此操作不可撤销。");

            if (confirmed)
            {
                Projects.Remove(SelectedProject);
                SelectedProject = null;
                Files.Clear();

                StatusMessage = "项目已删除";
                await _notificationService.ShowInfoAsync("信息", "项目已删除");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除项目失败");
            await _notificationService.ShowErrorAsync("错误", "删除项目失败");
        }
    }

    /// <summary>
    /// 导入文件
    /// </summary>
    [RelayCommand]
    private async Task ImportFiles()
    {
        try
        {
            if (SelectedProject == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择项目");
                return;
            }

            var filePaths = await _dialogService.OpenFileDialogAsync(
                "选择要导入的文件",
                "所有支持的文件|*.dcm;*.jpg;*.jpeg;*.png;*.bmp;*.json;*.xml|DICOM文件|*.dcm|图像文件|*.jpg;*.jpeg;*.png;*.bmp|标注文件|*.json;*.xml");

            if (!string.IsNullOrEmpty(filePaths))
            {
                await ImportFilesToProject(new[] { filePaths });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入文件失败");
            await _notificationService.ShowErrorAsync("错误", "导入文件失败");
        }
    }

    /// <summary>
    /// 导入文件夹
    /// </summary>
    [RelayCommand]
    private async Task ImportFolder()
    {
        try
        {
            if (SelectedProject == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择项目");
                return;
            }

            var folderPath = await _dialogService.SelectFolderDialogAsync("选择要导入的文件夹");
            if (!string.IsNullOrEmpty(folderPath))
            {
                await ImportFolderToProject(folderPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入文件夹失败");
            await _notificationService.ShowErrorAsync("错误", "导入文件夹失败");
        }
    }

    /// <summary>
    /// 导出文件
    /// </summary>
    [RelayCommand]
    private async Task ExportFiles()
    {
        try
        {
            if (SelectedFile == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择要导出的文件");
                return;
            }

            var folderPath = await _dialogService.SelectFolderDialogAsync("选择导出目标文件夹");
            if (!string.IsNullOrEmpty(folderPath))
            {
                await ExportFilesToFolder(new[] { SelectedFile }, folderPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出文件失败");
            await _notificationService.ShowErrorAsync("错误", "导出文件失败");
        }
    }

    /// <summary>
    /// 删除文件
    /// </summary>
    [RelayCommand]
    private async Task DeleteFile()
    {
        try
        {
            if (SelectedFile == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择要删除的文件");
                return;
            }

            var confirmed = await _dialogService.ShowConfirmationAsync(
                "确认删除", $"确定要删除文件 '{SelectedFile.Name}' 吗？");

            if (confirmed)
            {
                Files.Remove(SelectedFile);
                SelectedFile = null;
                UpdateStatistics();

                StatusMessage = "文件已删除";
                await _notificationService.ShowInfoAsync("信息", "文件已删除");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文件失败");
            await _notificationService.ShowErrorAsync("错误", "删除文件失败");
        }
    }

    /// <summary>
    /// 刷新文件列表
    /// </summary>
    [RelayCommand]
    private async Task RefreshFiles()
    {
        try
        {
            await LoadProjectFiles();
            StatusMessage = "文件列表已刷新";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新文件列表失败");
            await _notificationService.ShowErrorAsync("错误", "刷新文件列表失败");
        }
    }

    /// <summary>
    /// 清空搜索
    /// </summary>
    [RelayCommand]
    private void ClearSearch()
    {
        SearchText = "";
        StatusMessage = "搜索已清空";
    }

    /// <summary>
    /// 查看文件详情
    /// </summary>
    [RelayCommand]
    private async Task ViewFileDetails()
    {
        try
        {
            if (SelectedFile == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择文件");
                return;
            }

            // 这里应该显示文件详情对话框
            var details = $"文件名: {SelectedFile.Name}\n" +
                         $"大小: {FormatFileSize(SelectedFile.Size)}\n" +
                         $"类型: {SelectedFile.Type}\n" +
                         $"创建时间: {SelectedFile.CreatedTime:yyyy-MM-dd HH:mm:ss}\n" +
                         $"修改时间: {SelectedFile.ModifiedTime:yyyy-MM-dd HH:mm:ss}\n" +
                         $"路径: {SelectedFile.Path}";

            await _dialogService.ShowMessageAsync("文件详情", details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查看文件详情失败");
            await _notificationService.ShowErrorAsync("错误", "查看文件详情失败");
        }
    }

    /// <summary>
    /// 批量操作
    /// </summary>
    [RelayCommand]
    private async Task BatchOperation()
    {
        try
        {
            // 这里应该显示批量操作对话框
            await _notificationService.ShowInfoAsync("信息", "批量操作功能正在开发中...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量操作失败");
            await _notificationService.ShowErrorAsync("错误", "批量操作失败");
        }
    }

    /// <summary>
    /// 初始化默认数据
    /// </summary>
    private void InitializeDefaultData()
    {
        // 创建示例项目
        var sampleProject = new DatasetProject
        {
            Id = Guid.NewGuid(),
            Name = "示例医学影像项目",
            Description = "包含各种医学影像数据的示例项目",
            CreatedTime = DateTime.Now.AddDays(-7),
            ModifiedTime = DateTime.Now,
            Path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
                               "MedicalImageAnalysis", "Projects", "Sample")
        };

        Projects.Add(sampleProject);
        StatusMessage = "数据管理模块已初始化";
    }

    /// <summary>
    /// 加载项目文件
    /// </summary>
    private async Task LoadProjectFiles()
    {
        if (SelectedProject == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = "加载项目文件...";

            Files.Clear();

            // 模拟加载文件
            await Task.Delay(500);

            // 添加示例文件
            var sampleFiles = new[]
            {
                new Models.FileInfo
                {
                    Name = "brain_scan_001.dcm",
                    Path = Path.Combine(SelectedProject.Path, "brain_scan_001.dcm"),
                    Size = 2048576,
                    Type = "DICOM",
                    Extension = ".dcm",
                    CreatedTime = DateTime.Now.AddDays(-5),
                    ModifiedTime = DateTime.Now.AddDays(-5)
                },
                new Models.FileInfo
                {
                    Name = "chest_xray_002.jpg",
                    Path = Path.Combine(SelectedProject.Path, "chest_xray_002.jpg"),
                    Size = 512000,
                    Type = "JPEG",
                    Extension = ".jpg",
                    CreatedTime = DateTime.Now.AddDays(-3),
                    ModifiedTime = DateTime.Now.AddDays(-3)
                },
                new Models.FileInfo
                {
                    Name = "annotations.json",
                    Path = Path.Combine(SelectedProject.Path, "annotations.json"),
                    Size = 8192,
                    Type = "标注文件",
                    Extension = ".json",
                    CreatedTime = DateTime.Now.AddDays(-1),
                    ModifiedTime = DateTime.Now.AddDays(-1)
                }
            };

            foreach (var file in sampleFiles)
            {
                Files.Add(file);
            }

            UpdateStatistics();
            FilterAndSortFiles();

            StatusMessage = $"已加载 {Files.Count} 个文件";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载项目文件失败");
            await _notificationService.ShowErrorAsync("错误", "加载项目文件失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 从路径加载项目
    /// </summary>
    private async Task LoadProjectFromPath(string path)
    {
        try
        {
            var projectName = Path.GetFileName(path);
            var project = new DatasetProject
            {
                Id = Guid.NewGuid(),
                Name = projectName,
                Description = $"从 {path} 加载的项目",
                Path = path,
                CreatedTime = Directory.GetCreationTime(path),
                ModifiedTime = Directory.GetLastWriteTime(path)
            };

            Projects.Add(project);
            SelectedProject = project;

            StatusMessage = $"项目 '{projectName}' 加载成功";
            await _notificationService.ShowSuccessAsync("成功", "项目加载成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从路径加载项目失败: {Path}", path);
            throw;
        }
    }

    /// <summary>
    /// 导入文件到项目
    /// </summary>
    private async Task ImportFilesToProject(string[] filePaths)
    {
        try
        {
            IsLoading = true;
            IsProgressVisible = true;
            ProgressValue = 0;
            StatusMessage = "导入文件中...";

            for (int i = 0; i < filePaths.Length; i++)
            {
                var filePath = filePaths[i];
                var fileInfo = new System.IO.FileInfo(filePath);

                var file = new Models.FileInfo
                {
                    Name = fileInfo.Name,
                    Path = filePath,
                    Size = fileInfo.Length,
                    Type = GetFileType(fileInfo.Extension),
                    Extension = fileInfo.Extension,
                    CreatedTime = fileInfo.CreationTime,
                    ModifiedTime = fileInfo.LastWriteTime
                };

                Files.Add(file);

                ProgressValue = (double)(i + 1) / filePaths.Length * 100;
                await Task.Delay(100); // 模拟导入时间
            }

            UpdateStatistics();
            StatusMessage = $"成功导入 {filePaths.Length} 个文件";
            await _notificationService.ShowSuccessAsync("成功", "文件导入完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入文件失败");
            throw;
        }
        finally
        {
            IsLoading = false;
            IsProgressVisible = false;
        }
    }

    /// <summary>
    /// 导入文件夹到项目
    /// </summary>
    private async Task ImportFolderToProject(string folderPath)
    {
        try
        {
            var files = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories)
                               .Where(f => IsValidFileType(Path.GetExtension(f)))
                               .ToArray();

            await ImportFilesToProject(files);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入文件夹失败: {FolderPath}", folderPath);
            throw;
        }
    }

    /// <summary>
    /// 导出文件到文件夹
    /// </summary>
    private async Task ExportFilesToFolder(Models.FileInfo[] files, string targetFolder)
    {
        try
        {
            IsLoading = true;
            IsProgressVisible = true;
            ProgressValue = 0;
            StatusMessage = "导出文件中...";

            for (int i = 0; i < files.Length; i++)
            {
                var file = files[i];
                var targetPath = Path.Combine(targetFolder, file.Name);

                // 这里应该实现实际的文件复制
                await Task.Delay(100); // 模拟导出时间

                ProgressValue = (double)(i + 1) / files.Length * 100;
            }

            StatusMessage = $"成功导出 {files.Length} 个文件";
            await _notificationService.ShowSuccessAsync("成功", "文件导出完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出文件失败");
            throw;
        }
        finally
        {
            IsLoading = false;
            IsProgressVisible = false;
        }
    }

    /// <summary>
    /// 过滤和排序文件
    /// </summary>
    private void FilterAndSortFiles()
    {
        // 这里应该实现文件过滤和排序逻辑
        // 简化实现，仅更新状态消息
        var filteredCount = Files.Count;
        if (!string.IsNullOrEmpty(SearchText))
        {
            filteredCount = Files.Count(f => f.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
        }

        StatusMessage = $"显示 {filteredCount} 个文件";
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics()
    {
        TotalFileCount = Files.Count;
        TotalFileSize = Files.Sum(f => f.Size);
    }

    /// <summary>
    /// 获取文件类型
    /// </summary>
    private string GetFileType(string extension)
    {
        return extension.ToLower() switch
        {
            ".dcm" => "DICOM",
            ".jpg" or ".jpeg" => "JPEG",
            ".png" => "PNG",
            ".bmp" => "BMP",
            ".json" => "标注文件",
            ".xml" => "标注文件",
            _ => "其他"
        };
    }

    /// <summary>
    /// 检查是否为有效文件类型
    /// </summary>
    private bool IsValidFileType(string extension)
    {
        var validExtensions = new[] { ".dcm", ".jpg", ".jpeg", ".png", ".bmp", ".json", ".xml" };
        return validExtensions.Contains(extension.ToLower());
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

/// <summary>
/// 数据集项目模型
/// </summary>
public class DatasetProject
{
    public Guid Id { get; set; }
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public string Path { get; set; } = "";
    public DateTime CreatedTime { get; set; }
    public DateTime ModifiedTime { get; set; }
    public int FileCount { get; set; }
    public long TotalSize { get; set; }
}
