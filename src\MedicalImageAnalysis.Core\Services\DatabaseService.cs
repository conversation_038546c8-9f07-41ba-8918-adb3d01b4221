using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Data;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using System.Text.Json;

namespace MedicalImageAnalysis.Core.Services;

/// <summary>
/// 数据库服务实现
/// </summary>
public class DatabaseService : IDatabaseService
{
    private readonly MedicalImageDbContext _context;
    private readonly ILogger<DatabaseService> _logger;

    public DatabaseService(MedicalImageDbContext context, ILogger<DatabaseService> logger)
    {
        _context = context;
        _logger = logger;
    }

    #region 患者相关操作

    public async Task<Patient?> GetPatientAsync(Guid id)
    {
        try
        {
            return await _context.Patients
                .Include(p => p.Studies)
                .FirstOrDefaultAsync(p => p.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取患者信息失败: {PatientId}", id);
            throw;
        }
    }

    public async Task<Patient?> GetPatientByPatientIdAsync(string patientId)
    {
        try
        {
            return await _context.Patients
                .Include(p => p.Studies)
                .FirstOrDefaultAsync(p => p.PatientId == patientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据患者ID获取患者信息失败: {PatientId}", patientId);
            throw;
        }
    }

    public async Task<IEnumerable<Patient>> GetAllPatientsAsync()
    {
        try
        {
            return await _context.Patients
                .Include(p => p.Studies)
                .OrderBy(p => p.PatientName)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有患者信息失败");
            throw;
        }
    }

    public async Task<Patient> CreatePatientAsync(Patient patient)
    {
        try
        {
            _context.Patients.Add(patient);
            await _context.SaveChangesAsync();
            _logger.LogInformation("创建患者成功: {PatientId}", patient.PatientId);
            return patient;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建患者失败: {PatientId}", patient.PatientId);
            throw;
        }
    }

    public async Task<Patient> UpdatePatientAsync(Patient patient)
    {
        try
        {
            _context.Patients.Update(patient);
            await _context.SaveChangesAsync();
            _logger.LogInformation("更新患者成功: {PatientId}", patient.PatientId);
            return patient;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新患者失败: {PatientId}", patient.PatientId);
            throw;
        }
    }

    public async Task DeletePatientAsync(Guid id)
    {
        try
        {
            var patient = await _context.Patients.FindAsync(id);
            if (patient != null)
            {
                _context.Patients.Remove(patient);
                await _context.SaveChangesAsync();
                _logger.LogInformation("删除患者成功: {PatientId}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除患者失败: {PatientId}", id);
            throw;
        }
    }

    #endregion

    #region DICOM研究相关操作

    public async Task<DicomStudy?> GetStudyAsync(Guid id)
    {
        try
        {
            return await _context.Studies
                .Include(s => s.Patient)
                .Include(s => s.Series)
                    .ThenInclude(ser => ser.Instances)
                .Include(s => s.ProcessingResults)
                .FirstOrDefaultAsync(s => s.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取研究信息失败: {StudyId}", id);
            throw;
        }
    }

    public async Task<DicomStudy?> GetStudyByStudyInstanceUidAsync(string studyInstanceUid)
    {
        try
        {
            return await _context.Studies
                .Include(s => s.Patient)
                .Include(s => s.Series)
                    .ThenInclude(ser => ser.Instances)
                .Include(s => s.ProcessingResults)
                .FirstOrDefaultAsync(s => s.StudyInstanceUid == studyInstanceUid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据研究实例UID获取研究信息失败: {StudyInstanceUid}", studyInstanceUid);
            throw;
        }
    }

    public async Task<IEnumerable<DicomStudy>> GetStudiesByPatientIdAsync(Guid patientId)
    {
        try
        {
            return await _context.Studies
                .Include(s => s.Patient)
                .Include(s => s.Series)
                .Where(s => s.PatientId == patientId)
                .OrderByDescending(s => s.StudyDateTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据患者ID获取研究信息失败: {PatientId}", patientId);
            throw;
        }
    }

    public async Task<IEnumerable<DicomStudy>> GetAllStudiesAsync()
    {
        try
        {
            return await _context.Studies
                .Include(s => s.Patient)
                .Include(s => s.Series)
                .OrderByDescending(s => s.StudyDateTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有研究信息失败");
            throw;
        }
    }

    public async Task<DicomStudy> CreateStudyAsync(DicomStudy study)
    {
        try
        {
            _context.Studies.Add(study);
            await _context.SaveChangesAsync();
            _logger.LogInformation("创建研究成功: {StudyInstanceUid}", study.StudyInstanceUid);
            return study;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建研究失败: {StudyInstanceUid}", study.StudyInstanceUid);
            throw;
        }
    }

    public async Task<DicomStudy> UpdateStudyAsync(DicomStudy study)
    {
        try
        {
            study.UpdatedAt = DateTime.UtcNow;
            _context.Studies.Update(study);
            await _context.SaveChangesAsync();
            _logger.LogInformation("更新研究成功: {StudyInstanceUid}", study.StudyInstanceUid);
            return study;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新研究失败: {StudyInstanceUid}", study.StudyInstanceUid);
            throw;
        }
    }

    public async Task DeleteStudyAsync(Guid id)
    {
        try
        {
            var study = await _context.Studies.FindAsync(id);
            if (study != null)
            {
                _context.Studies.Remove(study);
                await _context.SaveChangesAsync();
                _logger.LogInformation("删除研究成功: {StudyId}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除研究失败: {StudyId}", id);
            throw;
        }
    }

    #endregion

    #region DICOM序列相关操作

    public async Task<DicomSeries?> GetSeriesAsync(Guid id)
    {
        try
        {
            return await _context.Series
                .Include(s => s.Study)
                .Include(s => s.Instances)
                    .ThenInclude(i => i.Annotations)
                .FirstOrDefaultAsync(s => s.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取序列信息失败: {SeriesId}", id);
            throw;
        }
    }

    public async Task<DicomSeries?> GetSeriesBySeriesInstanceUidAsync(string seriesInstanceUid)
    {
        try
        {
            return await _context.Series
                .Include(s => s.Study)
                .Include(s => s.Instances)
                    .ThenInclude(i => i.Annotations)
                .FirstOrDefaultAsync(s => s.SeriesInstanceUid == seriesInstanceUid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据序列实例UID获取序列信息失败: {SeriesInstanceUid}", seriesInstanceUid);
            throw;
        }
    }

    public async Task<IEnumerable<DicomSeries>> GetSeriesByStudyIdAsync(Guid studyId)
    {
        try
        {
            return await _context.Series
                .Include(s => s.Study)
                .Include(s => s.Instances)
                .Where(s => s.StudyId == studyId)
                .OrderBy(s => s.SeriesNumber)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据研究ID获取序列信息失败: {StudyId}", studyId);
            throw;
        }
    }

    public async Task<IEnumerable<DicomSeries>> GetAllSeriesAsync()
    {
        try
        {
            return await _context.Series
                .Include(s => s.Study)
                .Include(s => s.Instances)
                .OrderBy(s => s.SeriesDateTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有序列信息失败");
            throw;
        }
    }

    public async Task<DicomSeries> CreateSeriesAsync(DicomSeries series)
    {
        try
        {
            _context.Series.Add(series);
            await _context.SaveChangesAsync();
            _logger.LogInformation("创建序列成功: {SeriesInstanceUid}", series.SeriesInstanceUid);
            return series;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建序列失败: {SeriesInstanceUid}", series.SeriesInstanceUid);
            throw;
        }
    }

    public async Task<DicomSeries> UpdateSeriesAsync(DicomSeries series)
    {
        try
        {
            _context.Series.Update(series);
            await _context.SaveChangesAsync();
            _logger.LogInformation("更新序列成功: {SeriesInstanceUid}", series.SeriesInstanceUid);
            return series;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新序列失败: {SeriesInstanceUid}", series.SeriesInstanceUid);
            throw;
        }
    }

    public async Task DeleteSeriesAsync(Guid id)
    {
        try
        {
            var series = await _context.Series.FindAsync(id);
            if (series != null)
            {
                _context.Series.Remove(series);
                await _context.SaveChangesAsync();
                _logger.LogInformation("删除序列成功: {SeriesId}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除序列失败: {SeriesId}", id);
            throw;
        }
    }

    #endregion

    #region DICOM实例相关操作

    public async Task<DicomInstance?> GetInstanceAsync(Guid id)
    {
        try
        {
            return await _context.Instances
                .Include(i => i.Series)
                    .ThenInclude(s => s.Study)
                .Include(i => i.Annotations)
                .FirstOrDefaultAsync(i => i.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实例信息失败: {InstanceId}", id);
            throw;
        }
    }

    public async Task<DicomInstance?> GetInstanceBySopInstanceUidAsync(string sopInstanceUid)
    {
        try
        {
            return await _context.Instances
                .Include(i => i.Series)
                    .ThenInclude(s => s.Study)
                .Include(i => i.Annotations)
                .FirstOrDefaultAsync(i => i.SopInstanceUid == sopInstanceUid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据SOP实例UID获取实例信息失败: {SopInstanceUid}", sopInstanceUid);
            throw;
        }
    }

    public async Task<IEnumerable<DicomInstance>> GetInstancesBySeriesIdAsync(Guid seriesId)
    {
        try
        {
            return await _context.Instances
                .Include(i => i.Series)
                .Include(i => i.Annotations)
                .Where(i => i.SeriesId == seriesId)
                .OrderBy(i => i.InstanceNumber)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据序列ID获取实例信息失败: {SeriesId}", seriesId);
            throw;
        }
    }

    public async Task<IEnumerable<DicomInstance>> GetAllInstancesAsync()
    {
        try
        {
            return await _context.Instances
                .Include(i => i.Series)
                .Include(i => i.Annotations)
                .OrderBy(i => i.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有实例信息失败");
            throw;
        }
    }

    public async Task<DicomInstance> CreateInstanceAsync(DicomInstance instance)
    {
        try
        {
            _context.Instances.Add(instance);
            await _context.SaveChangesAsync();
            _logger.LogInformation("创建实例成功: {SopInstanceUid}", instance.SopInstanceUid);
            return instance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建实例失败: {SopInstanceUid}", instance.SopInstanceUid);
            throw;
        }
    }

    public async Task<DicomInstance> UpdateInstanceAsync(DicomInstance instance)
    {
        try
        {
            _context.Instances.Update(instance);
            await _context.SaveChangesAsync();
            _logger.LogInformation("更新实例成功: {SopInstanceUid}", instance.SopInstanceUid);
            return instance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新实例失败: {SopInstanceUid}", instance.SopInstanceUid);
            throw;
        }
    }

    public async Task DeleteInstanceAsync(Guid id)
    {
        try
        {
            var instance = await _context.Instances.FindAsync(id);
            if (instance != null)
            {
                _context.Instances.Remove(instance);
                await _context.SaveChangesAsync();
                _logger.LogInformation("删除实例成功: {InstanceId}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除实例失败: {InstanceId}", id);
            throw;
        }
    }

    #endregion

    #region 标注相关操作

    public async Task<Annotation?> GetAnnotationAsync(Guid id)
    {
        try
        {
            return await _context.Annotations
                .Include(a => a.Instance)
                    .ThenInclude(i => i.Series)
                        .ThenInclude(s => s.Study)
                .FirstOrDefaultAsync(a => a.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取标注信息失败: {AnnotationId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<Annotation>> GetAnnotationsByInstanceIdAsync(Guid instanceId)
    {
        try
        {
            return await _context.Annotations
                .Include(a => a.Instance)
                .Where(a => a.InstanceId == instanceId)
                .OrderBy(a => a.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据实例ID获取标注信息失败: {InstanceId}", instanceId);
            throw;
        }
    }

    public async Task<IEnumerable<Annotation>> GetAnnotationsByLabelAsync(string label)
    {
        try
        {
            return await _context.Annotations
                .Include(a => a.Instance)
                .Where(a => a.Label == label)
                .OrderBy(a => a.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据标签获取标注信息失败: {Label}", label);
            throw;
        }
    }

    public async Task<IEnumerable<Annotation>> GetAnnotationsBySourceAsync(AnnotationSource source)
    {
        try
        {
            return await _context.Annotations
                .Include(a => a.Instance)
                .Where(a => a.Source == source)
                .OrderBy(a => a.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据来源获取标注信息失败: {Source}", source);
            throw;
        }
    }

    public async Task<IEnumerable<Annotation>> GetAnnotationsByVerificationStatusAsync(VerificationStatus status)
    {
        try
        {
            return await _context.Annotations
                .Include(a => a.Instance)
                .Where(a => a.VerificationStatus == status)
                .OrderBy(a => a.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据验证状态获取标注信息失败: {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<Annotation>> GetAllAnnotationsAsync()
    {
        try
        {
            return await _context.Annotations
                .Include(a => a.Instance)
                .OrderBy(a => a.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有标注信息失败");
            throw;
        }
    }

    public async Task<Annotation> CreateAnnotationAsync(Annotation annotation)
    {
        try
        {
            _context.Annotations.Add(annotation);
            await _context.SaveChangesAsync();
            _logger.LogInformation("创建标注成功: {AnnotationId}", annotation.Id);
            return annotation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建标注失败: {AnnotationId}", annotation.Id);
            throw;
        }
    }

    public async Task<Annotation> UpdateAnnotationAsync(Annotation annotation)
    {
        try
        {
            annotation.UpdatedAt = DateTime.UtcNow;
            _context.Annotations.Update(annotation);
            await _context.SaveChangesAsync();
            _logger.LogInformation("更新标注成功: {AnnotationId}", annotation.Id);
            return annotation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新标注失败: {AnnotationId}", annotation.Id);
            throw;
        }
    }

    public async Task DeleteAnnotationAsync(Guid id)
    {
        try
        {
            var annotation = await _context.Annotations.FindAsync(id);
            if (annotation != null)
            {
                _context.Annotations.Remove(annotation);
                await _context.SaveChangesAsync();
                _logger.LogInformation("删除标注成功: {AnnotationId}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除标注失败: {AnnotationId}", id);
            throw;
        }
    }

    public async Task<int> GetAnnotationCountByInstanceIdAsync(Guid instanceId)
    {
        try
        {
            return await _context.Annotations
                .Where(a => a.InstanceId == instanceId)
                .CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实例标注数量失败: {InstanceId}", instanceId);
            throw;
        }
    }

    #endregion

    #region 处理结果相关操作

    public async Task<ProcessingResult?> GetProcessingResultAsync(Guid id)
    {
        try
        {
            return await _context.ProcessingResults
                .Include(pr => pr.Study)
                .FirstOrDefaultAsync(pr => pr.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取处理结果失败: {ResultId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<ProcessingResult>> GetProcessingResultsByStudyIdAsync(Guid studyId)
    {
        try
        {
            return await _context.ProcessingResults
                .Include(pr => pr.Study)
                .Where(pr => pr.StudyId == studyId)
                .OrderByDescending(pr => pr.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据研究ID获取处理结果失败: {StudyId}", studyId);
            throw;
        }
    }

    public async Task<IEnumerable<ProcessingResult>> GetProcessingResultsByTypeAsync(ProcessingType type)
    {
        try
        {
            return await _context.ProcessingResults
                .Include(pr => pr.Study)
                .Where(pr => pr.Type == type)
                .OrderByDescending(pr => pr.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据处理类型获取处理结果失败: {Type}", type);
            throw;
        }
    }

    public async Task<IEnumerable<ProcessingResult>> GetProcessingResultsByStatusAsync(ProcessingStatus status)
    {
        try
        {
            return await _context.ProcessingResults
                .Include(pr => pr.Study)
                .Where(pr => pr.Status == status)
                .OrderByDescending(pr => pr.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据处理状态获取处理结果失败: {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<ProcessingResult>> GetAllProcessingResultsAsync()
    {
        try
        {
            return await _context.ProcessingResults
                .Include(pr => pr.Study)
                .OrderByDescending(pr => pr.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有处理结果失败");
            throw;
        }
    }

    public async Task<ProcessingResult> CreateProcessingResultAsync(ProcessingResult result)
    {
        try
        {
            _context.ProcessingResults.Add(result);
            await _context.SaveChangesAsync();
            _logger.LogInformation("创建处理结果成功: {ResultId}", result.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建处理结果失败: {ResultId}", result.Id);
            throw;
        }
    }

    public async Task<ProcessingResult> UpdateProcessingResultAsync(ProcessingResult result)
    {
        try
        {
            result.UpdatedAt = DateTime.UtcNow;
            _context.ProcessingResults.Update(result);
            await _context.SaveChangesAsync();
            _logger.LogInformation("更新处理结果成功: {ResultId}", result.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新处理结果失败: {ResultId}", result.Id);
            throw;
        }
    }

    public async Task DeleteProcessingResultAsync(Guid id)
    {
        try
        {
            var result = await _context.ProcessingResults.FindAsync(id);
            if (result != null)
            {
                _context.ProcessingResults.Remove(result);
                await _context.SaveChangesAsync();
                _logger.LogInformation("删除处理结果成功: {ResultId}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除处理结果失败: {ResultId}", id);
            throw;
        }
    }

    #endregion

    #region 训练任务相关操作

    public async Task<TrainingJob?> GetTrainingJobAsync(Guid id)
    {
        try
        {
            return await _context.TrainingJobs.FindAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取训练任务失败: {JobId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<TrainingJob>> GetTrainingJobsByStatusAsync(TrainingJobStatus status)
    {
        try
        {
            return await _context.TrainingJobs
                .Where(tj => tj.Status == status)
                .OrderByDescending(tj => tj.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取训练任务失败: {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<TrainingJob>> GetAllTrainingJobsAsync()
    {
        try
        {
            return await _context.TrainingJobs
                .OrderByDescending(tj => tj.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有训练任务失败");
            throw;
        }
    }

    public async Task<TrainingJob> CreateTrainingJobAsync(TrainingJob job)
    {
        try
        {
            _context.TrainingJobs.Add(job);
            await _context.SaveChangesAsync();
            _logger.LogInformation("创建训练任务成功: {JobId}", job.Id);
            return job;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建训练任务失败: {JobId}", job.Id);
            throw;
        }
    }

    public async Task<TrainingJob> UpdateTrainingJobAsync(TrainingJob job)
    {
        try
        {
            job.UpdatedAt = DateTime.UtcNow;
            _context.TrainingJobs.Update(job);
            await _context.SaveChangesAsync();
            _logger.LogInformation("更新训练任务成功: {JobId}", job.Id);
            return job;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新训练任务失败: {JobId}", job.Id);
            throw;
        }
    }

    public async Task DeleteTrainingJobAsync(Guid id)
    {
        try
        {
            var job = await _context.TrainingJobs.FindAsync(id);
            if (job != null)
            {
                _context.TrainingJobs.Remove(job);
                await _context.SaveChangesAsync();
                _logger.LogInformation("删除训练任务成功: {JobId}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除训练任务失败: {JobId}", id);
            throw;
        }
    }

    #endregion

    #region 批量操作

    public async Task<IEnumerable<DicomInstance>> CreateInstancesBatchAsync(IEnumerable<DicomInstance> instances)
    {
        try
        {
            _context.Instances.AddRange(instances);
            await _context.SaveChangesAsync();
            _logger.LogInformation("批量创建实例成功: {Count}个", instances.Count());
            return instances;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量创建实例失败");
            throw;
        }
    }

    public async Task<IEnumerable<Annotation>> CreateAnnotationsBatchAsync(IEnumerable<Annotation> annotations)
    {
        try
        {
            _context.Annotations.AddRange(annotations);
            await _context.SaveChangesAsync();
            _logger.LogInformation("批量创建标注成功: {Count}个", annotations.Count());
            return annotations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量创建标注失败");
            throw;
        }
    }

    public async Task DeleteAnnotationsByInstanceIdAsync(Guid instanceId)
    {
        try
        {
            var annotations = await _context.Annotations
                .Where(a => a.InstanceId == instanceId)
                .ToListAsync();

            _context.Annotations.RemoveRange(annotations);
            await _context.SaveChangesAsync();
            _logger.LogInformation("删除实例标注成功: {InstanceId}, 删除数量: {Count}", instanceId, annotations.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除实例标注失败: {InstanceId}", instanceId);
            throw;
        }
    }

    #endregion

    #region 搜索和查询

    public async Task<IEnumerable<Patient>> SearchPatientsAsync(string searchTerm)
    {
        try
        {
            return await _context.Patients
                .Where(p => p.PatientName.Contains(searchTerm) ||
                           p.PatientId.Contains(searchTerm))
                .OrderBy(p => p.PatientName)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索患者失败: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<DicomStudy>> SearchStudiesAsync(string searchTerm)
    {
        try
        {
            return await _context.Studies
                .Include(s => s.Patient)
                .Where(s => s.StudyDescription.Contains(searchTerm) ||
                           s.StudyId.Contains(searchTerm) ||
                           s.Patient.PatientName.Contains(searchTerm))
                .OrderByDescending(s => s.StudyDateTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索研究失败: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<DicomInstance>> SearchInstancesAsync(string searchTerm)
    {
        try
        {
            return await _context.Instances
                .Include(i => i.Series)
                    .ThenInclude(s => s.Study)
                        .ThenInclude(st => st.Patient)
                .Where(i => i.SopInstanceUid.Contains(searchTerm) ||
                           i.Series.SeriesDescription.Contains(searchTerm) ||
                           i.Series.Study.StudyDescription.Contains(searchTerm) ||
                           i.Series.Study.Patient.PatientName.Contains(searchTerm))
                .OrderBy(i => i.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索实例失败: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<IEnumerable<Annotation>> SearchAnnotationsAsync(string searchTerm)
    {
        try
        {
            return await _context.Annotations
                .Include(a => a.Instance)
                .Where(a => a.Label.Contains(searchTerm) ||
                           a.Description.Contains(searchTerm) ||
                           a.CreatedBy.Contains(searchTerm))
                .OrderBy(a => a.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索标注失败: {SearchTerm}", searchTerm);
            throw;
        }
    }

    #endregion

    #region 统计信息

    public async Task<int> GetPatientCountAsync()
    {
        try
        {
            return await _context.Patients.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取患者数量失败");
            throw;
        }
    }

    public async Task<int> GetStudyCountAsync()
    {
        try
        {
            return await _context.Studies.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取研究数量失败");
            throw;
        }
    }

    public async Task<int> GetSeriesCountAsync()
    {
        try
        {
            return await _context.Series.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取序列数量失败");
            throw;
        }
    }

    public async Task<int> GetInstanceCountAsync()
    {
        try
        {
            return await _context.Instances.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实例数量失败");
            throw;
        }
    }

    public async Task<int> GetAnnotationCountAsync()
    {
        try
        {
            return await _context.Annotations.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取标注数量失败");
            throw;
        }
    }

    public async Task<Dictionary<string, int>> GetAnnotationCountByLabelAsync()
    {
        try
        {
            return await _context.Annotations
                .GroupBy(a => a.Label)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取标签标注数量失败");
            throw;
        }
    }

    public async Task<Dictionary<AnnotationSource, int>> GetAnnotationCountBySourceAsync()
    {
        try
        {
            return await _context.Annotations
                .GroupBy(a => a.Source)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取来源标注数量失败");
            throw;
        }
    }

    public async Task<Dictionary<VerificationStatus, int>> GetAnnotationCountByVerificationStatusAsync()
    {
        try
        {
            return await _context.Annotations
                .GroupBy(a => a.VerificationStatus)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取验证状态标注数量失败");
            throw;
        }
    }

    #endregion

    #region 数据库维护

    public async Task<bool> DatabaseExistsAsync()
    {
        try
        {
            return await _context.Database.CanConnectAsync();
        }
        catch
        {
            return false;
        }
    }

    public async Task CreateDatabaseAsync()
    {
        try
        {
            await _context.Database.EnsureCreatedAsync();
            _logger.LogInformation("数据库创建成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建数据库失败");
            throw;
        }
    }

    public async Task DeleteDatabaseAsync()
    {
        try
        {
            await _context.Database.EnsureDeletedAsync();
            _logger.LogInformation("数据库删除成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除数据库失败");
            throw;
        }
    }

    public async Task MigrateDatabaseAsync()
    {
        try
        {
            await _context.Database.MigrateAsync();
            _logger.LogInformation("数据库迁移成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库迁移失败");
            throw;
        }
    }

    public async Task<string> GetDatabaseVersionAsync()
    {
        try
        {
            // 简化实现：返回当前时间作为版本
            await Task.Delay(10);
            return DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取数据库版本失败");
            throw;
        }
    }

    #endregion

    #region 事务支持

    public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var result = await operation();
            await transaction.CommitAsync();
            return result;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task ExecuteInTransactionAsync(Func<Task> operation)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            await operation();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    #endregion

    #region 数据导入导出

    public async Task<int> ImportDicomDataAsync(string directoryPath, IProgress<string>? progress = null)
    {
        try
        {
            var dicomFiles = Directory.GetFiles(directoryPath, "*.dcm", SearchOption.AllDirectories)
                .Concat(Directory.GetFiles(directoryPath, "*.dicom", SearchOption.AllDirectories))
                .ToList();

            progress?.Report($"发现 {dicomFiles.Count} 个DICOM文件");

            // 这里应该使用DICOM服务来处理文件
            // 简化实现：仅返回文件数量
            await Task.Delay(1000);

            return dicomFiles.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入DICOM数据失败: {DirectoryPath}", directoryPath);
            throw;
        }
    }

    public async Task<string> ExportAnnotationsAsync(string outputPath, ExportFormat format)
    {
        try
        {
            var annotations = await GetAllAnnotationsAsync();
            var outputFile = Path.Combine(outputPath, $"annotations_{DateTime.Now:yyyyMMdd_HHmmss}.{format.ToString().ToLower()}");

            switch (format)
            {
                case ExportFormat.Json:
                    var json = JsonSerializer.Serialize(annotations, new JsonSerializerOptions { WriteIndented = true });
                    await File.WriteAllTextAsync(outputFile, json);
                    break;
                case ExportFormat.Csv:
                    // 简化的CSV导出
                    var csv = "Id,Label,Type,Confidence,CreatedAt\n" +
                             string.Join("\n", annotations.Select(a => $"{a.Id},{a.Label},{a.Type},{a.Confidence},{a.CreatedAt}"));
                    await File.WriteAllTextAsync(outputFile, csv);
                    break;
                default:
                    throw new NotSupportedException($"不支持的导出格式: {format}");
            }

            _logger.LogInformation("标注数据导出成功: {OutputFile}", outputFile);
            return outputFile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出标注数据失败: {OutputPath}", outputPath);
            throw;
        }
    }

    public async Task<string> ExportStudyDataAsync(Guid studyId, string outputPath)
    {
        try
        {
            var study = await GetStudyAsync(studyId);
            if (study == null)
                throw new ArgumentException($"研究不存在: {studyId}");

            var outputFile = Path.Combine(outputPath, $"study_{study.StudyId}_{DateTime.Now:yyyyMMdd_HHmmss}.json");
            var json = JsonSerializer.Serialize(study, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(outputFile, json);

            _logger.LogInformation("研究数据导出成功: {OutputFile}", outputFile);
            return outputFile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出研究数据失败: {StudyId}", studyId);
            throw;
        }
    }

    #endregion
}
