version: '3.8'

services:
  # 医学影像解析 API 服务 (开发环境)
  medical-image-analysis-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: medical-image-analysis-api-dev
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
    volumes:
      - ./data/logs:/app/logs
      - ./data/temp:/app/temp
      - ./data/output:/app/output
      - ./data/models:/app/models
      - ./Brain:/app/sample-data  # 挂载示例 DICOM 文件
    networks:
      - medical-dev-network
    restart: unless-stopped

networks:
  medical-dev-network:
    driver: bridge
