#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11 模型训练脚本
支持从头训练和预训练权重微调
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from datetime import datetime
import yaml
import torch
import numpy as np
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from ultralytics import YOLO
    from ultralytics.utils import LOGGER
    LOGGER.setLevel(logging.WARNING)  # 减少ultralytics的日志输出
except ImportError:
    logger.error("请安装ultralytics库: pip install ultralytics>=8.3.0")
    sys.exit(1)

class YOLOv11Trainer:
    """YOLOv11 训练器"""
    
    def __init__(self, config_path: str):
        """初始化训练器
        
        Args:
            config_path: 训练配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.output_dir = Path(self.config['output_directory'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置设备
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"使用设备: {self.device}")
        
    def _load_config(self) -> Dict[str, Any]:
        """加载训练配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"加载配置文件: {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def _create_dataset_yaml(self) -> str:
        """创建数据集配置文件"""
        dataset_config = {
            'path': self.config['dataset_path'],
            'train': 'train/images',
            'val': 'val/images',
            'test': 'test/images',
            'nc': self.config['num_classes'],
            'names': self.config['class_names']
        }
        
        yaml_path = self.output_dir / 'dataset.yaml'
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"创建数据集配置: {yaml_path}")
        return str(yaml_path)
    
    def train(self) -> Dict[str, Any]:
        """执行训练"""
        logger.info("开始训练 YOLOv11 模型")
        
        try:
            # 创建数据集配置
            dataset_yaml = self._create_dataset_yaml()
            
            # 初始化模型
            model_size = self.config.get('model_size', 'n')  # n, s, m, l, x
            if self.config.get('use_pretrained', True):
                model_path = f'yolo11{model_size}.pt'
                logger.info(f"使用预训练模型: {model_path}")
            else:
                model_path = f'yolo11{model_size}.yaml'
                logger.info(f"从头训练模型: {model_path}")
            
            model = YOLO(model_path)
            
            # 训练参数
            train_args = {
                'data': dataset_yaml,
                'epochs': self.config.get('epochs', 100),
                'batch': self.config.get('batch_size', 16),
                'imgsz': self.config.get('image_size', 640),
                'lr0': self.config.get('learning_rate', 0.01),
                'device': self.device,
                'project': str(self.output_dir.parent),
                'name': self.output_dir.name,
                'exist_ok': True,
                'save': True,
                'save_period': self.config.get('save_period', 10),
                'patience': self.config.get('patience', 50),
                'workers': self.config.get('workers', 8),
                'optimizer': self.config.get('optimizer', 'auto'),
                'verbose': True,
                'seed': self.config.get('seed', 0),
                'deterministic': True,
                'single_cls': self.config.get('single_cls', False),
                'rect': self.config.get('rect', False),
                'cos_lr': self.config.get('cos_lr', False),
                'close_mosaic': self.config.get('close_mosaic', 10),
                'resume': self.config.get('resume', False),
                'amp': self.config.get('amp', True),
                'fraction': self.config.get('fraction', 1.0),
                'profile': self.config.get('profile', False),
                'freeze': self.config.get('freeze', None),
                'multi_scale': self.config.get('multi_scale', False),
                'overlap_mask': self.config.get('overlap_mask', True),
                'mask_ratio': self.config.get('mask_ratio', 4),
                'dropout': self.config.get('dropout', 0.0),
                'val': self.config.get('val', True),
                'plots': self.config.get('plots', True),
                'save_json': True,
                'save_hybrid': self.config.get('save_hybrid', False),
                'conf': self.config.get('conf', None),
                'iou': self.config.get('iou', 0.7),
                'max_det': self.config.get('max_det', 300),
                'half': self.config.get('half', False),
                'dnn': self.config.get('dnn', False),
                'augment': self.config.get('augment', False),
                'agnostic_nms': self.config.get('agnostic_nms', False),
                'retina_masks': self.config.get('retina_masks', False),
                'embed': self.config.get('embed', None),
            }
            
            # 开始训练
            results = model.train(**train_args)
            
            # 保存训练结果
            result_data = {
                'success': True,
                'best_model_path': str(self.output_dir / 'weights' / 'best.pt'),
                'last_model_path': str(self.output_dir / 'weights' / 'last.pt'),
                'output_directory': str(self.output_dir),
                'training_time': None,  # 将由调用方计算
                'metrics': self._extract_metrics(results),
                'config': self.config
            }
            
            # 保存结果到JSON文件
            results_path = self.output_dir / 'results.json'
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"训练完成，结果保存到: {results_path}")
            return result_data
            
        except Exception as e:
            logger.error(f"训练失败: {e}")
            error_result = {
                'success': False,
                'error_message': str(e),
                'output_directory': str(self.output_dir)
            }
            
            # 保存错误结果
            results_path = self.output_dir / 'results.json'
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(error_result, f, indent=2, ensure_ascii=False)
            
            return error_result
    
    def _extract_metrics(self, results) -> Dict[str, Any]:
        """提取训练指标"""
        try:
            if hasattr(results, 'results_dict'):
                metrics = results.results_dict
            else:
                metrics = {}
            
            # 提取关键指标
            extracted = {
                'map50': float(metrics.get('metrics/mAP50(B)', 0.0)),
                'map50_95': float(metrics.get('metrics/mAP50-95(B)', 0.0)),
                'precision': float(metrics.get('metrics/precision(B)', 0.0)),
                'recall': float(metrics.get('metrics/recall(B)', 0.0)),
                'box_loss': float(metrics.get('train/box_loss', 0.0)),
                'cls_loss': float(metrics.get('train/cls_loss', 0.0)),
                'dfl_loss': float(metrics.get('train/dfl_loss', 0.0)),
                'fitness': float(metrics.get('fitness', 0.0))
            }
            
            return extracted
        except Exception as e:
            logger.warning(f"提取指标失败: {e}")
            return {}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLOv11 模型训练')
    parser.add_argument('--config', required=True, help='训练配置文件路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.config):
        logger.error(f"配置文件不存在: {args.config}")
        sys.exit(1)
    
    # 创建训练器并开始训练
    trainer = YOLOv11Trainer(args.config)
    result = trainer.train()
    
    # 输出结果
    if result['success']:
        logger.info("训练成功完成!")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        logger.error("训练失败!")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        sys.exit(1)

if __name__ == '__main__':
    main()
