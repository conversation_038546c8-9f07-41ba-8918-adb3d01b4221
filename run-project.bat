@echo off
echo ========================================
echo 医学影像解析系统启动脚本
echo ========================================

echo.
echo 检查 .NET 8 SDK 安装状态...
dotnet --version
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到 .NET 8 SDK
    echo 请确保已安装 .NET 8 SDK 并重新启动命令提示符
    pause
    exit /b 1
)

echo.
echo 当前 .NET 版本:
dotnet --version

echo.
echo 创建必要的目录...
if not exist "data" mkdir data
if not exist "data\logs" mkdir data\logs
if not exist "data\temp" mkdir data\temp
if not exist "data\output" mkdir data\output
if not exist "data\models" mkdir data\models
if not exist "data\cache" mkdir data\cache
if not exist "data\backup" mkdir data\backup

echo.
echo 恢复 NuGet 包...
dotnet restore
if %ERRORLEVEL% neq 0 (
    echo 错误: NuGet 包恢复失败
    pause
    exit /b 1
)

echo.
echo 构建解决方案...
dotnet build --configuration Debug
if %ERRORLEVEL% neq 0 (
    echo 错误: 项目构建失败
    echo 请检查编译错误并修复后重试
    pause
    exit /b 1
)

echo.
echo 构建成功！
echo.
echo 选择要运行的项目:
echo 1. API 服务 (推荐)
echo 2. Web 界面
echo 3. 同时运行 API 和 Web
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" goto run_api
if "%choice%"=="2" goto run_web
if "%choice%"=="3" goto run_both
echo 无效选择，默认运行 API 服务
goto run_api

:run_api
echo.
echo 启动 API 服务...
echo 访问地址: http://localhost:5000
echo Swagger 文档: http://localhost:5000/swagger
echo.
cd src\MedicalImageAnalysis.Api
dotnet run
goto end

:run_web
echo.
echo 启动 Web 界面...
echo 访问地址: http://localhost:5002
echo.
cd src\MedicalImageAnalysis.Web
dotnet run
goto end

:run_both
echo.
echo 同时启动 API 和 Web 服务...
echo API 访问地址: http://localhost:5000
echo Web 访问地址: http://localhost:5002
echo.
start "API Service" cmd /k "cd src\MedicalImageAnalysis.Api && dotnet run"
timeout /t 3 /nobreak > nul
start "Web Service" cmd /k "cd src\MedicalImageAnalysis.Web && dotnet run"
echo 两个服务已在新窗口中启动
goto end

:end
echo.
echo 按任意键退出...
pause > nul
