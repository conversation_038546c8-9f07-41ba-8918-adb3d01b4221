// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

// 抑制XML文档注释警告，因为这是一个内部项目，不需要完整的API文档
[assembly: SuppressMessage("Documentation", "CS1591:缺少对公共可见类型或成员的 XML 注释", Justification = "内部项目，不需要完整的XML文档注释")]
