namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// 标注相关的扩展实体和配置类
/// </summary>

/// <summary>
/// 检测结果
/// </summary>
public class Detection
{
    /// <summary>
    /// 检测标签
    /// </summary>
    public string Label { get; set; } = string.Empty;

    /// <summary>
    /// 置信度
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// 边界框
    /// </summary>
    public BoundingBox BoundingBox { get; set; } = new();

    /// <summary>
    /// 类别ID
    /// </summary>
    public int ClassId { get; set; }
}

/// <summary>
/// 批量检测结果
/// </summary>
public class BatchDetectionResult
{
    /// <summary>
    /// 图像路径
    /// </summary>
    public string ImagePath { get; set; } = string.Empty;

    /// <summary>
    /// 检测结果
    /// </summary>
    public List<Detection> Detections { get; set; } = new();

    /// <summary>
    /// 处理时间 (毫秒)
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// YOLO 模型信息
/// </summary>
public class YoloModelInfo
{
    /// <summary>
    /// 模型名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 模型版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 输入尺寸
    /// </summary>
    public (int Width, int Height) InputSize { get; set; }

    /// <summary>
    /// 类别数量
    /// </summary>
    public int ClassCount { get; set; }

    /// <summary>
    /// 类别名称
    /// </summary>
    public List<string> ClassNames { get; set; } = new();

    /// <summary>
    /// 模型文件大小 (字节)
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 数据集配置
/// </summary>
public class DatasetConfig
{
    /// <summary>
    /// 训练集路径
    /// </summary>
    public string TrainPath { get; set; } = string.Empty;

    /// <summary>
    /// 验证集路径
    /// </summary>
    public string ValidationPath { get; set; } = string.Empty;

    /// <summary>
    /// 测试集路径
    /// </summary>
    public string? TestPath { get; set; }

    /// <summary>
    /// 类别数量
    /// </summary>
    public int ClassCount { get; set; }

    /// <summary>
    /// 类别名称
    /// </summary>
    public List<string> ClassNames { get; set; } = new();
}

/// <summary>
/// 数据集验证结果
/// </summary>
public class DatasetValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 训练集图像数量
    /// </summary>
    public int TrainImageCount { get; set; }

    /// <summary>
    /// 验证集图像数量
    /// </summary>
    public int ValidationImageCount { get; set; }

    /// <summary>
    /// 测试集图像数量
    /// </summary>
    public int TestImageCount { get; set; }

    /// <summary>
    /// 总标注数量
    /// </summary>
    public int TotalAnnotationCount { get; set; }
}



/// <summary>
/// 训练增强配置
/// </summary>
public class TrainingAugmentationConfig : MedicalImageAnalysis.Core.Interfaces.DataAugmentationConfig
{
    /// <summary>
    /// Mosaic 增强概率
    /// </summary>
    public double MosaicProbability { get; set; } = 0.5;

    /// <summary>
    /// MixUp 增强概率
    /// </summary>
    public double MixUpProbability { get; set; } = 0.3;

    /// <summary>
    /// CutMix 增强概率
    /// </summary>
    public double CutMixProbability { get; set; } = 0.3;
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// mAP@0.5
    /// </summary>
    public double Map50 { get; set; }

    /// <summary>
    /// mAP@0.5:0.95
    /// </summary>
    public double Map5095 { get; set; }

    /// <summary>
    /// 精确率
    /// </summary>
    public double Precision { get; set; }

    /// <summary>
    /// 召回率
    /// </summary>
    public double Recall { get; set; }

    /// <summary>
    /// F1 分数
    /// </summary>
    public double F1Score { get; set; }

    /// <summary>
    /// 每个类别的 AP
    /// </summary>
    public Dictionary<string, double> ClassAP { get; set; } = new();

    /// <summary>
    /// 验证耗时 (秒)
    /// </summary>
    public double ValidationTimeSeconds { get; set; }
}

/// <summary>
/// 导出进度
/// </summary>
public class ExportProgress
{
    /// <summary>
    /// 当前处理的文件索引
    /// </summary>
    public int CurrentFileIndex { get; set; }

    /// <summary>
    /// 总文件数
    /// </summary>
    public int TotalFiles { get; set; }

    /// <summary>
    /// 当前文件名
    /// </summary>
    public string CurrentFileName { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public double ProgressPercentage => TotalFiles > 0 ? (double)CurrentFileIndex / TotalFiles * 100 : 0;
}

/// <summary>
/// 数据集导出结果
/// </summary>
public class DatasetExportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 输出路径
    /// </summary>
    public string OutputPath { get; set; } = string.Empty;

    /// <summary>
    /// 导出的图像数量
    /// </summary>
    public int ExportedImageCount { get; set; }

    /// <summary>
    /// 导出的标注数量
    /// </summary>
    public int ExportedAnnotationCount { get; set; }

    /// <summary>
    /// 导出耗时 (秒)
    /// </summary>
    public double ExportTimeSeconds { get; set; }
}

/// <summary>
/// 标注统计信息
/// </summary>
public class AnnotationStatistics
{
    /// <summary>
    /// 总标注数量
    /// </summary>
    public int TotalAnnotations { get; set; }

    /// <summary>
    /// 平均置信度
    /// </summary>
    public double AverageConfidence { get; set; }

    /// <summary>
    /// 最小置信度
    /// </summary>
    public double MinConfidence { get; set; }

    /// <summary>
    /// 最大置信度
    /// </summary>
    public double MaxConfidence { get; set; }

    /// <summary>
    /// 类别分布
    /// </summary>
    public Dictionary<string, int> ClassDistribution { get; set; } = new();

    /// <summary>
    /// 来源分布
    /// </summary>
    public Dictionary<string, int> SourceDistribution { get; set; } = new();

    /// <summary>
    /// 类型分布
    /// </summary>
    public Dictionary<string, int> TypeDistribution { get; set; } = new();

    /// <summary>
    /// 平均边界框面积
    /// </summary>
    public double AverageBoundingBoxArea { get; set; }

    /// <summary>
    /// 最小边界框面积
    /// </summary>
    public double MinBoundingBoxArea { get; set; }

    /// <summary>
    /// 最大边界框面积
    /// </summary>
    public double MaxBoundingBoxArea { get; set; }

    /// <summary>
    /// 验证状态分布
    /// </summary>
    public Dictionary<string, int> VerificationStatusDistribution { get; set; } = new();
}

/// <summary>
/// 标注异常
/// </summary>
public class AnnotationAnomaly
{
    /// <summary>
    /// 标注ID
    /// </summary>
    public Guid AnnotationId { get; set; }

    /// <summary>
    /// 异常类型
    /// </summary>
    public AnomalyType Type { get; set; }

    /// <summary>
    /// 异常描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 严重程度
    /// </summary>
    public AnomalySeverity Severity { get; set; }

    /// <summary>
    /// 检测时间
    /// </summary>
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 异常检测配置
/// </summary>
public class AnomalyDetectionConfig
{
    /// <summary>
    /// 置信度阈值
    /// </summary>
    public double ConfidenceThreshold { get; set; } = 0.1;

    /// <summary>
    /// 尺寸异常检测敏感度
    /// </summary>
    public double SizeAnomalySensitivity { get; set; } = 2.0;

    /// <summary>
    /// 重叠检测阈值
    /// </summary>
    public double OverlapThreshold { get; set; } = 0.7;
}

/// <summary>
/// 标注推荐
/// </summary>
public class AnnotationRecommendation
{
    /// <summary>
    /// 推荐的标签
    /// </summary>
    public string RecommendedLabel { get; set; } = string.Empty;

    /// <summary>
    /// 推荐的边界框
    /// </summary>
    public BoundingBox RecommendedBoundingBox { get; set; } = new();

    /// <summary>
    /// 推荐置信度
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// 推荐原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public RecommendationPriority Priority { get; set; }
}

/// <summary>
/// 标注推荐配置
/// </summary>
public class AnnotationRecommendationConfig
{
    /// <summary>
    /// 最小置信度阈值
    /// </summary>
    public double MinConfidenceThreshold { get; set; } = 0.5;

    /// <summary>
    /// 最大推荐数量
    /// </summary>
    public int MaxRecommendations { get; set; } = 10;

    /// <summary>
    /// 是否基于相似性推荐
    /// </summary>
    public bool EnableSimilarityBasedRecommendation { get; set; } = true;
}

/// <summary>
/// 标注合并配置
/// </summary>
public class AnnotationMergeConfig
{
    /// <summary>
    /// 重叠阈值
    /// </summary>
    public double OverlapThreshold { get; set; } = 0.5;

    /// <summary>
    /// 合并策略
    /// </summary>
    public MergeStrategy Strategy { get; set; } = MergeStrategy.HighestConfidence;

    /// <summary>
    /// 是否保留原始标注
    /// </summary>
    public bool KeepOriginalAnnotations { get; set; } = false;
}

/// <summary>
/// 异常类型枚举
/// </summary>
public enum AnomalyType
{
    LowConfidence = 1,
    UnusualSize = 2,
    Overlap = 3,
    OutOfBounds = 4,
    MissingLabel = 5
}

/// <summary>
/// 异常严重程度枚举
/// </summary>
public enum AnomalySeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// 推荐优先级枚举
/// </summary>
public enum RecommendationPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// 合并策略枚举
/// </summary>
public enum MergeStrategy
{
    HighestConfidence = 1,
    LargestArea = 2,
    Average = 3,
    Union = 4
}
