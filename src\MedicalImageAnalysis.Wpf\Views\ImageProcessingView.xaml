<UserControl x:Class="MedicalImageAnalysis.Wpf.Views.ImageProcessingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
            <materialDesign:PackIcon Kind="ImageEdit"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Margin="0,0,12,0"/>
            <TextBlock Text="医学影像处理"
                     FontSize="28"
                     FontWeight="Medium"
                     VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：图像显示区域 -->
            <materialDesign:Card Grid.Column="0"
                               Margin="0,0,8,0"
                               materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 图像工具栏 -->
                    <Border Grid.Row="0"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,0,0,1"
                          Padding="16,12">
                        <StackPanel Orientation="Horizontal">
                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,0,8,0"
                                  Click="OpenImage_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderOpen"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="打开图像"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"
                                  IsEnabled="{Binding HasImage}"
                                  Click="SaveImage_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ContentSave"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="保存"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,0,8,0"
                                  IsEnabled="{Binding HasImage}"
                                  Click="ResetImage_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Refresh"
                                                               Width="16" Height="16"
                                                               Margin="0,0,8,0"/>
                                        <TextBlock Text="重置"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- 图像显示区域 -->
                    <Border Grid.Row="1" Background="Black">
                        <ScrollViewer x:Name="ImageScrollViewer"
                                    HorizontalScrollBarVisibility="Auto"
                                    VerticalScrollBarVisibility="Auto">
                            <Grid>
                                <!-- 占位符 -->
                                <StackPanel x:Name="PlaceholderPanel"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Visibility="Visible">
                                    <materialDesign:PackIcon Kind="ImageOutline"
                                                           Width="64" Height="64"
                                                           Foreground="Gray"
                                                           HorizontalAlignment="Center"
                                                           Margin="0,0,0,16"/>
                                    <TextBlock Text="点击&quot;打开图像&quot;加载医学影像文件"
                                             Foreground="Gray"
                                             HorizontalAlignment="Center"
                                             FontSize="16"/>
                                    <TextBlock Text="支持格式：DICOM (.dcm), PNG, JPEG, BMP"
                                             Foreground="DarkGray"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Margin="0,8,0,0"/>
                                </StackPanel>

                                <!-- 图像显示 -->
                                <Image x:Name="MainImage"
                                     Visibility="Collapsed"
                                     Stretch="Uniform"
                                     RenderOptions.BitmapScalingMode="HighQuality"/>
                            </Grid>
                        </ScrollViewer>
                    </Border>

                    <!-- 图像信息栏 -->
                    <Border Grid.Row="2"
                          Background="{DynamicResource MaterialDesignCardBackground}"
                          BorderBrush="{DynamicResource MaterialDesignDivider}"
                          BorderThickness="0,1,0,0"
                          Padding="16,8">
                        <StackPanel x:Name="ImageInfoPanel"
                                  Orientation="Horizontal"
                                  Visibility="Collapsed">
                            <TextBlock x:Name="ImageInfoText"
                                     Text="图像信息将在此显示"
                                     FontSize="12"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </materialDesign:Card>

            <!-- 右侧：处理控制面板 -->
            <materialDesign:Card Grid.Column="1"
                               Margin="8,0,0,0"
                               materialDesign:ElevationAssist.Elevation="Dp2">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock Text="图像处理工具"
                                 FontSize="18"
                                 FontWeight="Medium"
                                 Margin="0,0,0,16"/>

                        <!-- 基本调整 -->
                        <Expander Header="基本调整" IsExpanded="True" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <!-- 亮度调整 -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock Text="亮度" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="BrightnessSlider"
                                          Minimum="-100"
                                          Maximum="100"
                                          Value="0"
                                          TickFrequency="10"
                                          IsSnapToTickEnabled="True"
                                          ValueChanged="BrightnessSlider_ValueChanged"/>
                                    <TextBlock Text="{Binding ElementName=BrightnessSlider, Path=Value, StringFormat=F0}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- 对比度调整 -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock Text="对比度" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="ContrastSlider"
                                          Minimum="-100"
                                          Maximum="100"
                                          Value="0"
                                          TickFrequency="10"
                                          IsSnapToTickEnabled="True"
                                          ValueChanged="ContrastSlider_ValueChanged"/>
                                    <TextBlock Text="{Binding ElementName=ContrastSlider, Path=Value, StringFormat=F0}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>

                                <!-- 伽马校正 -->
                                <StackPanel Margin="0,0,0,16">
                                    <TextBlock Text="伽马校正" FontWeight="Medium" Margin="0,0,0,8"/>
                                    <Slider x:Name="GammaSlider"
                                          Minimum="0.1"
                                          Maximum="3.0"
                                          Value="1.0"
                                          TickFrequency="0.1"
                                          IsSnapToTickEnabled="True"
                                          ValueChanged="GammaSlider_ValueChanged"/>
                                    <TextBlock Text="{Binding ElementName=GammaSlider, Path=Value, StringFormat=F1}"
                                             HorizontalAlignment="Center"
                                             FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>
                            </StackPanel>
                        </Expander>

                        <!-- 滤波处理 -->
                        <Expander Header="滤波处理" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <Button Content="高斯模糊"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyGaussianBlur_Click"/>
                                <Button Content="锐化"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplySharpen_Click"/>
                                <Button Content="边缘检测"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyEdgeDetection_Click"/>
                                <Button Content="降噪"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyDenoising_Click"/>
                            </StackPanel>
                        </Expander>

                        <!-- 直方图处理 -->
                        <Expander Header="直方图处理" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <Button Content="直方图均衡化"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyHistogramEqualization_Click"/>
                                <Button Content="自适应直方图均衡化"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyAdaptiveHistogramEqualization_Click"/>
                            </StackPanel>
                        </Expander>

                        <!-- 形态学操作 -->
                        <Expander Header="形态学操作" Margin="0,0,0,8">
                            <StackPanel Margin="16,8,0,8">
                                <Button Content="腐蚀"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyErosion_Click"/>
                                <Button Content="膨胀"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyDilation_Click"/>
                                <Button Content="开运算"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyOpening_Click"/>
                                <Button Content="闭运算"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Margin="0,4"
                                      Click="ApplyClosing_Click"/>
                            </StackPanel>
                        </Expander>

                        <!-- 操作按钮 -->
                        <StackPanel Margin="0,24,0,0">
                            <Button Content="应用所有更改"
                                  Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,4"
                                  Click="ApplyAllChanges_Click"/>
                            <Button Content="重置所有参数"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4"
                                  Click="ResetAllParameters_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
