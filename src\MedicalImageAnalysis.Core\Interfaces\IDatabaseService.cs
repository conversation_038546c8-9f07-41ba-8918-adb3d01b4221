using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 数据库服务接口
/// </summary>
public interface IDatabaseService
{
    // 患者相关操作
    Task<Patient?> GetPatientAsync(Guid id);
    Task<Patient?> GetPatientByPatientIdAsync(string patientId);
    Task<IEnumerable<Patient>> GetAllPatientsAsync();
    Task<Patient> CreatePatientAsync(Patient patient);
    Task<Patient> UpdatePatientAsync(Patient patient);
    Task DeletePatientAsync(Guid id);

    // DICOM研究相关操作
    Task<DicomStudy?> GetStudyAsync(Guid id);
    Task<DicomStudy?> GetStudyByStudyInstanceUidAsync(string studyInstanceUid);
    Task<IEnumerable<DicomStudy>> GetStudiesByPatientIdAsync(Guid patientId);
    Task<IEnumerable<DicomStudy>> GetAllStudiesAsync();
    Task<DicomStudy> CreateStudyAsync(DicomStudy study);
    Task<DicomStudy> UpdateStudyAsync(DicomStudy study);
    Task DeleteStudyAsync(Guid id);

    // DICOM序列相关操作
    Task<DicomSeries?> GetSeriesAsync(Guid id);
    Task<DicomSeries?> GetSeriesBySeriesInstanceUidAsync(string seriesInstanceUid);
    Task<IEnumerable<DicomSeries>> GetSeriesByStudyIdAsync(Guid studyId);
    Task<IEnumerable<DicomSeries>> GetAllSeriesAsync();
    Task<DicomSeries> CreateSeriesAsync(DicomSeries series);
    Task<DicomSeries> UpdateSeriesAsync(DicomSeries series);
    Task DeleteSeriesAsync(Guid id);

    // DICOM实例相关操作
    Task<DicomInstance?> GetInstanceAsync(Guid id);
    Task<DicomInstance?> GetInstanceBySopInstanceUidAsync(string sopInstanceUid);
    Task<IEnumerable<DicomInstance>> GetInstancesBySeriesIdAsync(Guid seriesId);
    Task<IEnumerable<DicomInstance>> GetAllInstancesAsync();
    Task<DicomInstance> CreateInstanceAsync(DicomInstance instance);
    Task<DicomInstance> UpdateInstanceAsync(DicomInstance instance);
    Task DeleteInstanceAsync(Guid id);

    // 标注相关操作
    Task<Annotation?> GetAnnotationAsync(Guid id);
    Task<IEnumerable<Annotation>> GetAnnotationsByInstanceIdAsync(Guid instanceId);
    Task<IEnumerable<Annotation>> GetAnnotationsByLabelAsync(string label);
    Task<IEnumerable<Annotation>> GetAnnotationsBySourceAsync(AnnotationSource source);
    Task<IEnumerable<Annotation>> GetAnnotationsByVerificationStatusAsync(VerificationStatus status);
    Task<IEnumerable<Annotation>> GetAllAnnotationsAsync();
    Task<Annotation> CreateAnnotationAsync(Annotation annotation);
    Task<Annotation> UpdateAnnotationAsync(Annotation annotation);
    Task DeleteAnnotationAsync(Guid id);
    Task<int> GetAnnotationCountByInstanceIdAsync(Guid instanceId);

    // 处理结果相关操作
    Task<ProcessingResult?> GetProcessingResultAsync(Guid id);
    Task<IEnumerable<ProcessingResult>> GetProcessingResultsByStudyIdAsync(Guid studyId);
    Task<IEnumerable<ProcessingResult>> GetProcessingResultsByTypeAsync(ProcessingType type);
    Task<IEnumerable<ProcessingResult>> GetProcessingResultsByStatusAsync(ProcessingStatus status);
    Task<IEnumerable<ProcessingResult>> GetAllProcessingResultsAsync();
    Task<ProcessingResult> CreateProcessingResultAsync(ProcessingResult result);
    Task<ProcessingResult> UpdateProcessingResultAsync(ProcessingResult result);
    Task DeleteProcessingResultAsync(Guid id);

    // 训练任务相关操作
    Task<TrainingJob?> GetTrainingJobAsync(Guid id);
    Task<IEnumerable<TrainingJob>> GetTrainingJobsByStatusAsync(TrainingJobStatus status);
    Task<IEnumerable<TrainingJob>> GetAllTrainingJobsAsync();
    Task<TrainingJob> CreateTrainingJobAsync(TrainingJob job);
    Task<TrainingJob> UpdateTrainingJobAsync(TrainingJob job);
    Task DeleteTrainingJobAsync(Guid id);

    // 批量操作
    Task<IEnumerable<DicomInstance>> CreateInstancesBatchAsync(IEnumerable<DicomInstance> instances);
    Task<IEnumerable<Annotation>> CreateAnnotationsBatchAsync(IEnumerable<Annotation> annotations);
    Task DeleteAnnotationsByInstanceIdAsync(Guid instanceId);

    // 搜索和查询
    Task<IEnumerable<Patient>> SearchPatientsAsync(string searchTerm);
    Task<IEnumerable<DicomStudy>> SearchStudiesAsync(string searchTerm);
    Task<IEnumerable<DicomInstance>> SearchInstancesAsync(string searchTerm);
    Task<IEnumerable<Annotation>> SearchAnnotationsAsync(string searchTerm);

    // 统计信息
    Task<int> GetPatientCountAsync();
    Task<int> GetStudyCountAsync();
    Task<int> GetSeriesCountAsync();
    Task<int> GetInstanceCountAsync();
    Task<int> GetAnnotationCountAsync();
    Task<Dictionary<string, int>> GetAnnotationCountByLabelAsync();
    Task<Dictionary<AnnotationSource, int>> GetAnnotationCountBySourceAsync();
    Task<Dictionary<VerificationStatus, int>> GetAnnotationCountByVerificationStatusAsync();

    // 数据库维护
    Task<bool> DatabaseExistsAsync();
    Task CreateDatabaseAsync();
    Task DeleteDatabaseAsync();
    Task MigrateDatabaseAsync();
    Task<string> GetDatabaseVersionAsync();

    // 事务支持
    Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation);
    Task ExecuteInTransactionAsync(Func<Task> operation);

    // 数据导入导出
    Task<int> ImportDicomDataAsync(string directoryPath, IProgress<string>? progress = null);
    Task<string> ExportAnnotationsAsync(string outputPath, ExportFormat format);
    Task<string> ExportStudyDataAsync(Guid studyId, string outputPath);
}

/// <summary>
/// 导出格式枚举
/// </summary>
public enum ExportFormat
{
    Json = 1,
    Xml = 2,
    Csv = 3,
    Yolo = 4,
    Coco = 5,
    PascalVoc = 6
}
