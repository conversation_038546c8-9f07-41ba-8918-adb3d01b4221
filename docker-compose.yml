version: '3.8'

services:
  # 医学影像解析主应用 (API + Web)
  medical-image-analysis:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: medical-image-analysis
    ports:
      - "5000:5000"  # API端口
      - "5001:5001"  # Web端口
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5000;http://+:5001
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=MedicalImageAnalysisDb;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;
      - MedicalImageAnalysis__Python__Executable=python
      - MedicalImageAnalysis__Models__ModelDirectory=/app/data/models
      - MedicalImageAnalysis__TempDirectory=/app/data/temp
      - MedicalImageAnalysis__OutputDirectory=/app/data/output
      - MedicalImageAnalysis__DICOM__StoragePath=/app/data/dicom
    volumes:
      - medical_data:/app/data
      - medical_logs:/app/data/logs
    depends_on:
      - sqlserver
      - redis
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health", "&&", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # SQL Server数据库
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: medical-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: medical-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - medical-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass YourRedisPassword
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: medical-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - medical-image-analysis
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch (用于日志聚合)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: medical-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana (日志可视化)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: medical-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 监控 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: medical-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - medical-network
    restart: unless-stopped

  # Grafana (监控可视化)
  grafana:
    image: grafana/grafana:latest
    container_name: medical-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - medical-network
    restart: unless-stopped

  # MinIO (对象存储)
  minio:
    image: minio/minio:latest
    container_name: medical-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  medical-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  medical_data:
    driver: local
  medical_logs:
    driver: local
  sqlserver_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  minio_data:
    driver: local
