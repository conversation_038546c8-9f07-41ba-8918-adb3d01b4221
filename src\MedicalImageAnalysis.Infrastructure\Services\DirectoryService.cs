using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 目录管理服务实现
/// </summary>
public class DirectoryService : IDirectoryService
{
    private readonly ILogger<DirectoryService> _logger;
    private readonly IConfiguration _configuration;
    private readonly SystemDirectories _systemDirectories;

    public DirectoryService(ILogger<DirectoryService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _systemDirectories = InitializeSystemDirectories();
    }

    /// <summary>
    /// 获取系统目录信息
    /// </summary>
    public async Task<SystemDirectories> GetSystemDirectoriesAsync()
    {
        await Task.CompletedTask; // 异步占位符
        
        // 确保所有目录存在
        await EnsureDirectoriesExistAsync();
        
        return _systemDirectories;
    }

    /// <summary>
    /// 打开指定目录
    /// </summary>
    public async Task<bool> OpenDirectoryAsync(string directoryPath)
    {
        await Task.CompletedTask; // 异步占位符

        try
        {
            if (!Directory.Exists(directoryPath))
            {
                _logger.LogWarning("尝试打开不存在的目录: {DirectoryPath}", directoryPath);
                return false;
            }

            _logger.LogInformation("打开目录: {DirectoryPath}", directoryPath);

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = directoryPath,
                    UseShellExecute = true
                });
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "open",
                    Arguments = directoryPath,
                    UseShellExecute = true
                });
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "xdg-open",
                    Arguments = directoryPath,
                    UseShellExecute = true
                });
            }
            else
            {
                _logger.LogWarning("不支持的操作系统平台");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开目录失败: {DirectoryPath}", directoryPath);
            return false;
        }
    }

    /// <summary>
    /// 获取目录内容
    /// </summary>
    public async Task<DirectoryContent> GetDirectoryContentAsync(string directoryPath, bool includeSubdirectories = false)
    {
        await Task.CompletedTask; // 异步占位符

        var content = new DirectoryContent
        {
            DirectoryPath = directoryPath
        };

        try
        {
            if (!Directory.Exists(directoryPath))
            {
                _logger.LogWarning("目录不存在: {DirectoryPath}", directoryPath);
                return content;
            }

            var directoryInfo = new System.IO.DirectoryInfo(directoryPath);
            content.LastModified = directoryInfo.LastWriteTime;

            // 获取文件信息
            var files = directoryInfo.GetFiles();
            foreach (var file in files)
            {
                content.Files.Add(new Core.Interfaces.FileInfo
                {
                    Name = file.Name,
                    FullPath = file.FullName,
                    Size = file.Length,
                    Extension = file.Extension,
                    CreatedTime = file.CreationTime,
                    LastModified = file.LastWriteTime,
                    FileType = GetFileType(file.Extension),
                    IsReadOnly = file.IsReadOnly
                });
                content.TotalSize += file.Length;
            }

            content.TotalFiles = files.Length;

            // 获取子目录信息
            if (includeSubdirectories)
            {
                var subdirectories = directoryInfo.GetDirectories();
                foreach (var subdir in subdirectories)
                {
                    var subdirSize = await GetDirectorySizeAsync(subdir.FullName);
                    var fileCount = Directory.GetFiles(subdir.FullName, "*", SearchOption.AllDirectories).Length;
                    var subdirCount = Directory.GetDirectories(subdir.FullName, "*", SearchOption.AllDirectories).Length;

                    content.Subdirectories.Add(new Core.Interfaces.DirectoryInfo
                    {
                        Name = subdir.Name,
                        FullPath = subdir.FullName,
                        CreatedTime = subdir.CreationTime,
                        LastModified = subdir.LastWriteTime,
                        FileCount = fileCount,
                        SubdirectoryCount = subdirCount,
                        Size = subdirSize
                    });
                    content.TotalSize += subdirSize;
                }

                content.TotalDirectories = subdirectories.Length;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取目录内容失败: {DirectoryPath}", directoryPath);
        }

        return content;
    }

    /// <summary>
    /// 创建目录
    /// </summary>
    public async Task<bool> CreateDirectoryAsync(string directoryPath)
    {
        await Task.CompletedTask; // 异步占位符

        try
        {
            if (Directory.Exists(directoryPath))
            {
                _logger.LogInformation("目录已存在: {DirectoryPath}", directoryPath);
                return true;
            }

            Directory.CreateDirectory(directoryPath);
            _logger.LogInformation("成功创建目录: {DirectoryPath}", directoryPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建目录失败: {DirectoryPath}", directoryPath);
            return false;
        }
    }

    /// <summary>
    /// 删除目录
    /// </summary>
    public async Task<bool> DeleteDirectoryAsync(string directoryPath, bool recursive = false)
    {
        await Task.CompletedTask; // 异步占位符

        try
        {
            if (!Directory.Exists(directoryPath))
            {
                _logger.LogWarning("目录不存在: {DirectoryPath}", directoryPath);
                return true;
            }

            Directory.Delete(directoryPath, recursive);
            _logger.LogInformation("成功删除目录: {DirectoryPath}", directoryPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除目录失败: {DirectoryPath}", directoryPath);
            return false;
        }
    }

    /// <summary>
    /// 获取目录大小
    /// </summary>
    public async Task<long> GetDirectorySizeAsync(string directoryPath)
    {
        await Task.CompletedTask; // 异步占位符

        try
        {
            if (!Directory.Exists(directoryPath))
            {
                return 0;
            }

            var directoryInfo = new System.IO.DirectoryInfo(directoryPath);
            return directoryInfo.EnumerateFiles("*", SearchOption.AllDirectories).Sum(file => file.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取目录大小失败: {DirectoryPath}", directoryPath);
            return 0;
        }
    }

    /// <summary>
    /// 清理临时文件
    /// </summary>
    public async Task<int> CleanupTempFilesAsync(int olderThanDays = 7)
    {
        await Task.CompletedTask; // 异步占位符

        var cleanedCount = 0;
        var cutoffDate = DateTime.Now.AddDays(-olderThanDays);

        try
        {
            var tempDirectory = _systemDirectories.TempDirectory;
            if (!Directory.Exists(tempDirectory))
            {
                return 0;
            }

            var files = Directory.GetFiles(tempDirectory, "*", SearchOption.AllDirectories);
            foreach (var file in files)
            {
                try
                {
                    var fileInfo = new System.IO.FileInfo(file);
                    if (fileInfo.LastWriteTime < cutoffDate)
                    {
                        File.Delete(file);
                        cleanedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "删除临时文件失败: {FilePath}", file);
                }
            }

            _logger.LogInformation("清理了 {Count} 个临时文件", cleanedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理临时文件失败");
        }

        return cleanedCount;
    }

    /// <summary>
    /// 获取磁盘使用情况
    /// </summary>
    public async Task<DiskUsage> GetDiskUsageAsync()
    {
        await Task.CompletedTask; // 异步占位符

        var diskUsage = new DiskUsage();

        try
        {
            var drives = System.IO.DriveInfo.GetDrives();
            foreach (var drive in drives)
            {
                if (drive.IsReady)
                {
                    diskUsage.Drives.Add(new Core.Interfaces.DriveInfo
                    {
                        Name = drive.Name,
                        DriveType = drive.DriveType.ToString(),
                        FileSystem = drive.DriveFormat,
                        TotalSpace = drive.TotalSize,
                        FreeSpace = drive.TotalFreeSpace
                    });

                    diskUsage.TotalSpace += drive.TotalSize;
                    diskUsage.FreeSpace += drive.TotalFreeSpace;
                }
            }

            diskUsage.UsedSpace = diskUsage.TotalSpace - diskUsage.FreeSpace;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取磁盘使用情况失败");
        }

        return diskUsage;
    }

    #region 私有方法

    /// <summary>
    /// 初始化系统目录
    /// </summary>
    private SystemDirectories InitializeSystemDirectories()
    {
        var baseDirectory = AppContext.BaseDirectory;
        var dataDirectory = _configuration["MedicalImageAnalysis:DataDirectory"] ?? "data";
        
        return new SystemDirectories
        {
            DataDirectory = Path.Combine(baseDirectory, dataDirectory),
            LogsDirectory = Path.Combine(baseDirectory, dataDirectory, "logs"),
            TempDirectory = Path.Combine(baseDirectory, dataDirectory, "temp"),
            OutputDirectory = Path.Combine(baseDirectory, dataDirectory, "output"),
            ModelsDirectory = Path.Combine(baseDirectory, dataDirectory, "models"),
            SampleDataDirectory = Path.Combine(baseDirectory, "Brain"),
            ConfigDirectory = Path.Combine(baseDirectory, "config"),
            BackupDirectory = Path.Combine(baseDirectory, dataDirectory, "backup"),
            CacheDirectory = Path.Combine(baseDirectory, dataDirectory, "cache"),
            ScriptsDirectory = Path.Combine(baseDirectory, "scripts")
        };
    }

    /// <summary>
    /// 确保目录存在
    /// </summary>
    private async Task EnsureDirectoriesExistAsync()
    {
        var directories = new[]
        {
            _systemDirectories.DataDirectory,
            _systemDirectories.LogsDirectory,
            _systemDirectories.TempDirectory,
            _systemDirectories.OutputDirectory,
            _systemDirectories.ModelsDirectory,
            _systemDirectories.ConfigDirectory,
            _systemDirectories.BackupDirectory,
            _systemDirectories.CacheDirectory
        };

        foreach (var directory in directories)
        {
            await CreateDirectoryAsync(directory);
        }
    }

    /// <summary>
    /// 获取文件类型
    /// </summary>
    private static string GetFileType(string extension)
    {
        return extension.ToLowerInvariant() switch
        {
            ".dcm" or ".dicom" => "DICOM 文件",
            ".png" => "PNG 图像",
            ".jpg" or ".jpeg" => "JPEG 图像",
            ".bmp" => "BMP 图像",
            ".tiff" or ".tif" => "TIFF 图像",
            ".webp" => "WebP 图像",
            ".txt" => "文本文件",
            ".json" => "JSON 文件",
            ".xml" => "XML 文件",
            ".yaml" or ".yml" => "YAML 文件",
            ".log" => "日志文件",
            ".pt" or ".pth" => "PyTorch 模型",
            ".onnx" => "ONNX 模型",
            ".zip" => "压缩文件",
            ".pdf" => "PDF 文档",
            _ => "未知类型"
        };
    }

    #endregion
}
