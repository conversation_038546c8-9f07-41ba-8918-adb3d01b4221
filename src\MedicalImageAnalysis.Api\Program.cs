using MedicalImageAnalysis.Application.Services;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Infrastructure.Services;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.IISIntegration;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 配置 Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/medical-image-analysis-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// 添加服务到容器
builder.Services.AddControllers();

// 添加 API 文档
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "医学影像解析系统 API",
        Version = "v1",
        Description = "基于 .NET 8 的现代化医学影像解析系统 API",
        Contact = new Microsoft.OpenApi.Models.OpenApiContact
        {
            Name = "开发团队",
            Email = "<EMAIL>"
        }
    });

    // 包含 XML 注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// 添加 CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 注册应用服务
builder.Services.AddScoped<IDicomService, DicomService>();
builder.Services.AddScoped<IImageProcessingService, ImageProcessingService>();
builder.Services.AddScoped<IAnnotationService, AnnotationService>();
builder.Services.AddScoped<IDirectoryService, DirectoryService>();
builder.Services.AddScoped<StudyProcessingService>();

// 注册 YOLO 服务的占位符实现
builder.Services.AddScoped<IYoloService, YoloServicePlaceholder>();

// 配置文件上传限制
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 500 * 1024 * 1024; // 500MB
});

builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = 500 * 1024 * 1024; // 500MB
});

var app = builder.Build();

// 配置 HTTP 请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "医学影像解析系统 API v1");
        c.RoutePrefix = string.Empty; // 设置 Swagger UI 为根路径
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();
app.MapControllers();

// 添加健康检查端点
app.MapGet("/health", () => new
{
    status = "healthy",
    timestamp = DateTime.UtcNow,
    version = "1.0.0"
});

// 添加根路径重定向到 Swagger
app.MapGet("/", () => Results.Redirect("/swagger"));

try
{
    Log.Information("启动医学影像解析系统 API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "应用启动失败");
}
finally
{
    Log.CloseAndFlush();
}

/// <summary>
/// YOLO 服务的占位符实现，用于演示
/// </summary>
public class YoloServicePlaceholder : IYoloService
{
    private readonly ILogger<YoloServicePlaceholder> _logger;

    public YoloServicePlaceholder(ILogger<YoloServicePlaceholder> logger)
    {
        _logger = logger;
    }

    public Task<TrainingResult> TrainModelAsync(YoloTrainingConfig trainingConfig, IProgress<TrainingProgress>? progressCallback = null, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 训练功能尚未实现");
        return Task.FromResult(new TrainingResult
        {
            Success = false,
            ErrorMessage = "YOLO 训练功能尚未实现"
        });
    }

    public Task<ValidationResult> ValidateModelAsync(string modelPath, string validationDataPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 验证功能尚未实现");
        return Task.FromResult(new ValidationResult
        {
            Success = false,
            ErrorMessage = "YOLO 验证功能尚未实现"
        });
    }

    public Task<List<Detection>> InferAsync(string modelPath, byte[] imageData, YoloInferenceConfig inferenceConfig, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 推理功能尚未实现");
        
        // 返回模拟检测结果
        var detections = new List<Detection>
        {
            new Detection
            {
                Label = "示例检测",
                Confidence = 0.85,
                ClassId = 0,
                BoundingBox = new BoundingBox
                {
                    CenterX = 0.5,
                    CenterY = 0.5,
                    Width = 0.2,
                    Height = 0.2
                }
            }
        };

        return Task.FromResult(detections);
    }

    public Task<List<BatchDetectionResult>> BatchInferAsync(string modelPath, IEnumerable<string> imagePaths, YoloInferenceConfig inferenceConfig, IProgress<int>? progressCallback = null, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 批量推理功能尚未实现");
        return Task.FromResult(new List<BatchDetectionResult>());
    }

    public Task<string> ExportModelAsync(string modelPath, ModelExportFormat exportFormat, string outputPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 模型导出功能尚未实现");
        return Task.FromResult(string.Empty);
    }

    public Task<YoloModelInfo> GetModelInfoAsync(string modelPath)
    {
        _logger.LogWarning("YOLO 模型信息获取功能尚未实现");
        return Task.FromResult(new YoloModelInfo
        {
            Name = "示例模型",
            Version = "1.0.0",
            InputSize = (640, 640),
            ClassCount = 1,
            ClassNames = { "示例类别" }
        });
    }

    public Task<string> CreateDatasetConfigAsync(DatasetConfig datasetConfig, string outputPath)
    {
        _logger.LogWarning("YOLO 数据集配置创建功能尚未实现");
        return Task.FromResult(string.Empty);
    }

    public Task<DatasetValidationResult> ValidateDatasetAsync(string datasetPath)
    {
        _logger.LogWarning("YOLO 数据集验证功能尚未实现");
        return Task.FromResult(new DatasetValidationResult
        {
            IsValid = false,
            Errors = { "YOLO 数据集验证功能尚未实现" }
        });
    }

    public Task<string> GenerateDataAugmentationAsync(string sourceDataPath, DataAugmentationConfig augmentationConfig, string outputPath, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("YOLO 数据增强功能尚未实现");
        return Task.FromResult(string.Empty);
    }
}
