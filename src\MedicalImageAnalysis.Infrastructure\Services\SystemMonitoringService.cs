using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using MedicalImageAnalysis.Core.Interfaces;
using System.Diagnostics;
using System.Management;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 系统监控服务
/// </summary>
public class SystemMonitoringService : BackgroundService, ISystemMonitoringService
{
    private readonly ILogger<SystemMonitoringService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly PerformanceCounter? _cpuCounter;
    private readonly PerformanceCounter? _memoryCounter;
    private Timer? _monitoringTimer = null;
    private bool _isMonitoring = false;

    public event EventHandler<SystemStatusInfo>? StatusUpdated;

    public SystemMonitoringService(
        ILogger<SystemMonitoringService> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;

        try
        {
            // 初始化性能计数器（仅在Windows上可用）
            if (OperatingSystem.IsWindows())
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "无法初始化性能计数器，将使用替代方法");
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await StartMonitoringAsync();

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var status = await GetCurrentStatusAsync();
                StatusUpdated?.Invoke(this, status);

                // 通过SignalR发送状态更新
                using var scope = _serviceProvider.CreateScope();
                var notificationService = scope.ServiceProvider.GetService<IRealTimeNotificationService>();
                if (notificationService != null)
                {
                    await notificationService.SendSystemStatusAsync(status);
                }

                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统监控过程中发生错误");
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
        }
    }

    public Task StartMonitoringAsync()
    {
        if (_isMonitoring)
        {
            return Task.CompletedTask;
        }

        _logger.LogInformation("开始系统监控");
        _isMonitoring = true;

        return Task.CompletedTask;
    }

    public Task StopMonitoringAsync()
    {
        if (!_isMonitoring)
        {
            return Task.CompletedTask;
        }

        _logger.LogInformation("停止系统监控");
        _isMonitoring = false;
        _monitoringTimer?.Dispose();

        return Task.CompletedTask;
    }

    public async Task<SystemStatusInfo> GetCurrentStatusAsync()
    {
        var status = new SystemStatusInfo();

        try
        {
            // CPU使用率
            status.CpuUsage = await GetCpuUsageAsync();

            // 内存使用率
            status.MemoryUsage = await GetMemoryUsageAsync();

            // 磁盘使用率
            status.DiskUsage = await GetDiskUsageAsync();

            // GPU使用率（如果可用）
            status.GpuUsage = await GetGpuUsageAsync();
            status.GpuMemoryUsage = await GetGpuMemoryUsageAsync();

            // 任务状态
            await GetTaskStatusAsync(status);

            // 系统健康状态
            status.SystemHealth = DetermineSystemHealth(status);

            // 警告信息
            status.Warnings = GetSystemWarnings(status);

            status.Timestamp = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统状态失败");
            status.SystemHealth = "error";
            status.Warnings.Add($"监控错误: {ex.Message}");
        }

        return status;
    }

    private async Task<double> GetCpuUsageAsync()
    {
        try
        {
            if (_cpuCounter != null && OperatingSystem.IsWindows())
            {
                // Windows性能计数器
                return Math.Round(_cpuCounter.NextValue(), 2);
            }
            else
            {
                // 跨平台方法
                var startTime = DateTime.UtcNow;
                var startCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;
                
                await Task.Delay(1000);
                
                var endTime = DateTime.UtcNow;
                var endCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;
                
                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
                
                return Math.Round(cpuUsageTotal * 100, 2);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取CPU使用率失败");
            return 0;
        }
    }

    private async Task<double> GetMemoryUsageAsync()
    {
        try
        {
            await Task.CompletedTask;

            if (_memoryCounter != null && OperatingSystem.IsWindows())
            {
                // Windows性能计数器
                var availableMemoryMB = _memoryCounter.NextValue();
                var totalMemoryMB = GetTotalPhysicalMemoryMB();
                var usedMemoryMB = totalMemoryMB - availableMemoryMB;
                return Math.Round((usedMemoryMB / totalMemoryMB) * 100, 2);
            }
            else
            {
                // 跨平台方法 - 使用当前进程内存
                var process = Process.GetCurrentProcess();
                var workingSet = process.WorkingSet64;
                var totalMemory = GC.GetTotalMemory(false);
                
                // 简化计算，实际应该获取系统总内存
                return Math.Round((double)totalMemory / (1024 * 1024 * 1024) * 10, 2); // 简化值
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取内存使用率失败");
            return 0;
        }
    }

    private async Task<double> GetDiskUsageAsync()
    {
        try
        {
            await Task.CompletedTask;

            var drives = System.IO.DriveInfo.GetDrives();
            var systemDrive = drives.FirstOrDefault(d => d.Name == Path.GetPathRoot(Environment.SystemDirectory));
            
            if (systemDrive != null && systemDrive.IsReady)
            {
                var totalSize = systemDrive.TotalSize;
                var freeSpace = systemDrive.TotalFreeSpace;
                var usedSpace = totalSize - freeSpace;
                return Math.Round((double)usedSpace / totalSize * 100, 2);
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取磁盘使用率失败");
            return 0;
        }
    }

    private async Task<double> GetGpuUsageAsync()
    {
        try
        {
            await Task.CompletedTask;

            // 这里需要使用NVIDIA ML API或其他GPU监控库
            // 简化实现，返回模拟值
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取GPU使用率失败");
            return 0;
        }
    }

    private async Task<double> GetGpuMemoryUsageAsync()
    {
        try
        {
            await Task.CompletedTask;

            // 这里需要使用NVIDIA ML API或其他GPU监控库
            // 简化实现，返回模拟值
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取GPU内存使用率失败");
            return 0;
        }
    }

    private async Task GetTaskStatusAsync(SystemStatusInfo status)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetService<IUnitOfWork>();
            
            if (unitOfWork != null)
            {
                // 获取活跃任务数
                status.ActiveTasks = await unitOfWork.ProcessingTasks.CountAsync(
                    t => t.Status == "running");

                // 获取队列中的任务数
                status.QueuedTasks = await unitOfWork.ProcessingTasks.CountAsync(
                    t => t.Status == "pending");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取任务状态失败");
        }
    }

    private string DetermineSystemHealth(SystemStatusInfo status)
    {
        var warnings = 0;

        if (status.CpuUsage > 90) warnings++;
        if (status.MemoryUsage > 90) warnings++;
        if (status.DiskUsage > 90) warnings++;

        return warnings switch
        {
            0 => "healthy",
            1 => "warning",
            _ => "critical"
        };
    }

    private List<string> GetSystemWarnings(SystemStatusInfo status)
    {
        var warnings = new List<string>();

        if (status.CpuUsage > 90)
            warnings.Add($"CPU使用率过高: {status.CpuUsage:F1}%");

        if (status.MemoryUsage > 90)
            warnings.Add($"内存使用率过高: {status.MemoryUsage:F1}%");

        if (status.DiskUsage > 90)
            warnings.Add($"磁盘使用率过高: {status.DiskUsage:F1}%");

        if (status.ActiveTasks > 10)
            warnings.Add($"活跃任务数过多: {status.ActiveTasks}");

        if (status.QueuedTasks > 50)
            warnings.Add($"队列任务数过多: {status.QueuedTasks}");

        return warnings;
    }

    private double GetTotalPhysicalMemoryMB()
    {
        try
        {
            if (OperatingSystem.IsWindows())
            {
                using var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var totalMemoryBytes = Convert.ToDouble(obj["TotalPhysicalMemory"]);
                    return totalMemoryBytes / (1024 * 1024); // 转换为MB
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取总物理内存失败");
        }

        return 8192; // 默认8GB
    }

    public override void Dispose()
    {
        _cpuCounter?.Dispose();
        _memoryCounter?.Dispose();
        _monitoringTimer?.Dispose();
        base.Dispose();
    }
}
