<Window x:Class="MedicalImageAnalysis.WpfClient.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="医学影像解析系统 - WPF客户端" 
        Height="800" Width="1200"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{StaticResource PrimaryFont}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 顶部工具栏 -->
            <materialDesign:ColorZone Grid.Row="0" 
                                    Mode="PrimaryMid" 
                                    Padding="16,8"
                                    materialDesign:ElevationAssist.Elevation="Dp4">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 应用标题和图标 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Hospital" 
                                               Width="32" Height="32" 
                                               VerticalAlignment="Center"
                                               Foreground="White"/>
                        <TextBlock Text="医学影像解析系统" 
                                 FontSize="20" 
                                 FontWeight="Medium"
                                 VerticalAlignment="Center"
                                 Foreground="White"
                                 Margin="12,0,0,0"/>
                    </StackPanel>

                    <!-- 连接状态 -->
                    <StackPanel Grid.Column="1" 
                              Orientation="Horizontal" 
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="CheckCircle"
                                               Width="16" Height="16"
                                               Foreground="Green"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding ConnectionStatusText}" 
                                 Foreground="White"
                                 Margin="8,0,0,0"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- 工具按钮 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button Style="{StaticResource MaterialDesignToolButton}"
                                Command="{Binding SettingsCommand}"
                                ToolTip="设置">
                            <materialDesign:PackIcon Kind="Settings" 
                                                   Width="20" Height="20"
                                                   Foreground="White"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignToolButton}"
                                Command="{Binding AboutCommand}"
                                ToolTip="关于"
                                Margin="8,0,0,0">
                            <materialDesign:PackIcon Kind="Information" 
                                                   Width="20" Height="20"
                                                   Foreground="White"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>

            <!-- 主内容区域 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧导航 -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="8,8,4,8"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!-- 导航标题 -->
                            <TextBlock Text="功能模块" 
                                     FontSize="16" 
                                     FontWeight="Medium"
                                     Margin="16,16,16,8"
                                     Foreground="{StaticResource PrimaryBrush}"/>

                            <!-- 导航菜单 -->
                            <ListBox x:Name="NavigationListBox"
                                   ItemsSource="{Binding NavigationItems}"
                                   SelectedItem="{Binding SelectedNavigationItem}"
                                   Style="{StaticResource MaterialDesignNavigationPrimaryListBox}">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" Margin="16,8">
                                            <materialDesign:PackIcon Kind="{Binding Icon}" 
                                                                   Width="20" Height="20"
                                                                   VerticalAlignment="Center"/>
                                            <TextBlock Text="{Binding Title}" 
                                                     Margin="16,0,0,0"
                                                     VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>

                            <Separator Margin="8,16"/>

                            <!-- 快速操作 -->
                            <TextBlock Text="快速操作" 
                                     FontSize="14" 
                                     FontWeight="Medium"
                                     Margin="16,8,16,8"
                                     Foreground="{StaticResource PrimaryBrush}"/>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding OpenDicomCommand}"
                                  Margin="16,4,16,4"
                                  HorizontalAlignment="Stretch">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileImage" Width="16" Height="16"/>
                                    <TextBlock Text="打开DICOM" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding StartTrainingCommand}"
                                  Margin="16,4,16,4"
                                  HorizontalAlignment="Stretch">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Play" Width="16" Height="16"/>
                                    <TextBlock Text="开始训练" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding RunInferenceCommand}"
                                  Margin="16,4,16,4"
                                  HorizontalAlignment="Stretch">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Brain" Width="16" Height="16"/>
                                    <TextBlock Text="运行推理" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>

                <!-- 右侧内容区域 -->
                <materialDesign:Card Grid.Column="1" 
                                   Margin="4,8,8,8"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <Grid>
                        <!-- 内容框架 -->
                        <Frame x:Name="ContentFrame" 
                             NavigationUIVisibility="Hidden"
                             Content="{Binding CurrentView}"/>

                        <!-- 加载遮罩 -->
                        <Grid Background="#80000000" 
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel HorizontalAlignment="Center" 
                                      VerticalAlignment="Center"
                                      Background="White"
                                      Margin="20"
                                      Opacity="0.95">
                                <materialDesign:PackIcon Kind="Loading"
                                                       Width="48" Height="48"
                                                       HorizontalAlignment="Center"
                                                       Foreground="{StaticResource PrimaryBrush}"/>
                                <TextBlock Text="{Binding LoadingMessage}" 
                                         HorizontalAlignment="Center"
                                         Margin="0,16,0,0"
                                         FontSize="14"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- 底部状态栏 -->
            <materialDesign:ColorZone Grid.Row="2" 
                                    Mode="PrimaryDark" 
                                    Padding="16,4"
                                    materialDesign:ElevationAssist.Elevation="Dp4">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 状态信息 -->
                    <TextBlock Grid.Column="0" 
                             Text="{Binding StatusMessage}" 
                             VerticalAlignment="Center"
                             Foreground="White"/>

                    <!-- 进度条 -->
                    <ProgressBar Grid.Column="1" 
                               Width="200" 
                               Height="4"
                               Value="{Binding ProgressValue}"
                               Maximum="100"
                               Visibility="{Binding IsProgressVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                               Margin="16,0"/>

                    <!-- 系统信息 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <TextBlock Text="{Binding SystemInfo}" 
                                 VerticalAlignment="Center"
                                 Foreground="White"
                                 FontSize="11"/>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>
