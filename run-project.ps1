# 医学影像解析系统启动脚本 (PowerShell)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "医学影像解析系统启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "检查 .NET 8 SDK 安装状态..." -ForegroundColor Yellow

try {
    $dotnetVersion = dotnet --version
    Write-Host "当前 .NET 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到 .NET 8 SDK" -ForegroundColor Red
    Write-Host "请确保已安装 .NET 8 SDK 并重新启动 PowerShell" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}

Write-Host ""
Write-Host "创建必要的目录..." -ForegroundColor Yellow

$directories = @(
    "data",
    "data\logs",
    "data\temp", 
    "data\output",
    "data\models",
    "data\cache",
    "data\backup"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "创建目录: $dir" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "恢复 NuGet 包..." -ForegroundColor Yellow

try {
    dotnet restore
    if ($LASTEXITCODE -ne 0) {
        throw "NuGet 包恢复失败"
    }
    Write-Host "NuGet 包恢复成功" -ForegroundColor Green
} catch {
    Write-Host "错误: $_" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}

Write-Host ""
Write-Host "构建解决方案..." -ForegroundColor Yellow

try {
    dotnet build --configuration Debug
    if ($LASTEXITCODE -ne 0) {
        throw "项目构建失败"
    }
    Write-Host "项目构建成功！" -ForegroundColor Green
} catch {
    Write-Host "错误: $_" -ForegroundColor Red
    Write-Host "请检查编译错误并修复后重试" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit 1
}

Write-Host ""
Write-Host "选择要运行的项目:" -ForegroundColor Cyan
Write-Host "1. API 服务 (推荐)" -ForegroundColor White
Write-Host "2. Web 界面" -ForegroundColor White
Write-Host "3. WPF 桌面应用" -ForegroundColor White
Write-Host "4. 同时运行 API 和 Web" -ForegroundColor White
Write-Host "5. 同时运行 API 和 WPF" -ForegroundColor White
Write-Host ""

$choice = Read-Host "请输入选择 (1-5)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "启动 API 服务..." -ForegroundColor Green
        Write-Host "访问地址: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "Swagger 文档: http://localhost:5000/swagger" -ForegroundColor Cyan
        Write-Host ""
        Set-Location "src\MedicalImageAnalysis.Api"
        dotnet run
    }
    "2" {
        Write-Host ""
        Write-Host "启动 Web 界面..." -ForegroundColor Green
        Write-Host "访问地址: http://localhost:5002" -ForegroundColor Cyan
        Write-Host ""
        Set-Location "src\MedicalImageAnalysis.Web"
        dotnet run
    }
    "3" {
        Write-Host ""
        Write-Host "启动 WPF 桌面应用..." -ForegroundColor Green
        Write-Host "桌面应用将在新窗口中打开" -ForegroundColor Cyan
        Write-Host ""
        Set-Location "src\MedicalImageAnalysis.Wpf"
        dotnet run
    }
    "4" {
        Write-Host ""
        Write-Host "同时启动 API 和 Web 服务..." -ForegroundColor Green
        Write-Host "API 访问地址: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "Web 访问地址: http://localhost:5002" -ForegroundColor Cyan
        Write-Host ""

        # 启动 API 服务
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\src\MedicalImageAnalysis.Api'; dotnet run"

        # 等待几秒钟
        Start-Sleep -Seconds 3

        # 启动 Web 服务
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\src\MedicalImageAnalysis.Web'; dotnet run"

        Write-Host "两个服务已在新窗口中启动" -ForegroundColor Green
    }
    "5" {
        Write-Host ""
        Write-Host "同时启动 API 和 WPF 应用..." -ForegroundColor Green
        Write-Host "API 访问地址: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "WPF 桌面应用将在新窗口中打开" -ForegroundColor Cyan
        Write-Host ""

        # 启动 API 服务
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\src\MedicalImageAnalysis.Api'; dotnet run"

        # 等待几秒钟让 API 服务启动
        Start-Sleep -Seconds 5

        # 启动 WPF 应用
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\src\MedicalImageAnalysis.Wpf'; dotnet run"

        Write-Host "API 服务和 WPF 应用已启动" -ForegroundColor Green
    }
    default {
        Write-Host "无效选择，默认运行 API 服务" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "启动 API 服务..." -ForegroundColor Green
        Write-Host "访问地址: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "Swagger 文档: http://localhost:5000/swagger" -ForegroundColor Cyan
        Write-Host ""
        Set-Location "src\MedicalImageAnalysis.Api"
        dotnet run
    }
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
Read-Host
