using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.WpfClient.Models;
using MedicalImageAnalysis.WpfClient.Services;
using System.Collections.ObjectModel;
using System.IO;
using System.Windows.Media.Imaging;

namespace MedicalImageAnalysis.WpfClient.ViewModels;

/// <summary>
/// DICOM查看器ViewModel
/// </summary>
public partial class DicomViewerViewModel : ObservableObject
{
    private readonly ILogger<DicomViewerViewModel> _logger;
    private readonly IApiService _apiService;
    private readonly IDialogService _dialogService;
    private readonly INotificationService _notificationService;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly DicomTestService _dicomTestService;

    [ObservableProperty]
    private ObservableCollection<Models.FileInfo> _dicomFiles = new();

    [ObservableProperty]
    private Models.FileInfo? _selectedDicomFile;

    [ObservableProperty]
    private BitmapImage? _currentImage;

    [ObservableProperty]
    private bool _isLoading = false;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private double _windowWidth = 400;

    [ObservableProperty]
    private double _windowCenter = 200;

    [ObservableProperty]
    private double _zoomLevel = 1.0;

    [ObservableProperty]
    private string _mousePosition = "";

    [ObservableProperty]
    private bool _showImageInfo = true;

    [ObservableProperty]
    private string _imageInfo = "";

    // DICOM信息属性
    [ObservableProperty]
    private string _patientName = "";

    [ObservableProperty]
    private string _patientId = "";

    [ObservableProperty]
    private string _patientSex = "";

    [ObservableProperty]
    private string _patientAge = "";

    [ObservableProperty]
    private string _modality = "";

    [ObservableProperty]
    private DateTime _studyDate = DateTime.Now;

    [ObservableProperty]
    private string _studyDescription = "";

    [ObservableProperty]
    private string _imageDimensions = "";

    [ObservableProperty]
    private string _pixelSpacing = "";

    [ObservableProperty]
    private string _sliceThickness = "";

    public DicomViewerViewModel(
        ILogger<DicomViewerViewModel> logger,
        IApiService apiService,
        IDialogService dialogService,
        INotificationService notificationService,
        IImageProcessingService imageProcessingService,
        DicomTestService dicomTestService)
    {
        _logger = logger;
        _apiService = apiService;
        _dialogService = dialogService;
        _notificationService = notificationService;
        _imageProcessingService = imageProcessingService;
        _dicomTestService = dicomTestService;

        // 监听选中文件变化
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(SelectedDicomFile))
            {
                _ = LoadDicomFileAsync();
            }
        };
    }

    /// <summary>
    /// 打开DICOM文件
    /// </summary>
    [RelayCommand]
    private async Task OpenDicom()
    {
        try
        {
            var filePath = await _dialogService.OpenFileDialogAsync(
                "选择DICOM文件",
                "DICOM文件 (*.dcm)|*.dcm|所有文件 (*.*)|*.*");

            if (!string.IsNullOrEmpty(filePath))
            {
                await LoadDicomFileFromPathAsync(filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开DICOM文件失败");
            await _notificationService.ShowErrorAsync("错误", "打开DICOM文件失败");
        }
    }

    /// <summary>
    /// 保存图像
    /// </summary>
    [RelayCommand]
    private async Task SaveImage()
    {
        try
        {
            if (CurrentImage == null)
            {
                await _notificationService.ShowWarningAsync("警告", "没有可保存的图像");
                return;
            }

            var filePath = await _dialogService.SaveFileDialogAsync(
                "保存图像",
                "JPEG文件 (*.jpg)|*.jpg|PNG文件 (*.png)|*.png");

            if (!string.IsNullOrEmpty(filePath))
            {
                // 这里应该实现图像保存逻辑
                StatusMessage = $"图像已保存到: {filePath}";
                await _notificationService.ShowSuccessAsync("成功", "图像保存成功");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存图像失败");
            await _notificationService.ShowErrorAsync("错误", "保存图像失败");
        }
    }

    /// <summary>
    /// 放大
    /// </summary>
    [RelayCommand]
    private void ZoomIn()
    {
        ZoomLevel = Math.Min(ZoomLevel * 1.2, 10.0);
        StatusMessage = $"缩放: {ZoomLevel:P0}";
    }

    /// <summary>
    /// 缩小
    /// </summary>
    [RelayCommand]
    private void ZoomOut()
    {
        ZoomLevel = Math.Max(ZoomLevel / 1.2, 0.1);
        StatusMessage = $"缩放: {ZoomLevel:P0}";
    }

    /// <summary>
    /// 重置缩放
    /// </summary>
    [RelayCommand]
    private void ResetZoom()
    {
        ZoomLevel = 1.0;
        StatusMessage = "缩放已重置";
    }

    /// <summary>
    /// 应用窗宽窗位
    /// </summary>
    [RelayCommand]
    private async Task ApplyWindowLevel()
    {
        try
        {
            if (SelectedDicomFile == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择DICOM文件");
                return;
            }

            IsLoading = true;
            StatusMessage = "应用窗宽窗位...";

            // 应用窗宽窗位并重新加载图像
            var updatedImage = await _imageProcessingService.ApplyWindowLevelAsync(
                SelectedDicomFile.Path, WindowWidth, WindowCenter);

            if (updatedImage != null)
            {
                CurrentImage = updatedImage;
                StatusMessage = $"窗宽窗位已应用: W={WindowWidth}, C={WindowCenter}";
            }
            else
            {
                StatusMessage = "应用窗宽窗位失败";
                await _notificationService.ShowErrorAsync("错误", "应用窗宽窗位失败");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用窗宽窗位失败");
            await _notificationService.ShowErrorAsync("错误", "应用窗宽窗位失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 查看标签
    /// </summary>
    [RelayCommand]
    private async Task ShowTags()
    {
        try
        {
            if (SelectedDicomFile == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择DICOM文件");
                return;
            }

            // 这里应该显示DICOM标签对话框
            await _notificationService.ShowInfoAsync("DICOM标签", "DICOM标签查看功能正在开发中...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查看DICOM标签失败");
            await _notificationService.ShowErrorAsync("错误", "查看DICOM标签失败");
        }
    }

    /// <summary>
    /// 导出图像
    /// </summary>
    [RelayCommand]
    private async Task ExportImage()
    {
        try
        {
            if (CurrentImage == null)
            {
                await _notificationService.ShowWarningAsync("警告", "没有可导出的图像");
                return;
            }

            var filePath = await _dialogService.SaveFileDialogAsync(
                "导出图像",
                "JPEG文件 (*.jpg)|*.jpg|PNG文件 (*.png)|*.png|TIFF文件 (*.tiff)|*.tiff");

            if (!string.IsNullOrEmpty(filePath))
            {
                // 这里应该实现图像导出逻辑
                StatusMessage = $"图像已导出到: {filePath}";
                await _notificationService.ShowSuccessAsync("成功", "图像导出成功");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出图像失败");
            await _notificationService.ShowErrorAsync("错误", "导出图像失败");
        }
    }

    /// <summary>
    /// 运行AI分析
    /// </summary>
    [RelayCommand]
    private async Task RunAnalysis()
    {
        try
        {
            if (SelectedDicomFile == null)
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择DICOM文件");
                return;
            }

            IsLoading = true;
            StatusMessage = "正在运行AI分析...";

            // 这里应该调用AI分析API
            await Task.Delay(3000); // 模拟分析时间

            StatusMessage = "AI分析完成";
            await _notificationService.ShowSuccessAsync("成功", "AI分析完成，发现3个可疑区域");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI分析失败");
            await _notificationService.ShowErrorAsync("错误", "AI分析失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 从路径加载DICOM文件
    /// </summary>
    private async Task LoadDicomFileFromPathAsync(string filePath)
    {
        try
        {
            IsLoading = true;
            StatusMessage = "加载DICOM文件...";

            var fileInfo = new System.IO.FileInfo(filePath);
            var dicomFileInfo = new Models.FileInfo
            {
                Name = fileInfo.Name,
                Path = fileInfo.FullName,
                Size = fileInfo.Length,
                CreatedTime = fileInfo.CreationTime,
                ModifiedTime = fileInfo.LastWriteTime,
                Extension = fileInfo.Extension,
                Type = "DICOM"
            };

            DicomFiles.Clear();
            DicomFiles.Add(dicomFileInfo);
            SelectedDicomFile = dicomFileInfo;

            StatusMessage = $"已加载DICOM文件: {fileInfo.Name}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载DICOM文件失败: {FilePath}", filePath);
            await _notificationService.ShowErrorAsync("错误", "加载DICOM文件失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 加载选中的DICOM文件
    /// </summary>
    private async Task LoadDicomFileAsync()
    {
        if (SelectedDicomFile == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = "解析DICOM文件...";

            // 提取DICOM元数据
            var metadata = await _imageProcessingService.ExtractDicomMetadataAsync(SelectedDicomFile.Path);

            // 更新DICOM信息
            PatientName = metadata.GetValueOrDefault("PatientName", "未知").ToString() ?? "未知";
            PatientId = metadata.GetValueOrDefault("PatientID", "未知").ToString() ?? "未知";
            PatientSex = metadata.GetValueOrDefault("PatientSex", "未知").ToString() ?? "未知";
            PatientAge = metadata.GetValueOrDefault("PatientAge", "未知").ToString() ?? "未知";
            Modality = metadata.GetValueOrDefault("Modality", "未知").ToString() ?? "未知";

            // 解析日期
            var studyDateStr = metadata.GetValueOrDefault("StudyDate", "").ToString();
            if (DateTime.TryParseExact(studyDateStr, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var parsedDate))
            {
                StudyDate = parsedDate;
            }
            else
            {
                StudyDate = DateTime.Now;
            }

            StudyDescription = metadata.GetValueOrDefault("StudyDescription", "未知").ToString() ?? "未知";

            // 图像尺寸信息
            var rows = Convert.ToInt32(metadata.GetValueOrDefault("Rows", 0));
            var columns = Convert.ToInt32(metadata.GetValueOrDefault("Columns", 0));
            ImageDimensions = $"{columns} x {rows}";

            PixelSpacing = metadata.GetValueOrDefault("PixelSpacing", "未知").ToString() ?? "未知";
            SliceThickness = metadata.GetValueOrDefault("SliceThickness", "未知").ToString() ?? "未知";

            // 更新窗宽窗位
            WindowWidth = Convert.ToDouble(metadata.GetValueOrDefault("WindowWidth", 400.0));
            WindowCenter = Convert.ToDouble(metadata.GetValueOrDefault("WindowCenter", 200.0));

            ImageInfo = $"患者: {PatientName}\n" +
                       $"模态: {Modality}\n" +
                       $"尺寸: {ImageDimensions}\n" +
                       $"日期: {StudyDate:yyyy-MM-dd}";

            // 加载DICOM图像
            CurrentImage = await _imageProcessingService.LoadDicomImageAsync(SelectedDicomFile.Path);

            if (CurrentImage != null)
            {
                StatusMessage = $"DICOM文件解析完成: {SelectedDicomFile.Name}";
            }
            else
            {
                StatusMessage = "DICOM图像加载失败";
                await _notificationService.ShowWarningAsync("警告", "DICOM图像加载失败，可能是不支持的格式");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析DICOM文件失败");
            await _notificationService.ShowErrorAsync("错误", "解析DICOM文件失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 创建测试DICOM数据
    /// </summary>
    [RelayCommand]
    private async Task CreateTestData()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "创建测试DICOM数据...";

            var testDataPath = await _dicomTestService.CreateTestDataDirectoryAsync();

            // 加载测试数据到文件列表
            await LoadDicomFilesFromDirectory(testDataPath);

            StatusMessage = $"测试数据创建成功: {testDataPath}";
            await _notificationService.ShowSuccessAsync("成功", "测试DICOM数据创建成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建测试数据失败");
            await _notificationService.ShowErrorAsync("错误", "创建测试数据失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 从目录加载DICOM文件
    /// </summary>
    private async Task LoadDicomFilesFromDirectory(string directoryPath)
    {
        try
        {
            var dicomFiles = Directory.GetFiles(directoryPath, "*.dcm", SearchOption.TopDirectoryOnly);

            DicomFiles.Clear();

            foreach (var filePath in dicomFiles)
            {
                var fileInfo = new System.IO.FileInfo(filePath);
                var dicomFileInfo = new Models.FileInfo
                {
                    Name = fileInfo.Name,
                    Path = fileInfo.FullName,
                    Size = fileInfo.Length,
                    CreatedTime = fileInfo.CreationTime,
                    ModifiedTime = fileInfo.LastWriteTime,
                    Extension = fileInfo.Extension,
                    Type = "DICOM"
                };

                DicomFiles.Add(dicomFileInfo);
            }

            if (DicomFiles.Count > 0)
            {
                SelectedDicomFile = DicomFiles[0];
            }

            StatusMessage = $"已加载 {DicomFiles.Count} 个DICOM文件";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从目录加载DICOM文件失败: {DirectoryPath}", directoryPath);
            await _notificationService.ShowErrorAsync("错误", "加载DICOM文件失败");
        }
    }
}
