using Microsoft.AspNetCore.Mvc;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;

namespace MedicalImageAnalysis.Api.Controllers;

/// <summary>
/// YOLO 模型管理控制器，提供模型训练、验证和推理 API
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class YoloController : ControllerBase
{
    private readonly IYoloService _yoloService;
    private readonly ILogger<YoloController> _logger;

    public YoloController(IYoloService yoloService, ILogger<YoloController> logger)
    {
        _yoloService = yoloService;
        _logger = logger;
    }

    /// <summary>
    /// 训练 YOLO 模型
    /// </summary>
    /// <param name="config">训练配置</param>
    /// <returns>训练结果</returns>
    [HttpPost("train")]
    [ProducesResponseType(typeof(TrainingResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<TrainingResult>> TrainModel([FromBody] YoloTrainingConfig config)
    {
        try
        {
            if (config == null)
            {
                return BadRequest("训练配置不能为空");
            }

            _logger.LogInformation("开始训练 YOLO 模型");

            var result = await _yoloService.TrainModelAsync(config, null, HttpContext.RequestAborted);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return StatusCode(500, result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOLO 模型训练失败");
            return StatusCode(500, new { error = "训练失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 验证模型性能
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <param name="validationDataPath">验证数据路径</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(ValidationResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<ValidationResult>> ValidateModel(
        [FromForm] string modelPath,
        [FromForm] string validationDataPath)
    {
        try
        {
            if (string.IsNullOrEmpty(modelPath) || string.IsNullOrEmpty(validationDataPath))
            {
                return BadRequest("模型路径和验证数据路径不能为空");
            }

            var result = await _yoloService.ValidateModelAsync(modelPath, validationDataPath, HttpContext.RequestAborted);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOLO 模型验证失败");
            return StatusCode(500, new { error = "验证失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 使用模型进行推理
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <param name="image">图像文件</param>
    /// <param name="config">推理配置</param>
    /// <returns>检测结果</returns>
    [HttpPost("infer")]
    [ProducesResponseType(typeof(List<Detection>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<List<Detection>>> Infer(
        [FromForm] string modelPath,
        [FromForm] IFormFile image,
        [FromForm] YoloInferenceConfig? config = null)
    {
        try
        {
            if (string.IsNullOrEmpty(modelPath))
            {
                return BadRequest("模型路径不能为空");
            }

            if (image == null || image.Length == 0)
            {
                return BadRequest("图像文件不能为空");
            }

            // 读取图像数据
            using var memoryStream = new MemoryStream();
            await image.CopyToAsync(memoryStream);
            var imageData = memoryStream.ToArray();

            // 使用默认配置如果没有提供
            config ??= new YoloInferenceConfig();

            var detections = await _yoloService.InferAsync(modelPath, imageData, config, HttpContext.RequestAborted);
            return Ok(detections);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOLO 推理失败");
            return StatusCode(500, new { error = "推理失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 批量推理
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <param name="imageDirectory">图像目录路径</param>
    /// <param name="config">推理配置</param>
    /// <returns>批量检测结果</returns>
    [HttpPost("batch-infer")]
    [ProducesResponseType(typeof(List<BatchDetectionResult>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<List<BatchDetectionResult>>> BatchInfer(
        [FromForm] string modelPath,
        [FromForm] string imageDirectory,
        [FromForm] YoloInferenceConfig? config = null)
    {
        try
        {
            if (string.IsNullOrEmpty(modelPath) || string.IsNullOrEmpty(imageDirectory))
            {
                return BadRequest("模型路径和图像目录路径不能为空");
            }

            if (!Directory.Exists(imageDirectory))
            {
                return BadRequest("图像目录不存在");
            }

            // 获取所有图像文件
            var imageExtensions = new[] { ".png", ".jpg", ".jpeg", ".bmp", ".tiff" };
            var imagePaths = Directory.GetFiles(imageDirectory)
                .Where(f => imageExtensions.Contains(Path.GetExtension(f).ToLowerInvariant()))
                .ToList();

            if (!imagePaths.Any())
            {
                return BadRequest("图像目录中没有找到有效的图像文件");
            }

            config ??= new YoloInferenceConfig();

            var results = await _yoloService.BatchInferAsync(modelPath, imagePaths, config, null, HttpContext.RequestAborted);
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOLO 批量推理失败");
            return StatusCode(500, new { error = "批量推理失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 导出模型
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <param name="exportFormat">导出格式</param>
    /// <param name="outputPath">输出路径</param>
    /// <returns>导出的模型路径</returns>
    [HttpPost("export")]
    [ProducesResponseType(typeof(string), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<string>> ExportModel(
        [FromForm] string modelPath,
        [FromForm] ModelExportFormat exportFormat,
        [FromForm] string outputPath)
    {
        try
        {
            if (string.IsNullOrEmpty(modelPath) || string.IsNullOrEmpty(outputPath))
            {
                return BadRequest("模型路径和输出路径不能为空");
            }

            var exportedPath = await _yoloService.ExportModelAsync(modelPath, exportFormat, outputPath, HttpContext.RequestAborted);
            return Ok(exportedPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOLO 模型导出失败");
            return StatusCode(500, new { error = "导出失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取模型信息
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <returns>模型信息</returns>
    [HttpGet("model-info")]
    [ProducesResponseType(typeof(YoloModelInfo), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<YoloModelInfo>> GetModelInfo([FromQuery] string modelPath)
    {
        try
        {
            if (string.IsNullOrEmpty(modelPath))
            {
                return BadRequest("模型路径不能为空");
            }

            var modelInfo = await _yoloService.GetModelInfoAsync(modelPath);
            return Ok(modelInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模型信息失败");
            return StatusCode(500, new { error = "获取失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 验证数据集
    /// </summary>
    /// <param name="datasetPath">数据集路径</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate-dataset")]
    [ProducesResponseType(typeof(DatasetValidationResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<DatasetValidationResult>> ValidateDataset([FromForm] string datasetPath)
    {
        try
        {
            if (string.IsNullOrEmpty(datasetPath))
            {
                return BadRequest("数据集路径不能为空");
            }

            var result = await _yoloService.ValidateDatasetAsync(datasetPath);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据集验证失败");
            return StatusCode(500, new { error = "验证失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 生成数据增强
    /// </summary>
    /// <param name="sourceDataPath">源数据路径</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="config">增强配置</param>
    /// <returns>增强后的数据路径</returns>
    [HttpPost("augment-data")]
    [ProducesResponseType(typeof(string), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<string>> AugmentData(
        [FromForm] string sourceDataPath,
        [FromForm] string outputPath,
        [FromForm] DataAugmentationConfig? config = null)
    {
        try
        {
            if (string.IsNullOrEmpty(sourceDataPath) || string.IsNullOrEmpty(outputPath))
            {
                return BadRequest("源数据路径和输出路径不能为空");
            }

            config ??= new DataAugmentationConfig();

            var augmentedPath = await _yoloService.GenerateDataAugmentationAsync(sourceDataPath, config, outputPath, HttpContext.RequestAborted);
            return Ok(augmentedPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据增强失败");
            return StatusCode(500, new { error = "增强失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取默认训练配置
    /// </summary>
    /// <returns>默认训练配置</returns>
    [HttpGet("default-training-config")]
    [ProducesResponseType(typeof(YoloTrainingConfig), 200)]
    public ActionResult<YoloTrainingConfig> GetDefaultTrainingConfig()
    {
        var config = new YoloTrainingConfig
        {
            Epochs = 100,
            BatchSize = 16,
            ImageSize = 640,
            LearningRate = 0.01,
            Optimizer = OptimizerType.SGD,
            Device = "0",
            Workers = 8,
            Patience = 50,
            UseMixedPrecision = true,
            CacheData = true,
            OutputDirectory = "./runs/train",
            ExperimentName = "exp",
            SaveCheckpoints = true,
            ValidationFrequency = 1
        };

        return Ok(config);
    }

    /// <summary>
    /// 获取默认推理配置
    /// </summary>
    /// <returns>默认推理配置</returns>
    [HttpGet("default-inference-config")]
    [ProducesResponseType(typeof(YoloInferenceConfig), 200)]
    public ActionResult<YoloInferenceConfig> GetDefaultInferenceConfig()
    {
        var config = new YoloInferenceConfig
        {
            ConfidenceThreshold = 0.5,
            IouThreshold = 0.45,
            MaxDetections = 300,
            ImageSize = 640,
            Device = "0",
            UseHalfPrecision = false,
            UseTensorRT = false,
            SaveResults = false
        };

        return Ok(config);
    }
}
