# 医学影像解析系统开发环境启动脚本

Write-Host "=== 医学影像解析系统开发环境启动 ===" -ForegroundColor Green

# 检查 Docker 是否运行
try {
    docker version | Out-Null
    Write-Host "✓ Docker 已运行" -ForegroundColor Green
}
catch {
    Write-Host "✗ Docker 未运行，请先启动 Docker Desktop" -ForegroundColor Red
    exit 1
}

# 检查 .NET 8 SDK
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green
}
catch {
    Write-Host "✗ 未找到 .NET 8 SDK，请先安装" -ForegroundColor Red
    exit 1
}

# 创建必要的目录
$directories = @("data/logs", "data/temp", "data/output", "data/models")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ 创建目录: $dir" -ForegroundColor Green
    }
}

# 检查是否有示例 DICOM 文件
if (Test-Path "Brain/*.dcm") {
    Write-Host "✓ 找到示例 DICOM 文件" -ForegroundColor Green
}
else {
    Write-Host "⚠ 未找到示例 DICOM 文件，请将 .dcm 文件放入 Brain 目录" -ForegroundColor Yellow
}

Write-Host "`n=== 构建和启动服务 ===" -ForegroundColor Cyan

# 构建项目
Write-Host "正在构建项目..." -ForegroundColor Yellow
dotnet build --configuration Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ 项目构建失败" -ForegroundColor Red
    exit 1
}
Write-Host "✓ 项目构建成功" -ForegroundColor Green

# 启动 Docker Compose
Write-Host "正在启动 Docker 服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up --build -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 服务启动成功" -ForegroundColor Green
    
    Write-Host "`n=== 服务信息 ===" -ForegroundColor Cyan
    Write-Host "API 地址: http://localhost:5000" -ForegroundColor White
    Write-Host "Swagger 文档: http://localhost:5000/swagger" -ForegroundColor White
    Write-Host "健康检查: http://localhost:5000/health" -ForegroundColor White
    
    Write-Host "`n=== 常用命令 ===" -ForegroundColor Cyan
    Write-Host "查看日志: docker-compose -f docker-compose.dev.yml logs -f" -ForegroundColor White
    Write-Host "停止服务: docker-compose -f docker-compose.dev.yml down" -ForegroundColor White
    Write-Host "重启服务: docker-compose -f docker-compose.dev.yml restart" -ForegroundColor White
    
    # 等待服务启动
    Write-Host "`n正在等待服务启动..." -ForegroundColor Yellow
    $maxAttempts = 30
    $attempt = 0
    
    do {
        Start-Sleep -Seconds 2
        $attempt++
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                Write-Host "✓ 服务已就绪" -ForegroundColor Green
                break
            }
        }
        catch {
            Write-Host "." -NoNewline -ForegroundColor Yellow
        }
    } while ($attempt -lt $maxAttempts)
    
    if ($attempt -ge $maxAttempts) {
        Write-Host "`n⚠ 服务启动超时，请检查日志" -ForegroundColor Yellow
    }
    else {
        Write-Host "`n🎉 开发环境已就绪！" -ForegroundColor Green
        Write-Host "访问 http://localhost:5000 开始使用" -ForegroundColor White
        
        # 自动打开浏览器
        $openBrowser = Read-Host "`n是否打开浏览器? (y/N)"
        if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
            Start-Process "http://localhost:5000"
        }
    }
}
else {
    Write-Host "✗ 服务启动失败" -ForegroundColor Red
    Write-Host "请检查 Docker 日志: docker-compose -f docker-compose.dev.yml logs" -ForegroundColor Yellow
    exit 1
}
