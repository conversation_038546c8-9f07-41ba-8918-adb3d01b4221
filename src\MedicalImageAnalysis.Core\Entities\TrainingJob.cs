using System.ComponentModel.DataAnnotations;

namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// 训练任务实体
/// </summary>
public class TrainingJob
{
    /// <summary>
    /// 任务唯一标识符
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 任务名称
    /// </summary>
    [Required]
    [StringLength(128)]
    public string JobName { get; set; } = string.Empty;

    /// <summary>
    /// 任务描述
    /// </summary>
    [StringLength(512)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public TrainingJobStatus Status { get; set; } = TrainingJobStatus.Pending;
    /// <summary>
    /// 模型类型
    /// </summary>
    [StringLength(64)]
    public string ModelType { get; set; } = string.Empty;

    /// <summary>
    /// 模型路径
    /// </summary>
    [StringLength(512)]
    public string ModelPath { get; set; } = string.Empty;

    /// <summary>
    /// 数据集路径
    /// </summary>
    [StringLength(512)]
    public string DatasetPath { get; set; } = string.Empty;

    /// <summary>
    /// 配置文件路径
    /// </summary>
    [StringLength(512)]
    public string ConfigPath { get; set; } = string.Empty;

    /// <summary>
    /// 输出路径
    /// </summary>
    [StringLength(512)]
    public string OutputPath { get; set; } = string.Empty;

    /// <summary>
    /// 日志路径
    /// </summary>
    [StringLength(512)]
    public string LogPath { get; set; } = string.Empty;

    /// <summary>
    /// 超参数
    /// </summary>
    public Dictionary<string, object> HyperParameters { get; set; } = new();

    /// <summary>
    /// 训练指标
    /// </summary>
    public Dictionary<string, double> TrainingMetrics { get; set; } = new();

    /// <summary>
    /// 验证指标
    /// </summary>
    public Dictionary<string, double> ValidationMetrics { get; set; } = new();

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public int Progress { get; set; } = 0;

    /// <summary>
    /// 错误消息
    /// </summary>
    [StringLength(1024)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建者
    /// </summary>
    [StringLength(128)]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }
}

/// <summary>
/// 训练数据集实体
/// </summary>
public class TrainingDataset
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string DatasetType { get; set; } = string.Empty; // yolo, coco, custom
    public string DataPath { get; set; } = string.Empty;
    public int ImageCount { get; set; }
    public int AnnotationCount { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 模型版本实体
/// </summary>
public class ModelVersion
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string ModelType { get; set; } = string.Empty;
    public string ModelPath { get; set; } = string.Empty;
    public long ModelSize { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public Dictionary<string, object> Metrics { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsActive { get; set; } = false;
    
    // 外键
    public Guid? TrainingJobId { get; set; }
    
    // 导航属性
    public virtual TrainingJob? TrainingJob { get; set; }
}

/// <summary>
/// 训练指标实体
/// </summary>
public class TrainingMetric
{
    public Guid Id { get; set; }
    public Guid TrainingJobId { get; set; }
    public int Epoch { get; set; }
    public string MetricName { get; set; } = string.Empty;
    public double MetricValue { get; set; }
    public DateTime Timestamp { get; set; }
    
    // 导航属性
    public virtual TrainingJob TrainingJob { get; set; } = null!;
}

/// <summary>
/// 标注项目实体
/// </summary>
public class AnnotationProject
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Settings { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
    
    // 导航属性
    public virtual ICollection<Annotation> Annotations { get; set; } = new List<Annotation>();
}

/// <summary>
/// 标注模板实体
/// </summary>
public class AnnotationTemplate
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TemplateType { get; set; } = string.Empty; // detection, segmentation, classification
    public Dictionary<string, object> Configuration { get; set; } = new();
    public List<string> ClassNames { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsDefault { get; set; } = false;
}

/// <summary>
/// 处理任务实体
/// </summary>
public class ProcessingTask
{
    public Guid Id { get; set; }
    public string TaskType { get; set; } = string.Empty; // training, inference, annotation, processing
    public string Status { get; set; } = string.Empty; // pending, running, completed, failed, cancelled
    public int Priority { get; set; } = 0;
    public Dictionary<string, object> Parameters { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public int Progress { get; set; } = 0;
    public string? ErrorMessage { get; set; }
    
    // 导航属性
    public virtual ICollection<ProcessingResult> Results { get; set; } = new List<ProcessingResult>();
    public virtual ICollection<ProcessingLog> Logs { get; set; } = new List<ProcessingLog>();
}



/// <summary>
/// 处理日志实体
/// </summary>
public class ProcessingLog
{
    public Guid Id { get; set; }
    public Guid TaskId { get; set; }
    public string LogLevel { get; set; } = string.Empty; // debug, info, warning, error
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
    public DateTime Timestamp { get; set; }
    
    // 导航属性
    public virtual ProcessingTask Task { get; set; } = null!;
}

/// <summary>
/// 系统配置实体
/// </summary>
public class SystemConfiguration
{
    public Guid Id { get; set; }
    public string Key { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Category { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 用户偏好设置实体
/// </summary>
public class UserPreference
{
    public Guid Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 审计日志实体
/// </summary>
public class AuditLog
{
    public Guid Id { get; set; }
    public string Action { get; set; } = string.Empty;
    public string? EntityType { get; set; }
    public string? EntityId { get; set; }
    public string? UserId { get; set; }
    public string? UserName { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public Dictionary<string, object> Changes { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 训练任务状态枚举
/// </summary>
public enum TrainingJobStatus
{
    Pending = 0,        // 待处理
    Running = 1,        // 运行中
    Completed = 2,      // 已完成
    Failed = 3,         // 失败
    Cancelled = 4       // 已取消
}
