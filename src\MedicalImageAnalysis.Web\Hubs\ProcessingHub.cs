using Microsoft.AspNetCore.SignalR;

namespace MedicalImageAnalysis.Web.Hubs;

/// <summary>
/// 处理进度实时通信Hub
/// </summary>
public class ProcessingHub : Hub
{
    private readonly ILogger<ProcessingHub> _logger;

    public ProcessingHub(ILogger<ProcessingHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 客户端连接时调用
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("客户端连接: {ConnectionId}", Context.ConnectionId);
        await Groups.AddToGroupAsync(Context.ConnectionId, "ProcessingGroup");
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时调用
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("客户端断开连接: {ConnectionId}", Context.ConnectionId);
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, "ProcessingGroup");
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 加入训练组
    /// </summary>
    public async Task JoinTrainingGroup(string trainingId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"Training_{trainingId}");
        _logger.LogInformation("客户端 {ConnectionId} 加入训练组 {TrainingId}", Context.ConnectionId, trainingId);
    }

    /// <summary>
    /// 离开训练组
    /// </summary>
    public async Task LeaveTrainingGroup(string trainingId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Training_{trainingId}");
        _logger.LogInformation("客户端 {ConnectionId} 离开训练组 {TrainingId}", Context.ConnectionId, trainingId);
    }

    /// <summary>
    /// 发送训练进度更新
    /// </summary>
    public async Task SendTrainingProgress(string trainingId, object progress)
    {
        await Clients.Group($"Training_{trainingId}").SendAsync("TrainingProgress", progress);
    }

    /// <summary>
    /// 发送训练完成通知
    /// </summary>
    public async Task SendTrainingCompleted(string trainingId, string message)
    {
        await Clients.Group($"Training_{trainingId}").SendAsync("TrainingCompleted", message);
    }

    /// <summary>
    /// 发送训练失败通知
    /// </summary>
    public async Task SendTrainingFailed(string trainingId, string error)
    {
        await Clients.Group($"Training_{trainingId}").SendAsync("TrainingFailed", error);
    }

    /// <summary>
    /// 发送处理进度更新
    /// </summary>
    public async Task SendProcessingProgress(object progress)
    {
        await Clients.Group("ProcessingGroup").SendAsync("ProcessingProgress", progress);
    }

    /// <summary>
    /// 发送系统状态更新
    /// </summary>
    public async Task SendSystemStatus(object status)
    {
        await Clients.All.SendAsync("SystemStatus", status);
    }

    /// <summary>
    /// 发送通知消息
    /// </summary>
    public async Task SendNotification(string type, string message)
    {
        await Clients.All.SendAsync("Notification", new { type, message, timestamp = DateTime.Now });
    }
}
