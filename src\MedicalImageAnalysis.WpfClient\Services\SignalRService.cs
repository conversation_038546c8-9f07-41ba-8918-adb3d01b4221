using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.IO;

namespace MedicalImageAnalysis.WpfClient.Services;

/// <summary>
/// SignalR服务接口
/// </summary>
public interface ISignalRService
{
    event Action<double>? OnProgressUpdate;
    event Action<string>? OnStatusUpdate;
    event Action<string, string, string>? OnNotification;
    
    Task StartAsync();
    Task StopAsync();
    bool IsConnected { get; }
}

/// <summary>
/// SignalR服务实现
/// </summary>
public class SignalRService : ISignalRService, IAsyncDisposable
{
    private readonly ILogger<SignalRService> _logger;
    private readonly IConfiguration _configuration;
    private HubConnection? _connection;

    public event Action<double>? OnProgressUpdate;
    public event Action<string>? OnStatusUpdate;
    public event Action<string, string, string>? OnNotification;

    public bool IsConnected => _connection?.State == HubConnectionState.Connected;

    public SignalRService(ILogger<SignalRService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// 启动SignalR连接
    /// </summary>
    public async Task StartAsync()
    {
        try
        {
            var hubUrl = _configuration["ApiSettings:SignalRUrl"] ?? "http://localhost:5000/hubs/processing";
            
            _connection = new HubConnectionBuilder()
                .WithUrl(hubUrl)
                .WithAutomaticReconnect()
                .Build();

            // 注册事件处理器
            RegisterEventHandlers();

            // 启动连接
            await _connection.StartAsync();
            
            _logger.LogInformation("SignalR连接已建立: {Url}", hubUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动SignalR连接失败");
            throw;
        }
    }

    /// <summary>
    /// 停止SignalR连接
    /// </summary>
    public async Task StopAsync()
    {
        try
        {
            if (_connection != null)
            {
                await _connection.StopAsync();
                _logger.LogInformation("SignalR连接已断开");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止SignalR连接失败");
        }
    }

    /// <summary>
    /// 注册事件处理器
    /// </summary>
    private void RegisterEventHandlers()
    {
        if (_connection == null) return;

        // 进度更新
        _connection.On<double>("ProgressUpdate", (progress) =>
        {
            OnProgressUpdate?.Invoke(progress);
        });

        // 状态更新
        _connection.On<string>("StatusUpdate", (status) =>
        {
            OnStatusUpdate?.Invoke(status);
        });

        // 通知
        _connection.On<string, string, string>("Notification", (type, title, message) =>
        {
            OnNotification?.Invoke(type, title, message);
        });

        // 训练进度
        _connection.On<object>("TrainingProgress", (progress) =>
        {
            // 处理训练进度更新
            _logger.LogDebug("收到训练进度更新: {Progress}", progress);
        });

        // 推理完成
        _connection.On<object>("InferenceComplete", (result) =>
        {
            // 处理推理完成事件
            _logger.LogDebug("收到推理完成通知: {Result}", result);
        });

        // 系统状态
        _connection.On<object>("SystemStatus", (status) =>
        {
            // 处理系统状态更新
            _logger.LogDebug("收到系统状态更新: {Status}", status);
        });

        // 连接状态事件
        _connection.Closed += async (error) =>
        {
            await Task.CompletedTask;
            _logger.LogWarning("SignalR连接已关闭: {Error}", error?.Message);
            OnStatusUpdate?.Invoke("连接已断开");
        };

        _connection.Reconnecting += (error) =>
        {
            _logger.LogInformation("SignalR正在重连: {Error}", error?.Message);
            OnStatusUpdate?.Invoke("正在重连...");
            return Task.CompletedTask;
        };

        _connection.Reconnected += (connectionId) =>
        {
            _logger.LogInformation("SignalR重连成功: {ConnectionId}", connectionId);
            OnStatusUpdate?.Invoke("重连成功");
            return Task.CompletedTask;
        };
    }

    public async ValueTask DisposeAsync()
    {
        if (_connection != null)
        {
            await _connection.DisposeAsync();
        }
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 对话框服务接口
/// </summary>
public interface IDialogService
{
    Task<string?> OpenFileDialogAsync(string title, string filter);
    Task<string?> SaveFileDialogAsync(string title, string filter);
    Task<string?> SelectFolderDialogAsync(string title);
    Task ShowMessageAsync(string title, string message);
    Task<bool> ShowConfirmationAsync(string title, string message);
    Task ShowSettingsDialogAsync();
    Task ShowAboutDialogAsync();
}

/// <summary>
/// 对话框服务实现
/// </summary>
public class DialogService : IDialogService
{
    private readonly ILogger<DialogService> _logger;

    public DialogService(ILogger<DialogService> logger)
    {
        _logger = logger;
    }

    public async Task<string?> OpenFileDialogAsync(string title, string filter)
    {
        await Task.CompletedTask;
        
        var dialog = new Microsoft.Win32.OpenFileDialog
        {
            Title = title,
            Filter = filter
        };

        return dialog.ShowDialog() == true ? dialog.FileName : null;
    }

    public async Task<string?> SaveFileDialogAsync(string title, string filter)
    {
        await Task.CompletedTask;
        
        var dialog = new Microsoft.Win32.SaveFileDialog
        {
            Title = title,
            Filter = filter
        };

        return dialog.ShowDialog() == true ? dialog.FileName : null;
    }

    public async Task<string?> SelectFolderDialogAsync(string title)
    {
        await Task.CompletedTask;
        
        var dialog = new Ookii.Dialogs.Wpf.VistaFolderBrowserDialog
        {
            Description = title
        };

        return dialog.ShowDialog() == true ? dialog.SelectedPath : null;
    }

    public async Task ShowMessageAsync(string title, string message)
    {
        await Task.CompletedTask;
        System.Windows.MessageBox.Show(message, title, System.Windows.MessageBoxButton.OK);
    }

    public async Task<bool> ShowConfirmationAsync(string title, string message)
    {
        await Task.CompletedTask;
        var result = System.Windows.MessageBox.Show(message, title, System.Windows.MessageBoxButton.YesNo);
        return result == System.Windows.MessageBoxResult.Yes;
    }

    public async Task ShowSettingsDialogAsync()
    {
        await Task.CompletedTask;
        // 这里应该显示设置对话框
        _logger.LogInformation("显示设置对话框");
    }

    public async Task ShowAboutDialogAsync()
    {
        await Task.CompletedTask;
        // 这里应该显示关于对话框
        var message = "医学影像解析系统 WPF客户端\n版本 1.0.0\n\n基于 .NET 8 和 WPF 开发";
        System.Windows.MessageBox.Show(message, "关于", System.Windows.MessageBoxButton.OK);
    }
}

/// <summary>
/// 文件服务接口
/// </summary>
public interface IFileService
{
    Task<byte[]> ReadFileAsync(string filePath);
    Task WriteFileAsync(string filePath, byte[] data);
    Task<bool> FileExistsAsync(string filePath);
    Task DeleteFileAsync(string filePath);
    Task<string[]> GetFilesAsync(string directory, string pattern = "*.*");
}

/// <summary>
/// 文件服务实现
/// </summary>
public class FileService : IFileService
{
    public async Task<byte[]> ReadFileAsync(string filePath)
    {
        return await File.ReadAllBytesAsync(filePath);
    }

    public async Task WriteFileAsync(string filePath, byte[] data)
    {
        await File.WriteAllBytesAsync(filePath, data);
    }

    public async Task<bool> FileExistsAsync(string filePath)
    {
        await Task.CompletedTask;
        return File.Exists(filePath);
    }

    public async Task DeleteFileAsync(string filePath)
    {
        await Task.CompletedTask;
        if (File.Exists(filePath))
        {
            File.Delete(filePath);
        }
    }

    public async Task<string[]> GetFilesAsync(string directory, string pattern = "*.*")
    {
        await Task.CompletedTask;
        return Directory.Exists(directory) 
            ? Directory.GetFiles(directory, pattern) 
            : Array.Empty<string>();
    }
}

/// <summary>
/// 设置服务接口
/// </summary>
public interface ISettingsService
{
    Task<T> GetSettingAsync<T>(string key, T defaultValue);
    Task SetSettingAsync<T>(string key, T value);
    Task SaveSettingsAsync();
    Task LoadSettingsAsync();
}

/// <summary>
/// 设置服务实现
/// </summary>
public class SettingsService : ISettingsService
{
    private readonly Dictionary<string, object> _settings = new();
    private const string SettingsFileName = "settings.json";

    public async Task<T> GetSettingAsync<T>(string key, T defaultValue)
    {
        await Task.CompletedTask;
        return _settings.TryGetValue(key, out var value) && value is T typedValue 
            ? typedValue 
            : defaultValue;
    }

    public async Task SetSettingAsync<T>(string key, T value)
    {
        await Task.CompletedTask;
        _settings[key] = value!;
    }

    public async Task SaveSettingsAsync()
    {
        var json = System.Text.Json.JsonSerializer.Serialize(_settings, new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = true
        });
        await File.WriteAllTextAsync(SettingsFileName, json);
    }

    public async Task LoadSettingsAsync()
    {
        if (File.Exists(SettingsFileName))
        {
            var json = await File.ReadAllTextAsync(SettingsFileName);
            var settings = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);
            if (settings != null)
            {
                _settings.Clear();
                foreach (var kvp in settings)
                {
                    _settings[kvp.Key] = kvp.Value;
                }
            }
        }
    }
}

/// <summary>
/// 通知服务接口
/// </summary>
public interface INotificationService
{
    Task ShowInfoAsync(string title, string message);
    Task ShowSuccessAsync(string title, string message);
    Task ShowWarningAsync(string title, string message);
    Task ShowErrorAsync(string title, string message);
}

/// <summary>
/// 通知服务实现
/// </summary>
public class NotificationService : INotificationService
{
    public async Task ShowInfoAsync(string title, string message)
    {
        await Task.CompletedTask;
        System.Windows.MessageBox.Show(message, title, System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }

    public async Task ShowSuccessAsync(string title, string message)
    {
        await Task.CompletedTask;
        System.Windows.MessageBox.Show(message, title, System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
    }

    public async Task ShowWarningAsync(string title, string message)
    {
        await Task.CompletedTask;
        System.Windows.MessageBox.Show(message, title, System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
    }

    public async Task ShowErrorAsync(string title, string message)
    {
        await Task.CompletedTask;
        System.Windows.MessageBox.Show(message, title, System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
    }
}
