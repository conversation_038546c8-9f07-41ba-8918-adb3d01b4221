using Microsoft.Extensions.Logging;
using FellowOakDicom;
using FellowOakDicom.Imaging;
using System.IO;

namespace MedicalImageAnalysis.WpfClient.Services;

/// <summary>
/// DICOM测试服务
/// </summary>
public class DicomTestService
{
    private readonly ILogger<DicomTestService> _logger;

    public DicomTestService(ILogger<DicomTestService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 创建测试DICOM文件
    /// </summary>
    public async Task<string> CreateTestDicomFileAsync(string outputPath)
    {
        try
        {
            // 创建一个简单的DICOM数据集
            var dataset = new DicomDataset();

            // 添加必要的DICOM标签
            dataset.Add(DicomTag.SOPClassUID, DicomUID.CTImageStorage);
            dataset.Add(DicomTag.SOPInstanceUID, DicomUID.Generate());
            dataset.Add(DicomTag.StudyInstanceUID, DicomUID.Generate());
            dataset.Add(DicomTag.SeriesInstanceUID, DicomUID.Generate());
            dataset.Add(DicomTag.InstanceNumber, "1");
            dataset.Add(DicomTag.PatientName, "测试^患者");
            dataset.Add(DicomTag.PatientID, "TEST001");
            dataset.Add(DicomTag.PatientSex, "M");
            dataset.Add(DicomTag.PatientAge, "045Y");
            dataset.Add(DicomTag.StudyDate, DateTime.Now.ToString("yyyyMMdd"));
            dataset.Add(DicomTag.StudyTime, DateTime.Now.ToString("HHmmss"));
            dataset.Add(DicomTag.Modality, "CT");
            dataset.Add(DicomTag.StudyDescription, "测试CT扫描");
            dataset.Add(DicomTag.SeriesDescription, "测试序列");
            dataset.Add(DicomTag.SeriesNumber, "1");
            dataset.Add(DicomTag.ImageType, "ORIGINAL", "PRIMARY", "AXIAL");
            
            // 图像相关标签
            dataset.Add(DicomTag.Rows, (ushort)512);
            dataset.Add(DicomTag.Columns, (ushort)512);
            dataset.Add(DicomTag.BitsAllocated, (ushort)16);
            dataset.Add(DicomTag.BitsStored, (ushort)16);
            dataset.Add(DicomTag.HighBit, (ushort)15);
            dataset.Add(DicomTag.PixelRepresentation, (ushort)0);
            dataset.Add(DicomTag.SamplesPerPixel, (ushort)1);
            dataset.Add(DicomTag.PhotometricInterpretation, PhotometricInterpretation.Monochrome2.Value);
            dataset.Add(DicomTag.PixelSpacing, "0.5", "0.5");
            dataset.Add(DicomTag.SliceThickness, "1.0");
            dataset.Add(DicomTag.WindowCenter, "200");
            dataset.Add(DicomTag.WindowWidth, "400");

            // 创建简单的像素数据（512x512的渐变图像）
            var pixelData = new ushort[512 * 512];
            for (int y = 0; y < 512; y++)
            {
                for (int x = 0; x < 512; x++)
                {
                    // 创建一个简单的渐变图案
                    var value = (ushort)((x + y) * 32);
                    pixelData[y * 512 + x] = value;
                }
            }

            // 添加像素数据
            var pixelDataElement = new DicomOtherWord(DicomTag.PixelData, pixelData);
            dataset.Add(pixelDataElement);

            // 创建DICOM文件
            var dicomFile = new DicomFile(dataset);
            
            // 确保输出目录存在
            var directory = Path.GetDirectoryName(outputPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 保存文件
            await dicomFile.SaveAsync(outputPath);
            
            _logger.LogInformation("测试DICOM文件创建成功: {OutputPath}", outputPath);
            return outputPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建测试DICOM文件失败: {OutputPath}", outputPath);
            throw;
        }
    }

    /// <summary>
    /// 验证DICOM文件
    /// </summary>
    public async Task<bool> ValidateDicomFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogError("DICOM文件不存在: {FilePath}", filePath);
                return false;
            }

            // 尝试打开DICOM文件
            var dicomFile = await DicomFile.OpenAsync(filePath);
            var dataset = dicomFile.Dataset;

            // 检查必要的标签
            var requiredTags = new[]
            {
                DicomTag.SOPClassUID,
                DicomTag.SOPInstanceUID,
                DicomTag.StudyInstanceUID,
                DicomTag.SeriesInstanceUID,
                DicomTag.Modality,
                DicomTag.Rows,
                DicomTag.Columns
            };

            foreach (var tag in requiredTags)
            {
                if (!dataset.Contains(tag))
                {
                    _logger.LogWarning("DICOM文件缺少必要标签: {Tag}", tag);
                    return false;
                }
            }

            _logger.LogInformation("DICOM文件验证成功: {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "DICOM文件验证失败: {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// 获取DICOM文件信息
    /// </summary>
    public async Task<Dictionary<string, object>> GetDicomInfoAsync(string filePath)
    {
        var info = new Dictionary<string, object>();

        try
        {
            var dicomFile = await DicomFile.OpenAsync(filePath);
            var dataset = dicomFile.Dataset;

            // 提取基本信息
            info["PatientName"] = GetDicomValue(dataset, DicomTag.PatientName, "未知");
            info["PatientID"] = GetDicomValue(dataset, DicomTag.PatientID, "未知");
            info["PatientSex"] = GetDicomValue(dataset, DicomTag.PatientSex, "未知");
            info["PatientAge"] = GetDicomValue(dataset, DicomTag.PatientAge, "未知");
            info["StudyDate"] = GetDicomValue(dataset, DicomTag.StudyDate, "未知");
            info["StudyTime"] = GetDicomValue(dataset, DicomTag.StudyTime, "未知");
            info["StudyDescription"] = GetDicomValue(dataset, DicomTag.StudyDescription, "未知");
            info["SeriesDescription"] = GetDicomValue(dataset, DicomTag.SeriesDescription, "未知");
            info["Modality"] = GetDicomValue(dataset, DicomTag.Modality, "未知");
            info["Rows"] = GetDicomValue(dataset, DicomTag.Rows, 0);
            info["Columns"] = GetDicomValue(dataset, DicomTag.Columns, 0);
            info["BitsAllocated"] = GetDicomValue(dataset, DicomTag.BitsAllocated, 0);
            info["BitsStored"] = GetDicomValue(dataset, DicomTag.BitsStored, 0);
            info["PixelSpacing"] = GetDicomValue(dataset, DicomTag.PixelSpacing, "未知");
            info["SliceThickness"] = GetDicomValue(dataset, DicomTag.SliceThickness, "未知");
            info["WindowWidth"] = GetDicomValue(dataset, DicomTag.WindowWidth, 400.0);
            info["WindowCenter"] = GetDicomValue(dataset, DicomTag.WindowCenter, 200.0);
            info["InstanceNumber"] = GetDicomValue(dataset, DicomTag.InstanceNumber, 0);
            info["SliceLocation"] = GetDicomValue(dataset, DicomTag.SliceLocation, "未知");

            _logger.LogInformation("DICOM信息提取成功: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取DICOM信息失败: {FilePath}", filePath);
        }

        return info;
    }

    /// <summary>
    /// 获取DICOM标签值
    /// </summary>
    private T GetDicomValue<T>(DicomDataset dataset, DicomTag tag, T defaultValue)
    {
        try
        {
            if (dataset.Contains(tag))
            {
                return dataset.GetValue<T>(tag, 0);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取DICOM标签值失败: {Tag}", tag);
        }
        
        return defaultValue;
    }

    /// <summary>
    /// 创建测试数据目录
    /// </summary>
    public async Task<string> CreateTestDataDirectoryAsync()
    {
        try
        {
            var testDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
                                           "MedicalImageAnalysis", "TestData");
            
            if (!Directory.Exists(testDataPath))
            {
                Directory.CreateDirectory(testDataPath);
            }

            // 创建几个测试DICOM文件
            var testFiles = new[]
            {
                "test_ct_001.dcm",
                "test_ct_002.dcm", 
                "test_ct_003.dcm"
            };

            foreach (var fileName in testFiles)
            {
                var filePath = Path.Combine(testDataPath, fileName);
                if (!File.Exists(filePath))
                {
                    await CreateTestDicomFileAsync(filePath);
                }
            }

            _logger.LogInformation("测试数据目录创建成功: {TestDataPath}", testDataPath);
            return testDataPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建测试数据目录失败");
            throw;
        }
    }
}
