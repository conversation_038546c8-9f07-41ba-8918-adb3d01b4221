// 目录管理相关的 JavaScript 功能

window.directoryManager = {
    // 打开目录（通过 API 调用）
    openDirectory: async function (directoryPath) {
        try {
            const response = await fetch('/api/directory/open', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `directoryPath=${encodeURIComponent(directoryPath)}`
            });

            const result = await response.json();
            
            if (response.ok && result) {
                this.showNotification('目录已打开', 'success');
                return true;
            } else {
                this.showNotification('打开目录失败', 'error');
                return false;
            }
        } catch (error) {
            console.error('打开目录失败:', error);
            this.showNotification('打开目录时发生错误', 'error');
            return false;
        }
    },

    // 打开系统目录
    openSystemDirectory: async function (directoryType) {
        try {
            const response = await fetch(`/api/directory/open-system/${directoryType}`, {
                method: 'POST'
            });

            const result = await response.json();
            
            if (response.ok && result) {
                this.showNotification(`${this.getDirectoryDisplayName(directoryType)}目录已打开`, 'success');
                return true;
            } else {
                this.showNotification(`打开${this.getDirectoryDisplayName(directoryType)}目录失败`, 'error');
                return false;
            }
        } catch (error) {
            console.error('打开系统目录失败:', error);
            this.showNotification('打开目录时发生错误', 'error');
            return false;
        }
    },

    // 获取目录内容
    getDirectoryContent: async function (directoryPath, includeSubdirectories = false) {
        try {
            const url = new URL('/api/directory/content', window.location.origin);
            url.searchParams.append('directoryPath', directoryPath);
            url.searchParams.append('includeSubdirectories', includeSubdirectories);

            const response = await fetch(url);
            
            if (response.ok) {
                return await response.json();
            } else {
                this.showNotification('获取目录内容失败', 'error');
                return null;
            }
        } catch (error) {
            console.error('获取目录内容失败:', error);
            this.showNotification('获取目录内容时发生错误', 'error');
            return null;
        }
    },

    // 创建目录
    createDirectory: async function (directoryPath) {
        try {
            const response = await fetch('/api/directory/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `directoryPath=${encodeURIComponent(directoryPath)}`
            });

            const result = await response.json();
            
            if (response.ok && result) {
                this.showNotification('目录创建成功', 'success');
                return true;
            } else {
                this.showNotification('创建目录失败', 'error');
                return false;
            }
        } catch (error) {
            console.error('创建目录失败:', error);
            this.showNotification('创建目录时发生错误', 'error');
            return false;
        }
    },

    // 获取磁盘使用情况
    getDiskUsage: async function () {
        try {
            const response = await fetch('/api/directory/disk-usage');
            
            if (response.ok) {
                return await response.json();
            } else {
                this.showNotification('获取磁盘使用情况失败', 'error');
                return null;
            }
        } catch (error) {
            console.error('获取磁盘使用情况失败:', error);
            this.showNotification('获取磁盘使用情况时发生错误', 'error');
            return null;
        }
    },

    // 清理临时文件
    cleanupTempFiles: async function (olderThanDays = 7) {
        try {
            const response = await fetch('/api/directory/cleanup-temp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `olderThanDays=${olderThanDays}`
            });

            const result = await response.json();
            
            if (response.ok) {
                this.showNotification(`已清理 ${result} 个临时文件`, 'success');
                return result;
            } else {
                this.showNotification('清理临时文件失败', 'error');
                return 0;
            }
        } catch (error) {
            console.error('清理临时文件失败:', error);
            this.showNotification('清理临时文件时发生错误', 'error');
            return 0;
        }
    },

    // 格式化文件大小
    formatFileSize: function (bytes) {
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0) return '0 B';
        
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        const size = (bytes / Math.pow(1024, i)).toFixed(2);
        
        return `${size} ${sizes[i]}`;
    },

    // 获取目录显示名称
    getDirectoryDisplayName: function (directoryType) {
        const names = {
            'data': '数据',
            'logs': '日志',
            'temp': '临时',
            'output': '输出',
            'models': '模型',
            'sample': '示例数据',
            'config': '配置',
            'backup': '备份',
            'cache': '缓存',
            'scripts': '脚本'
        };
        
        return names[directoryType.toLowerCase()] || directoryType;
    },

    // 显示通知
    showNotification: function (message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${this.getBootstrapAlertClass(type)} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    },

    // 获取 Bootstrap 警告类
    getBootstrapAlertClass: function (type) {
        const classMap = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };
        
        return classMap[type] || 'info';
    },

    // 复制路径到剪贴板
    copyPathToClipboard: async function (path) {
        try {
            await navigator.clipboard.writeText(path);
            this.showNotification('路径已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制路径失败:', error);
            this.showNotification('复制路径失败', 'error');
        }
    },

    // 获取相对路径
    getRelativePath: function (fullPath) {
        const basePath = window.location.origin;
        if (fullPath.startsWith(basePath)) {
            return fullPath.substring(basePath.length);
        }
        return fullPath;
    },

    // 初始化目录管理器
    init: function () {
        console.log('目录管理器已初始化');
        
        // 添加键盘快捷键
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+D 打开数据目录
            if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                event.preventDefault();
                this.openSystemDirectory('data');
            }
            
            // Ctrl+Shift+L 打开日志目录
            if (event.ctrlKey && event.shiftKey && event.key === 'L') {
                event.preventDefault();
                this.openSystemDirectory('logs');
            }
            
            // Ctrl+Shift+O 打开输出目录
            if (event.ctrlKey && event.shiftKey && event.key === 'O') {
                event.preventDefault();
                this.openSystemDirectory('output');
            }
        });
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    window.directoryManager.init();
});

// 为 Blazor 提供的全局函数
window.blazorDirectoryManager = {
    openDirectory: (path) => window.directoryManager.openDirectory(path),
    openSystemDirectory: (type) => window.directoryManager.openSystemDirectory(type),
    copyPath: (path) => window.directoryManager.copyPathToClipboard(path),
    formatFileSize: (bytes) => window.directoryManager.formatFileSize(bytes)
};
