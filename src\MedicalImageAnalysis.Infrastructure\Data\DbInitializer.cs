using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;

namespace MedicalImageAnalysis.Infrastructure.Data;

/// <summary>
/// 数据库初始化器
/// </summary>
public static class DbInitializer
{
    /// <summary>
    /// 初始化数据库
    /// </summary>
    public static async Task InitializeAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MedicalImageDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<MedicalImageDbContext>>();

        try
        {
            // 确保数据库已创建
            await context.Database.EnsureCreatedAsync();
            
            // 应用待处理的迁移
            if ((await context.Database.GetPendingMigrationsAsync()).Any())
            {
                logger.LogInformation("应用数据库迁移...");
                await context.Database.MigrateAsync();
            }

            // 种子数据
            await SeedDataAsync(context, logger);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "数据库初始化失败");
            throw;
        }
    }

    /// <summary>
    /// 种子数据
    /// </summary>
    private static async Task SeedDataAsync(MedicalImageDbContext context, ILogger logger)
    {
        try
        {
            // 种子系统配置
            await SeedSystemConfigurationsAsync(context, logger);

            // 种子标注模板
            await SeedAnnotationTemplatesAsync(context, logger);

            // 种子训练数据集
            await SeedTrainingDatasetsAsync(context, logger);

            await context.SaveChangesAsync();
            logger.LogInformation("种子数据创建完成");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "种子数据创建失败");
            throw;
        }
    }

    /// <summary>
    /// 种子系统配置
    /// </summary>
    private static async Task SeedSystemConfigurationsAsync(MedicalImageDbContext context, ILogger logger)
    {
        if (await context.SystemConfigurations.AnyAsync())
        {
            return; // 已存在配置数据
        }

        logger.LogInformation("创建系统配置种子数据...");

        var configurations = new[]
        {
            new SystemConfiguration
            {
                Id = Guid.NewGuid(),
                Key = "System.Version",
                Value = "1.0.0",
                Description = "系统版本号",
                Category = "System",
                UpdatedAt = DateTime.UtcNow
            },
            new SystemConfiguration
            {
                Id = Guid.NewGuid(),
                Key = "DICOM.StoragePath",
                Value = "dicom_storage",
                Description = "DICOM文件存储路径",
                Category = "DICOM",
                UpdatedAt = DateTime.UtcNow
            },
            new SystemConfiguration
            {
                Id = Guid.NewGuid(),
                Key = "DICOM.MaxFileSize",
                Value = "500",
                Description = "DICOM文件最大大小(MB)",
                Category = "DICOM",
                UpdatedAt = DateTime.UtcNow
            },
            new SystemConfiguration
            {
                Id = Guid.NewGuid(),
                Key = "Training.OutputPath",
                Value = "training_output",
                Description = "训练输出路径",
                Category = "Training",
                UpdatedAt = DateTime.UtcNow
            },
            new SystemConfiguration
            {
                Id = Guid.NewGuid(),
                Key = "Training.MaxConcurrentJobs",
                Value = "2",
                Description = "最大并发训练任务数",
                Category = "Training",
                UpdatedAt = DateTime.UtcNow
            },
            new SystemConfiguration
            {
                Id = Guid.NewGuid(),
                Key = "Processing.TempPath",
                Value = "temp",
                Description = "临时文件路径",
                Category = "Processing",
                UpdatedAt = DateTime.UtcNow
            },
            new SystemConfiguration
            {
                Id = Guid.NewGuid(),
                Key = "Processing.MaxRetries",
                Value = "3",
                Description = "处理任务最大重试次数",
                Category = "Processing",
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.SystemConfigurations.AddRangeAsync(configurations);
    }

    /// <summary>
    /// 种子标注模板
    /// </summary>
    private static async Task SeedAnnotationTemplatesAsync(MedicalImageDbContext context, ILogger logger)
    {
        if (await context.AnnotationTemplates.AnyAsync())
        {
            return; // 已存在模板数据
        }

        logger.LogInformation("创建标注模板种子数据...");

        var templates = new[]
        {
            new AnnotationTemplate
            {
                Id = Guid.NewGuid(),
                Name = "胸部X光检测",
                Description = "用于胸部X光影像的病灶检测标注",
                TemplateType = "detection",
                Configuration = new Dictionary<string, object>
                {
                    ["min_box_size"] = 10,
                    ["max_box_size"] = 500,
                    ["confidence_threshold"] = 0.5
                },
                ClassNames = new List<string> { "肺结节", "肺炎", "气胸", "胸腔积液", "心脏肥大" },
                CreatedBy = "system",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsDefault = true
            },
            new AnnotationTemplate
            {
                Id = Guid.NewGuid(),
                Name = "CT肺结节检测",
                Description = "用于CT影像的肺结节检测标注",
                TemplateType = "detection",
                Configuration = new Dictionary<string, object>
                {
                    ["min_box_size"] = 5,
                    ["max_box_size"] = 200,
                    ["confidence_threshold"] = 0.3
                },
                ClassNames = new List<string> { "肺结节", "磨玻璃结节", "实性结节", "部分实性结节" },
                CreatedBy = "system",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsDefault = false
            },
            new AnnotationTemplate
            {
                Id = Guid.NewGuid(),
                Name = "脑部MRI分割",
                Description = "用于脑部MRI影像的组织分割标注",
                TemplateType = "segmentation",
                Configuration = new Dictionary<string, object>
                {
                    ["segmentation_type"] = "polygon",
                    ["min_area"] = 100
                },
                ClassNames = new List<string> { "灰质", "白质", "脑脊液", "肿瘤", "水肿" },
                CreatedBy = "system",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsDefault = false
            }
        };

        await context.AnnotationTemplates.AddRangeAsync(templates);
    }

    /// <summary>
    /// 种子训练数据集
    /// </summary>
    private static async Task SeedTrainingDatasetsAsync(MedicalImageDbContext context, ILogger logger)
    {
        if (await context.TrainingDatasets.AnyAsync())
        {
            return; // 已存在数据集
        }

        logger.LogInformation("创建训练数据集种子数据...");

        var datasets = new[]
        {
            new TrainingDataset
            {
                Id = Guid.NewGuid(),
                Name = "示例胸部X光数据集",
                Description = "用于演示的胸部X光检测数据集",
                DatasetType = "yolo",
                DataPath = "datasets/chest_xray_demo",
                ImageCount = 0,
                AnnotationCount = 0,
                Metadata = new Dictionary<string, object>
                {
                    ["image_format"] = "jpg",
                    ["annotation_format"] = "yolo",
                    ["image_size"] = new[] { 640, 640 },
                    ["classes"] = new[] { "pneumonia", "normal" }
                },
                CreatedBy = "system",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new TrainingDataset
            {
                Id = Guid.NewGuid(),
                Name = "示例CT肺结节数据集",
                Description = "用于演示的CT肺结节检测数据集",
                DatasetType = "yolo",
                DataPath = "datasets/ct_nodule_demo",
                ImageCount = 0,
                AnnotationCount = 0,
                Metadata = new Dictionary<string, object>
                {
                    ["image_format"] = "png",
                    ["annotation_format"] = "yolo",
                    ["image_size"] = new[] { 512, 512 },
                    ["classes"] = new[] { "nodule", "mass" }
                },
                CreatedBy = "system",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.TrainingDatasets.AddRangeAsync(datasets);
    }
}

/// <summary>
/// 数据库服务扩展
/// </summary>
public static class DatabaseServiceExtensions
{
    /// <summary>
    /// 添加数据库服务
    /// </summary>
    public static IServiceCollection AddDatabaseServices(this IServiceCollection services, string connectionString)
    {
        // 添加数据库上下文
        services.AddDbContext<MedicalImageDbContext>(options =>
        {
            options.UseSqlServer(connectionString, sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);
                sqlOptions.CommandTimeout(120);
            });
            
            // 在开发环境启用敏感数据日志
            #if DEBUG
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
            #endif
        });

        // 添加仓储和工作单元
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        return services;
    }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    public static async Task InitializeDatabaseAsync(this IServiceProvider serviceProvider)
    {
        await DbInitializer.InitializeAsync(serviceProvider);
    }
}
