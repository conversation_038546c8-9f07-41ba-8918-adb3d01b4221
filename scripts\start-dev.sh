#!/bin/bash

# 医学影像解析系统开发环境启动脚本

echo "=== 医学影像解析系统开发环境启动 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 检查 Docker 是否运行
if ! docker version >/dev/null 2>&1; then
    echo -e "${RED}✗ Docker 未运行，请先启动 Docker${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Docker 已运行${NC}"

# 检查 .NET 8 SDK
if ! dotnet --version >/dev/null 2>&1; then
    echo -e "${RED}✗ 未找到 .NET 8 SDK，请先安装${NC}"
    exit 1
fi
DOTNET_VERSION=$(dotnet --version)
echo -e "${GREEN}✓ .NET SDK 版本: $DOTNET_VERSION${NC}"

# 创建必要的目录
DIRECTORIES=("data/logs" "data/temp" "data/output" "data/models")
for dir in "${DIRECTORIES[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo -e "${GREEN}✓ 创建目录: $dir${NC}"
    fi
done

# 检查是否有示例 DICOM 文件
if ls Brain/*.dcm >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 找到示例 DICOM 文件${NC}"
else
    echo -e "${YELLOW}⚠ 未找到示例 DICOM 文件，请将 .dcm 文件放入 Brain 目录${NC}"
fi

echo -e "\n${CYAN}=== 构建和启动服务 ===${NC}"

# 构建项目
echo -e "${YELLOW}正在构建项目...${NC}"
if ! dotnet build --configuration Release; then
    echo -e "${RED}✗ 项目构建失败${NC}"
    exit 1
fi
echo -e "${GREEN}✓ 项目构建成功${NC}"

# 启动 Docker Compose
echo -e "${YELLOW}正在启动 Docker 服务...${NC}"
if docker-compose -f docker-compose.dev.yml up --build -d; then
    echo -e "${GREEN}✓ 服务启动成功${NC}"
    
    echo -e "\n${CYAN}=== 服务信息 ===${NC}"
    echo -e "${WHITE}API 地址: http://localhost:5000${NC}"
    echo -e "${WHITE}Swagger 文档: http://localhost:5000/swagger${NC}"
    echo -e "${WHITE}健康检查: http://localhost:5000/health${NC}"
    
    echo -e "\n${CYAN}=== 常用命令 ===${NC}"
    echo -e "${WHITE}查看日志: docker-compose -f docker-compose.dev.yml logs -f${NC}"
    echo -e "${WHITE}停止服务: docker-compose -f docker-compose.dev.yml down${NC}"
    echo -e "${WHITE}重启服务: docker-compose -f docker-compose.dev.yml restart${NC}"
    
    # 等待服务启动
    echo -e "\n${YELLOW}正在等待服务启动...${NC}"
    MAX_ATTEMPTS=30
    ATTEMPT=0
    
    while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
        sleep 2
        ATTEMPT=$((ATTEMPT + 1))
        
        if curl -f http://localhost:5000/health >/dev/null 2>&1; then
            echo -e "\n${GREEN}✓ 服务已就绪${NC}"
            break
        fi
        echo -n "."
    done
    
    if [ $ATTEMPT -ge $MAX_ATTEMPTS ]; then
        echo -e "\n${YELLOW}⚠ 服务启动超时，请检查日志${NC}"
    else
        echo -e "\n${GREEN}🎉 开发环境已就绪！${NC}"
        echo -e "${WHITE}访问 http://localhost:5000 开始使用${NC}"
        
        # 询问是否打开浏览器
        echo -n -e "\n是否打开浏览器? (y/N): "
        read -r OPEN_BROWSER
        if [[ $OPEN_BROWSER =~ ^[Yy]$ ]]; then
            # 尝试不同的浏览器命令
            if command -v xdg-open >/dev/null 2>&1; then
                xdg-open http://localhost:5000
            elif command -v open >/dev/null 2>&1; then
                open http://localhost:5000
            elif command -v start >/dev/null 2>&1; then
                start http://localhost:5000
            else
                echo -e "${YELLOW}无法自动打开浏览器，请手动访问 http://localhost:5000${NC}"
            fi
        fi
    fi
else
    echo -e "${RED}✗ 服务启动失败${NC}"
    echo -e "${YELLOW}请检查 Docker 日志: docker-compose -f docker-compose.dev.yml logs${NC}"
    exit 1
fi
