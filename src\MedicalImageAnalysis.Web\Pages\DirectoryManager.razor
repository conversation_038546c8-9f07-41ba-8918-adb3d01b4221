@page "/directory-manager"
@using MedicalImageAnalysis.Core.Interfaces
@inject IDirectoryService DirectoryService
@inject IJSRuntime JSRuntime

<PageTitle>目录管理 - 医学影像解析系统</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-folder-open text-primary me-2"></i>
                    目录管理
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/">首页</a></li>
                        <li class="breadcrumb-item active">目录管理</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- 系统目录快速访问 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        系统目录快速访问
                    </h5>
                </div>
                <div class="card-body">
                    @if (systemDirectories != null)
                    {
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="directory-card" @onclick="() => OpenSystemDirectory(@"data")" @oncontextmenu="(e) => ShowContextMenu(e, @"data")" @oncontextmenu:preventDefault="true">
                                    <div class="directory-icon">
                                        <i class="fas fa-database text-primary"></i>
                                    </div>
                                    <div class="directory-info">
                                        <h6 class="mb-1">数据目录</h6>
                                        <small class="text-muted">处理后的数据文件</small>
                                        <div class="directory-path">
                                            <small>@GetRelativePath(systemDirectories.DataDirectory)</small>
                                        </div>
                                    </div>
                                    <div class="directory-actions">
                                        <button class="btn btn-sm btn-outline-secondary" @onclick:stopPropagation="true" @onclick="() => CopyPathToClipboard(systemDirectories.DataDirectory)" title="复制路径">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="directory-card" @onclick="() => OpenSystemDirectory(@"logs")" @oncontextmenu="(e) => ShowContextMenu(e, @"logs")" @oncontextmenu:preventDefault="true">
                                    <div class="directory-icon">
                                        <i class="fas fa-file-alt text-info"></i>
                                    </div>
                                    <div class="directory-info">
                                        <h6 class="mb-1">日志目录</h6>
                                        <small class="text-muted">系统运行日志</small>
                                        <div class="directory-path">
                                            <small>@GetRelativePath(systemDirectories.LogsDirectory)</small>
                                        </div>
                                    </div>
                                    <div class="directory-actions">
                                        <button class="btn btn-sm btn-outline-secondary" @onclick:stopPropagation="true" @onclick="() => CopyPathToClipboard(systemDirectories.LogsDirectory)" title="复制路径">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="directory-card" @onclick="() => OpenSystemDirectory(@"output")">
                                    <div class="directory-icon">
                                        <i class="fas fa-download text-success"></i>
                                    </div>
                                    <div class="directory-info">
                                        <h6 class="mb-1">输出目录</h6>
                                        <small class="text-muted">处理结果输出</small>
                                        <div class="directory-path">
                                            <small>@GetRelativePath(systemDirectories.OutputDirectory)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="directory-card" @onclick="() => OpenSystemDirectory(@"models")">
                                    <div class="directory-icon">
                                        <i class="fas fa-brain text-warning"></i>
                                    </div>
                                    <div class="directory-info">
                                        <h6 class="mb-1">模型目录</h6>
                                        <small class="text-muted">AI 模型文件</small>
                                        <div class="directory-path">
                                            <small>@GetRelativePath(systemDirectories.ModelsDirectory)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="directory-card" @onclick="() => OpenSystemDirectory(@"sample")">
                                    <div class="directory-icon">
                                        <i class="fas fa-file-medical text-danger"></i>
                                    </div>
                                    <div class="directory-info">
                                        <h6 class="mb-1">示例数据</h6>
                                        <small class="text-muted">示例 DICOM 文件</small>
                                        <div class="directory-path">
                                            <small>@GetRelativePath(systemDirectories.SampleDataDirectory)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="directory-card" @onclick="() => OpenSystemDirectory(@"temp")">
                                    <div class="directory-icon">
                                        <i class="fas fa-clock text-secondary"></i>
                                    </div>
                                    <div class="directory-info">
                                        <h6 class="mb-1">临时目录</h6>
                                        <small class="text-muted">临时文件存储</small>
                                        <div class="directory-path">
                                            <small>@GetRelativePath(systemDirectories.TempDirectory)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="directory-card" @onclick="() => OpenSystemDirectory(@"config")">
                                    <div class="directory-icon">
                                        <i class="fas fa-cog text-dark"></i>
                                    </div>
                                    <div class="directory-info">
                                        <h6 class="mb-1">配置目录</h6>
                                        <small class="text-muted">系统配置文件</small>
                                        <div class="directory-path">
                                            <small>@GetRelativePath(systemDirectories.ConfigDirectory)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="directory-card" @onclick="() => OpenSystemDirectory(@"scripts")">
                                    <div class="directory-icon">
                                        <i class="fas fa-code text-purple"></i>
                                    </div>
                                    <div class="directory-info">
                                        <h6 class="mb-1">脚本目录</h6>
                                        <small class="text-muted">系统脚本文件</small>
                                        <div class="directory-path">
                                            <small>@GetRelativePath(systemDirectories.ScriptsDirectory)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载系统目录信息...</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- 磁盘使用情况 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-hdd me-2"></i>
                        磁盘使用情况
                    </h5>
                </div>
                <div class="card-body">
                    @if (diskUsage != null)
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>总空间</span>
                                        <span>@FormatFileSize(diskUsage.TotalSpace)</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>已使用</span>
                                        <span>@FormatFileSize(diskUsage.UsedSpace)</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>可用空间</span>
                                        <span class="text-success">@FormatFileSize(diskUsage.FreeSpace)</span>
                                    </div>
                                </div>
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar @GetUsageColorClass(diskUsage.UsagePercentage)" 
                                         role="progressbar" 
                                         style="width: @(diskUsage.UsagePercentage)%"
                                         aria-valuenow="@diskUsage.UsagePercentage" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">使用率: @diskUsage.UsagePercentage.ToString("F1")%</small>
                            </div>
                            <div class="col-md-6">
                                @if (diskUsage.Drives.Any())
                                {
                                    <h6>驱动器详情</h6>
                                    @foreach (var drive in diskUsage.Drives)
                                    {
                                        <div class="mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span><strong>@drive.Name</strong> (@drive.FileSystem)</span>
                                                <span>@drive.UsagePercentage.ToString("F1")%</span>
                                            </div>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar @GetUsageColorClass(drive.UsagePercentage)" 
                                                     style="width: @(drive.UsagePercentage)%">
                                                </div>
                                            </div>
                                            <small class="text-muted">
                                                @FormatFileSize(drive.FreeSpace) 可用 / @FormatFileSize(drive.TotalSpace) 总计
                                            </small>
                                        </div>
                                    }
                                }
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在获取磁盘使用情况...</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>
                        维护工具
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-warning" @onclick="CleanupTempFiles" disabled="@isCleaningUp">
                            @if (isCleaningUp)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            else
                            {
                                <i class="fas fa-broom me-2"></i>
                            }
                            清理临时文件
                        </button>
                        
                        <button class="btn btn-outline-info" @onclick="RefreshData">
                            <i class="fas fa-sync-alt me-2"></i>
                            刷新数据
                        </button>
                        
                        <button class="btn btn-outline-secondary" @onclick="OpenWorkingDirectory">
                            <i class="fas fa-folder me-2"></i>
                            打开工作目录
                        </button>
                    </div>

                    @if (!string.IsNullOrEmpty(lastCleanupResult))
                    {
                        <div class="alert alert-info mt-3 mb-0">
                            <small>@lastCleanupResult</small>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(alertMessage))
    {
        <div class="alert @alertClass alert-dismissible fade show" role="alert">
            @alertMessage
            <button type="button" class="btn-close" @onclick="ClearAlert"></button>
        </div>
    }
</div>

<style>
    .directory-card {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        align-items: center;
        position: relative;
    }

    .directory-card:hover {
        border-color: #0d6efd;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transform: translateY(-2px);
    }

    .directory-card:hover .directory-actions {
        opacity: 1;
    }

    .directory-icon {
        font-size: 2rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .directory-info {
        flex-grow: 1;
    }

    .directory-path {
        margin-top: 0.25rem;
        font-family: 'Courier New', monospace;
        background-color: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        border: 1px solid #e9ecef;
    }

    .directory-actions {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .text-purple {
        color: #6f42c1 !important;
    }

    .context-menu {
        position: fixed;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        z-index: 1000;
        min-width: 150px;
    }

    .context-menu-item {
        padding: 0.5rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f8f9fa;
        transition: background-color 0.2s ease;
    }

    .context-menu-item:hover {
        background-color: #f8f9fa;
    }

    .context-menu-item:last-child {
        border-bottom: none;
    }

    .context-menu-item i {
        width: 16px;
        margin-right: 0.5rem;
    }
</style>

@code {
    private SystemDirectories? systemDirectories;
    private DiskUsage? diskUsage;
    private bool isCleaningUp = false;
    private string alertMessage = string.Empty;
    private string alertClass = string.Empty;
    private string lastCleanupResult = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            systemDirectories = await DirectoryService.GetSystemDirectoriesAsync();
            diskUsage = await DirectoryService.GetDiskUsageAsync();
        }
        catch (Exception ex)
        {
            ShowAlert($"加载数据失败: {ex.Message}", "alert-danger");
        }
    }

    private async Task OpenSystemDirectory(string directoryType)
    {
        try
        {
            var success = await DirectoryService.OpenDirectoryAsync(GetDirectoryPath(directoryType));
            if (success)
            {
                ShowAlert($"已打开 {GetDirectoryDisplayName(directoryType)} 目录", "alert-success");
            }
            else
            {
                ShowAlert($"打开 {GetDirectoryDisplayName(directoryType)} 目录失败", "alert-warning");
            }
        }
        catch (Exception ex)
        {
            ShowAlert($"打开目录失败: {ex.Message}", "alert-danger");
        }
    }

    private async Task OpenWorkingDirectory()
    {
        try
        {
            var workingDirectory = AppContext.BaseDirectory;
            var success = await DirectoryService.OpenDirectoryAsync(workingDirectory);
            if (success)
            {
                ShowAlert("已打开工作目录", "alert-success");
            }
            else
            {
                ShowAlert("打开工作目录失败", "alert-warning");
            }
        }
        catch (Exception ex)
        {
            ShowAlert($"打开工作目录失败: {ex.Message}", "alert-danger");
        }
    }

    private async Task CleanupTempFiles()
    {
        isCleaningUp = true;
        try
        {
            var cleanedCount = await DirectoryService.CleanupTempFilesAsync(7);
            lastCleanupResult = $"已清理 {cleanedCount} 个临时文件";
            ShowAlert(lastCleanupResult, "alert-success");
            
            // 刷新磁盘使用情况
            diskUsage = await DirectoryService.GetDiskUsageAsync();
        }
        catch (Exception ex)
        {
            ShowAlert($"清理临时文件失败: {ex.Message}", "alert-danger");
        }
        finally
        {
            isCleaningUp = false;
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
        ShowAlert("数据已刷新", "alert-info");
    }

    private string GetDirectoryPath(string directoryType)
    {
        if (systemDirectories == null) return string.Empty;

        return directoryType.ToLowerInvariant() switch
        {
            "data" => systemDirectories.DataDirectory,
            "logs" => systemDirectories.LogsDirectory,
            "temp" => systemDirectories.TempDirectory,
            "output" => systemDirectories.OutputDirectory,
            "models" => systemDirectories.ModelsDirectory,
            "sample" => systemDirectories.SampleDataDirectory,
            "config" => systemDirectories.ConfigDirectory,
            "backup" => systemDirectories.BackupDirectory,
            "cache" => systemDirectories.CacheDirectory,
            "scripts" => systemDirectories.ScriptsDirectory,
            _ => string.Empty
        };
    }

    private string GetDirectoryDisplayName(string directoryType)
    {
        return directoryType.ToLowerInvariant() switch
        {
            "data" => "数据",
            "logs" => "日志",
            "temp" => "临时",
            "output" => "输出",
            "models" => "模型",
            "sample" => "示例数据",
            "config" => "配置",
            "backup" => "备份",
            "cache" => "缓存",
            "scripts" => "脚本",
            _ => directoryType
        };
    }

    private string GetRelativePath(string fullPath)
    {
        var basePath = AppContext.BaseDirectory;
        if (fullPath.StartsWith(basePath))
        {
            return "." + fullPath.Substring(basePath.Length - 1).Replace('\\', '/');
        }
        return fullPath;
    }

    private string GetUsageColorClass(double percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-danger",
            >= 75 => "bg-warning",
            >= 50 => "bg-info",
            _ => "bg-success"
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private void ShowAlert(string message, string cssClass)
    {
        alertMessage = message;
        alertClass = cssClass;
        StateHasChanged();
    }

    private void ClearAlert()
    {
        alertMessage = string.Empty;
        alertClass = string.Empty;
    }

    private async Task CopyPathToClipboard(string path)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("blazorHelpers.copyToClipboard", path);
        }
        catch (Exception ex)
        {
            ShowAlert($"复制路径失败: {ex.Message}", "alert-danger");
        }
    }

    private async Task ShowContextMenu(Microsoft.AspNetCore.Components.Web.MouseEventArgs e, string directoryType)
    {
        // 这里可以实现右键菜单功能
        // 由于 Blazor Server 的限制，我们使用简单的操作
        await Task.CompletedTask;
    }

    private async Task ShowDirectoryInfo(string directoryType)
    {
        try
        {
            var directoryPath = GetDirectoryPath(directoryType);
            if (!string.IsNullOrEmpty(directoryPath))
            {
                var content = await DirectoryService.GetDirectoryContentAsync(directoryPath, false);
                var size = await DirectoryService.GetDirectorySizeAsync(directoryPath);

                var info = $"目录: {GetDirectoryDisplayName(directoryType)}\n" +
                          $"路径: {directoryPath}\n" +
                          $"文件数: {content.TotalFiles}\n" +
                          $"大小: {FormatFileSize(size)}\n" +
                          $"最后修改: {content.LastModified:yyyy-MM-dd HH:mm:ss}";

                await JSRuntime.InvokeVoidAsync("alert", info);
            }
        }
        catch (Exception ex)
        {
            ShowAlert($"获取目录信息失败: {ex.Message}", "alert-danger");
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // 添加键盘快捷键支持
            await JSRuntime.InvokeVoidAsync("eval", @"
                document.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && e.shiftKey) {
                        switch(e.key) {
                            case 'D':
                                e.preventDefault();
                                window.blazorDirectoryManager.openSystemDirectory('data');
                                break;
                            case 'L':
                                e.preventDefault();
                                window.blazorDirectoryManager.openSystemDirectory('logs');
                                break;
                            case 'O':
                                e.preventDefault();
                                window.blazorDirectoryManager.openSystemDirectory('output');
                                break;
                            case 'M':
                                e.preventDefault();
                                window.blazorDirectoryManager.openSystemDirectory('models');
                                break;
                        }
                    }
                });
            ");
        }
    }
}
