@page "/"
@using MedicalImageAnalysis.Core.Entities
@using MedicalImageAnalysis.Core.Interfaces
@inject IJSRuntime JSRuntime
@inject IDirectoryService DirectoryService

<PageTitle>医学影像解析系统</PageTitle>

<div class="container-fluid">
    <!-- 欢迎横幅 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="card-title mb-2">
                                <i class="fas fa-heartbeat me-3"></i>
                                医学影像解析系统
                            </h1>
                            <p class="card-text lead mb-0">
                                基于 .NET 8 和 YOLOv11 的现代化医学影像处理平台
                            </p>
                            <p class="card-text">
                                <small class="text-white-50">
                                    高精度 DICOM 处理 • 智能 AI 标注 • 模型训练 • 数据集导出
                                </small>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-brain fa-5x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-3 mx-auto">
                        <i class="fas fa-file-medical"></i>
                    </div>
                    <h5 class="card-title">DICOM 处理</h5>
                    <p class="card-text text-muted">
                        支持标准 DICOM 格式，自动检测影像方向，精准元数据提取
                    </p>
                    <a href="/dicom-upload" class="btn btn-outline-primary btn-sm">开始处理</a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon bg-success bg-gradient text-white rounded-circle mb-3 mx-auto">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h5 class="card-title">智能标注</h5>
                    <p class="card-text text-muted">
                        AI 自动标注，多种格式支持，标注质量验证和优化
                    </p>
                    <a href="/annotation" class="btn btn-outline-success btn-sm">智能标注</a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon bg-warning bg-gradient text-white rounded-circle mb-3 mx-auto">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h5 class="card-title">模型训练</h5>
                    <p class="card-text text-muted">
                        YOLOv11 模型训练，实时进度监控，性能评估
                    </p>
                    <a href="/model-training" class="btn btn-outline-warning btn-sm">开始训练</a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon bg-info bg-gradient text-white rounded-circle mb-3 mx-auto">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h5 class="card-title">统计分析</h5>
                    <p class="card-text text-muted">
                        数据统计分析，可视化图表，性能指标监控
                    </p>
                    <a href="/statistics" class="btn btn-outline-info btn-sm">查看统计</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统状态 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="status-item">
                                <div class="status-value text-success">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                                <div class="status-label">DICOM 服务</div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="status-item">
                                <div class="status-value text-success">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                                <div class="status-label">影像处理</div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="status-item">
                                <div class="status-value text-warning">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                                <div class="status-label">AI 模型</div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="status-item">
                                <div class="status-value text-success">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                                <div class="status-label">标注服务</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-folder-open me-2"></i>
                        快速访问目录
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-sm btn-outline-primary" @onclick="() => OpenDirectory(@"data")">
                            <i class="fas fa-database me-2"></i>数据目录
                        </button>
                        <button class="btn btn-sm btn-outline-info" @onclick="() => OpenDirectory(@"logs")">
                            <i class="fas fa-file-alt me-2"></i>日志目录
                        </button>
                        <button class="btn btn-sm btn-outline-success" @onclick="() => OpenDirectory(@"output")">
                            <i class="fas fa-download me-2"></i>输出目录
                        </button>
                        <button class="btn btn-sm btn-outline-warning" @onclick="() => OpenDirectory(@"models")">
                            <i class="fas fa-brain me-2"></i>模型目录
                        </button>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        系统信息
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <strong>版本：</strong> v1.0.0
                        </li>
                        <li class="mb-2">
                            <strong>框架：</strong> .NET 8
                        </li>
                        <li class="mb-2">
                            <strong>运行时间：</strong> @GetUptime()
                        </li>
                        <li class="mb-0">
                            <strong>最后更新：</strong> @DateTime.Now.ToString("yyyy-MM-dd")
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="/dicom-upload" class="btn btn-primary btn-lg">
                                    <i class="fas fa-upload me-2"></i>
                                    上传 DICOM 文件
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="/annotation" class="btn btn-success btn-lg">
                                    <i class="fas fa-tags me-2"></i>
                                    开始智能标注
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="/directory-manager" class="btn btn-outline-info btn-lg">
                                    <i class="fas fa-folder-open me-2"></i>
                                    目录管理
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="d-grid">
                                <a href="/help" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-question-circle me-2"></i>
                                    查看帮助文档
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-grid">
                                <button class="btn btn-outline-warning btn-lg" onclick="window.open('http://localhost:5000/swagger', '_blank')">
                                    <i class="fas fa-code me-2"></i>
                                    API 文档
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .feature-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .status-item {
        padding: 1rem 0;
    }

    .status-value {
        margin-bottom: 0.5rem;
    }

    .status-label {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .card {
        transition: transform 0.2s ease-in-out;
    }

    .card:hover {
        transform: translateY(-2px);
    }
</style>

@code {
    private string GetUptime()
    {
        var uptime = DateTime.Now - System.Diagnostics.Process.GetCurrentProcess().StartTime;
        return $"{uptime.Days}天 {uptime.Hours}小时 {uptime.Minutes}分钟";
    }

    private async Task OpenDirectory(string directoryType)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("blazorDirectoryManager.openSystemDirectory", directoryType);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("blazorHelpers.showError", $"打开目录失败: {ex.Message}");
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // 可以在这里添加页面初始化逻辑
            await JSRuntime.InvokeVoidAsync("console.log", "医学影像解析系统已加载");
        }
    }
}
