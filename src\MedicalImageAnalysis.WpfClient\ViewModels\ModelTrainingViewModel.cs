using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.WpfClient.Models;
using MedicalImageAnalysis.WpfClient.Services;
using System.Collections.ObjectModel;

namespace MedicalImageAnalysis.WpfClient.ViewModels;

/// <summary>
/// 模型训练ViewModel
/// </summary>
public partial class ModelTrainingViewModel : ObservableObject
{
    private readonly ILogger<ModelTrainingViewModel> _logger;
    private readonly IApiService _apiService;
    private readonly IDialogService _dialogService;
    private readonly INotificationService _notificationService;

    [ObservableProperty]
    private bool _isTraining = false;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private double _progressValue = 0;

    [ObservableProperty]
    private bool _isProgressVisible = false;

    [ObservableProperty]
    private string _currentEpoch = "0";

    [ObservableProperty]
    private string _totalEpochs = "100";

    [ObservableProperty]
    private double _currentLoss = 0.0;

    [ObservableProperty]
    private double _currentAccuracy = 0.0;

    [ObservableProperty]
    private double _validationLoss = 0.0;

    [ObservableProperty]
    private double _validationAccuracy = 0.0;

    [ObservableProperty]
    private string _learningRate = "0.001";

    [ObservableProperty]
    private string _batchSize = "16";

    [ObservableProperty]
    private string _selectedModel = "YOLOv11";

    [ObservableProperty]
    private string _datasetPath = "";

    [ObservableProperty]
    private string _outputPath = "";

    [ObservableProperty]
    private bool _usePretrainedModel = true;

    [ObservableProperty]
    private string _pretrainedModelPath = "";

    [ObservableProperty]
    private ObservableCollection<TrainingLogEntry> _trainingLogs = new();

    [ObservableProperty]
    private ObservableCollection<string> _availableModels = new() 
    { 
        "YOLOv11", "YOLOv8", "YOLOv5", "Custom CNN" 
    };

    [ObservableProperty]
    private ObservableCollection<DatasetInfo> _datasets = new();

    [ObservableProperty]
    private DatasetInfo? _selectedDataset;

    [ObservableProperty]
    private string _trainingTime = "00:00:00";

    [ObservableProperty]
    private DateTime _trainingStartTime;

    [ObservableProperty]
    private bool _enableDataAugmentation = true;

    [ObservableProperty]
    private bool _enableEarlyStopping = true;

    [ObservableProperty]
    private string _patience = "10";

    [ObservableProperty]
    private string _validationSplit = "0.2";

    public ModelTrainingViewModel(
        ILogger<ModelTrainingViewModel> logger,
        IApiService apiService,
        IDialogService dialogService,
        INotificationService notificationService)
    {
        _logger = logger;
        _apiService = apiService;
        _dialogService = dialogService;
        _notificationService = notificationService;

        InitializeDefaultSettings();
        LoadAvailableDatasets();
    }

    /// <summary>
    /// 开始训练
    /// </summary>
    [RelayCommand]
    private async Task StartTraining()
    {
        try
        {
            if (IsTraining)
            {
                await _notificationService.ShowWarningAsync("警告", "训练正在进行中");
                return;
            }

            if (!await ValidateTrainingParameters())
            {
                return;
            }

            IsTraining = true;
            IsProgressVisible = true;
            ProgressValue = 0;
            TrainingStartTime = DateTime.Now;
            StatusMessage = "正在初始化训练...";

            TrainingLogs.Clear();
            AddTrainingLog("INFO", "训练开始");
            AddTrainingLog("INFO", $"模型: {SelectedModel}");
            AddTrainingLog("INFO", $"数据集: {SelectedDataset?.Name ?? "未选择"}");
            AddTrainingLog("INFO", $"批次大小: {BatchSize}");
            AddTrainingLog("INFO", $"学习率: {LearningRate}");

            // 模拟训练过程
            await SimulateTrainingProcess();

            StatusMessage = "训练完成";
            await _notificationService.ShowSuccessAsync("成功", "模型训练完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "训练失败");
            await _notificationService.ShowErrorAsync("错误", "训练失败");
            AddTrainingLog("ERROR", $"训练失败: {ex.Message}");
        }
        finally
        {
            IsTraining = false;
            IsProgressVisible = false;
        }
    }

    /// <summary>
    /// 停止训练
    /// </summary>
    [RelayCommand]
    private async Task StopTraining()
    {
        try
        {
            if (!IsTraining)
            {
                await _notificationService.ShowInfoAsync("信息", "没有正在进行的训练");
                return;
            }

            var confirmed = await _dialogService.ShowConfirmationAsync(
                "确认", "确定要停止训练吗？当前进度将会丢失。");

            if (confirmed)
            {
                IsTraining = false;
                IsProgressVisible = false;
                StatusMessage = "训练已停止";
                AddTrainingLog("WARNING", "训练被用户停止");
                await _notificationService.ShowInfoAsync("信息", "训练已停止");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止训练失败");
            await _notificationService.ShowErrorAsync("错误", "停止训练失败");
        }
    }

    /// <summary>
    /// 选择数据集路径
    /// </summary>
    [RelayCommand]
    private async Task SelectDatasetPath()
    {
        try
        {
            var path = await _dialogService.SelectFolderDialogAsync("选择数据集文件夹");
            if (!string.IsNullOrEmpty(path))
            {
                DatasetPath = path;
                StatusMessage = $"数据集路径: {path}";
                await LoadDatasetInfo(path);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "选择数据集路径失败");
            await _notificationService.ShowErrorAsync("错误", "选择数据集路径失败");
        }
    }

    /// <summary>
    /// 选择输出路径
    /// </summary>
    [RelayCommand]
    private async Task SelectOutputPath()
    {
        try
        {
            var path = await _dialogService.SelectFolderDialogAsync("选择输出文件夹");
            if (!string.IsNullOrEmpty(path))
            {
                OutputPath = path;
                StatusMessage = $"输出路径: {path}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "选择输出路径失败");
            await _notificationService.ShowErrorAsync("错误", "选择输出路径失败");
        }
    }

    /// <summary>
    /// 选择预训练模型
    /// </summary>
    [RelayCommand]
    private async Task SelectPretrainedModel()
    {
        try
        {
            var filePath = await _dialogService.OpenFileDialogAsync(
                "选择预训练模型文件",
                "模型文件 (*.pt;*.pth;*.onnx)|*.pt;*.pth;*.onnx|所有文件 (*.*)|*.*");

            if (!string.IsNullOrEmpty(filePath))
            {
                PretrainedModelPath = filePath;
                StatusMessage = $"预训练模型: {System.IO.Path.GetFileName(filePath)}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "选择预训练模型失败");
            await _notificationService.ShowErrorAsync("错误", "选择预训练模型失败");
        }
    }

    /// <summary>
    /// 验证数据集
    /// </summary>
    [RelayCommand]
    private async Task ValidateDataset()
    {
        try
        {
            if (string.IsNullOrEmpty(DatasetPath))
            {
                await _notificationService.ShowWarningAsync("警告", "请先选择数据集路径");
                return;
            }

            StatusMessage = "正在验证数据集...";
            
            // 模拟验证过程
            await Task.Delay(2000);

            StatusMessage = "数据集验证完成";
            await _notificationService.ShowSuccessAsync("成功", "数据集验证通过");
            AddTrainingLog("INFO", "数据集验证通过");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证数据集失败");
            await _notificationService.ShowErrorAsync("错误", "验证数据集失败");
        }
    }

    /// <summary>
    /// 清除日志
    /// </summary>
    [RelayCommand]
    private void ClearLogs()
    {
        TrainingLogs.Clear();
        StatusMessage = "日志已清除";
    }

    /// <summary>
    /// 导出训练配置
    /// </summary>
    [RelayCommand]
    private async Task ExportConfig()
    {
        try
        {
            var filePath = await _dialogService.SaveFileDialogAsync(
                "导出训练配置",
                "JSON文件 (*.json)|*.json|YAML文件 (*.yaml)|*.yaml");

            if (!string.IsNullOrEmpty(filePath))
            {
                // 这里应该实现配置导出逻辑
                StatusMessage = $"配置已导出到: {filePath}";
                await _notificationService.ShowSuccessAsync("成功", "配置导出成功");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出配置失败");
            await _notificationService.ShowErrorAsync("错误", "导出配置失败");
        }
    }

    /// <summary>
    /// 导入训练配置
    /// </summary>
    [RelayCommand]
    private async Task ImportConfig()
    {
        try
        {
            var filePath = await _dialogService.OpenFileDialogAsync(
                "导入训练配置",
                "JSON文件 (*.json)|*.json|YAML文件 (*.yaml)|*.yaml");

            if (!string.IsNullOrEmpty(filePath))
            {
                // 这里应该实现配置导入逻辑
                StatusMessage = $"配置已从 {filePath} 导入";
                await _notificationService.ShowSuccessAsync("成功", "配置导入成功");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导入配置失败");
            await _notificationService.ShowErrorAsync("错误", "导入配置失败");
        }
    }

    /// <summary>
    /// 初始化默认设置
    /// </summary>
    private void InitializeDefaultSettings()
    {
        OutputPath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "MedicalImageAnalysis", "Models");
        StatusMessage = "请配置训练参数";
    }

    /// <summary>
    /// 加载可用数据集
    /// </summary>
    private void LoadAvailableDatasets()
    {
        // 模拟加载数据集信息
        Datasets.Add(new DatasetInfo
        {
            Name = "医学影像数据集1",
            Path = @"C:\Data\Medical1",
            ImageCount = 1000,
            ClassCount = 5,
            Description = "包含多种医学影像的标注数据集"
        });

        Datasets.Add(new DatasetInfo
        {
            Name = "DICOM数据集",
            Path = @"C:\Data\DICOM",
            ImageCount = 500,
            ClassCount = 3,
            Description = "DICOM格式的医学影像数据集"
        });
    }

    /// <summary>
    /// 验证训练参数
    /// </summary>
    private async Task<bool> ValidateTrainingParameters()
    {
        if (string.IsNullOrEmpty(DatasetPath))
        {
            await _notificationService.ShowWarningAsync("警告", "请选择数据集路径");
            return false;
        }

        if (string.IsNullOrEmpty(OutputPath))
        {
            await _notificationService.ShowWarningAsync("警告", "请选择输出路径");
            return false;
        }

        if (!int.TryParse(TotalEpochs, out int epochs) || epochs <= 0)
        {
            await _notificationService.ShowWarningAsync("警告", "请输入有效的训练轮数");
            return false;
        }

        if (!double.TryParse(LearningRate, out double lr) || lr <= 0)
        {
            await _notificationService.ShowWarningAsync("警告", "请输入有效的学习率");
            return false;
        }

        if (!int.TryParse(BatchSize, out int bs) || bs <= 0)
        {
            await _notificationService.ShowWarningAsync("警告", "请输入有效的批次大小");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 模拟训练过程
    /// </summary>
    private async Task SimulateTrainingProcess()
    {
        var totalEpochs = int.Parse(TotalEpochs);
        var random = new Random();

        for (int epoch = 1; epoch <= totalEpochs && IsTraining; epoch++)
        {
            CurrentEpoch = epoch.ToString();
            ProgressValue = (double)epoch / totalEpochs * 100;

            // 模拟损失和准确率变化
            CurrentLoss = Math.Max(0.1, 2.0 - epoch * 0.05 + random.NextDouble() * 0.2);
            CurrentAccuracy = Math.Min(0.99, epoch * 0.02 + random.NextDouble() * 0.1);
            ValidationLoss = CurrentLoss + random.NextDouble() * 0.1;
            ValidationAccuracy = Math.Max(0, CurrentAccuracy - random.NextDouble() * 0.05);

            // 更新训练时间
            var elapsed = DateTime.Now - TrainingStartTime;
            TrainingTime = elapsed.ToString(@"hh\:mm\:ss");

            StatusMessage = $"训练中... Epoch {epoch}/{totalEpochs}";

            if (epoch % 10 == 0)
            {
                AddTrainingLog("INFO", $"Epoch {epoch}: Loss={CurrentLoss:F4}, Acc={CurrentAccuracy:F4}");
            }

            await Task.Delay(100); // 模拟训练时间
        }
    }

    /// <summary>
    /// 添加训练日志
    /// </summary>
    private void AddTrainingLog(string level, string message)
    {
        TrainingLogs.Add(new TrainingLogEntry
        {
            Timestamp = DateTime.Now,
            Level = level,
            Message = message
        });
    }

    /// <summary>
    /// 加载数据集信息
    /// </summary>
    private async Task LoadDatasetInfo(string path)
    {
        try
        {
            // 这里应该实现实际的数据集信息加载
            await Task.Delay(500);
            AddTrainingLog("INFO", $"数据集路径已设置: {path}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载数据集信息失败");
        }
    }
}

/// <summary>
/// 训练日志条目
/// </summary>
public class TrainingLogEntry
{
    public DateTime Timestamp { get; set; }
    public string Level { get; set; } = "";
    public string Message { get; set; } = "";
}

/// <summary>
/// 数据集信息
/// </summary>
public class DatasetInfo
{
    public string Name { get; set; } = "";
    public string Path { get; set; } = "";
    public int ImageCount { get; set; }
    public int ClassCount { get; set; }
    public string Description { get; set; } = "";
}
