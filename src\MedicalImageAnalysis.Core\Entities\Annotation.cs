using System.ComponentModel.DataAnnotations;

namespace MedicalImageAnalysis.Core.Entities;

/// <summary>
/// 标注实体，表示在医学影像上的标注信息
/// </summary>
public class Annotation
{
    /// <summary>
    /// 标注唯一标识符
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 标注类型
    /// </summary>
    public AnnotationType Type { get; set; }

    /// <summary>
    /// 标注标签/类别
    /// </summary>
    [Required]
    [StringLength(64)]
    public string Label { get; set; } = string.Empty;

    /// <summary>
    /// 标注描述
    /// </summary>
    [StringLength(512)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 置信度 (0.0 - 1.0)
    /// </summary>
    public double Confidence { get; set; } = 1.0;

    /// <summary>
    /// 边界框坐标 (归一化坐标 0.0-1.0)
    /// </summary>
    public BoundingBox BoundingBox { get; set; } = new();

    /// <summary>
    /// 多边形坐标点 (用于精确分割)
    /// </summary>
    public List<Point2D> PolygonPoints { get; set; } = new();

    /// <summary>
    /// 标注来源
    /// </summary>
    public AnnotationSource Source { get; set; } = AnnotationSource.Manual;

    /// <summary>
    /// 创建者
    /// </summary>
    [StringLength(128)]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 验证者
    /// </summary>
    [StringLength(128)]
    public string VerifiedBy { get; set; } = string.Empty;

    /// <summary>
    /// 验证状态
    /// </summary>
    public VerificationStatus VerificationStatus { get; set; } = VerificationStatus.Pending;

    /// <summary>
    /// 所属实例ID
    /// </summary>
    public Guid InstanceId { get; set; }

    /// <summary>
    /// 所属实例
    /// </summary>
    public DicomInstance Instance { get; set; } = null!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime? VerifiedAt { get; set; }

    /// <summary>
    /// 获取标注的面积 (归一化)
    /// </summary>
    public double Area
    {
        get
        {
            return Type switch
            {
                AnnotationType.BoundingBox => BoundingBox.Width * BoundingBox.Height,
                AnnotationType.Polygon => CalculatePolygonArea(),
                _ => 0.0
            };
        }
    }

    /// <summary>
    /// 转换为 YOLO 格式
    /// </summary>
    public string ToYoloFormat(int classId)
    {
        if (Type != AnnotationType.BoundingBox)
            throw new InvalidOperationException("只有边界框标注可以转换为 YOLO 格式");

        return $"{classId} {BoundingBox.CenterX:F6} {BoundingBox.CenterY:F6} {BoundingBox.Width:F6} {BoundingBox.Height:F6}";
    }

    /// <summary>
    /// 转换为 COCO 格式
    /// </summary>
    public object ToCocoFormat(int imageWidth, int imageHeight, int annotationId, int imageId, int categoryId)
    {
        var absoluteBox = BoundingBox.ToAbsolute(imageWidth, imageHeight);
        
        return new
        {
            id = annotationId,
            image_id = imageId,
            category_id = categoryId,
            bbox = new[] { absoluteBox.X, absoluteBox.Y, absoluteBox.Width, absoluteBox.Height },
            area = absoluteBox.Width * absoluteBox.Height,
            iscrowd = 0,
            segmentation = Type == AnnotationType.Polygon ? 
                new[] { PolygonPoints.SelectMany(p => new[] { p.X * imageWidth, p.Y * imageHeight }).ToArray() } :
                new double[0][]
        };
    }

    /// <summary>
    /// 计算多边形面积
    /// </summary>
    private double CalculatePolygonArea()
    {
        if (PolygonPoints.Count < 3) return 0.0;

        double area = 0.0;
        for (int i = 0; i < PolygonPoints.Count; i++)
        {
            int j = (i + 1) % PolygonPoints.Count;
            area += PolygonPoints[i].X * PolygonPoints[j].Y;
            area -= PolygonPoints[j].X * PolygonPoints[i].Y;
        }
        return Math.Abs(area) / 2.0;
    }
}

/// <summary>
/// 边界框
/// </summary>
public class BoundingBox
{
    /// <summary>
    /// 中心点 X 坐标 (归一化)
    /// </summary>
    public double CenterX { get; set; }

    /// <summary>
    /// 中心点 Y 坐标 (归一化)
    /// </summary>
    public double CenterY { get; set; }

    /// <summary>
    /// 宽度 (归一化)
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 高度 (归一化)
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 左上角 X 坐标
    /// </summary>
    public double Left => CenterX - Width / 2;

    /// <summary>
    /// 左上角 Y 坐标
    /// </summary>
    public double Top => CenterY - Height / 2;

    /// <summary>
    /// 右下角 X 坐标
    /// </summary>
    public double Right => CenterX + Width / 2;

    /// <summary>
    /// 右下角 Y 坐标
    /// </summary>
    public double Bottom => CenterY + Height / 2;

    /// <summary>
    /// 转换为绝对坐标
    /// </summary>
    public AbsoluteBoundingBox ToAbsolute(int imageWidth, int imageHeight)
    {
        return new AbsoluteBoundingBox
        {
            X = Left * imageWidth,
            Y = Top * imageHeight,
            Width = Width * imageWidth,
            Height = Height * imageHeight
        };
    }
}

/// <summary>
/// 绝对坐标边界框
/// </summary>
public class AbsoluteBoundingBox
{
    public double X { get; set; }
    public double Y { get; set; }
    public double Width { get; set; }
    public double Height { get; set; }
}

/// <summary>
/// 2D 点
/// </summary>
public class Point2D
{
    public double X { get; set; }
    public double Y { get; set; }
}

/// <summary>
/// 标注类型枚举
/// </summary>
public enum AnnotationType
{
    BoundingBox = 1,    // 边界框
    Polygon = 2,        // 多边形
    Point = 3,          // 点标注
    Line = 4,           // 线段
    Circle = 5,         // 圆形
    Ellipse = 6         // 椭圆
}

/// <summary>
/// 标注来源枚举
/// </summary>
public enum AnnotationSource
{
    Manual = 1,         // 手动标注
    AI = 2,             // AI 自动标注
    SemiAutomatic = 3,  // 半自动标注
    Imported = 4        // 导入的标注
}

/// <summary>
/// 验证状态枚举
/// </summary>
public enum VerificationStatus
{
    Pending = 0,        // 待验证
    Approved = 1,       // 已批准
    Rejected = 2,       // 已拒绝
    NeedsRevision = 3   // 需要修订
}
