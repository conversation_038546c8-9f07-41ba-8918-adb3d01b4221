using FellowOakDicom;
using FellowOakDicom.Imaging;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// DICOM 服务实现，提供高精度的 DICOM 文件处理功能
/// </summary>
public class DicomService : IDicomService
{
    private readonly ILogger<DicomService> _logger;

    public DicomService(ILogger<DicomService> logger)
    {
        _logger = logger;
        
        // 配置 fo-dicom 使用 ImageSharp
        new DicomSetupBuilder()
            .RegisterServices(s => s.AddFellowOakDicom().AddImageManager<ImageSharpImageManager>())
            .Build();
    }

    /// <summary>
    /// 解析 DICOM 文件并创建研究
    /// </summary>
    public async Task<DicomStudy> ParseDicomFilesAsync(IEnumerable<string> filePaths, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始解析 DICOM 文件，文件数量: {Count}", filePaths.Count());

        var instances = new List<DicomInstance>();
        var validationErrors = new List<string>();

        // 并行处理文件以提高性能
        var semaphore = new SemaphoreSlim(Environment.ProcessorCount);
        var tasks = filePaths.Select(async filePath =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var instance = await ParseDicomFileAsync(filePath, cancellationToken);
                return instance;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析 DICOM 文件失败: {FilePath}", filePath);
                validationErrors.Add($"文件 {filePath}: {ex.Message}");
                return null;
            }
            finally
            {
                semaphore.Release();
            }
        });

        var results = await Task.WhenAll(tasks);
        instances.AddRange(results.Where(r => r != null)!);

        if (!instances.Any())
        {
            throw new InvalidOperationException("没有找到有效的 DICOM 文件");
        }

        // 按研究分组
        var studyGroups = instances.GroupBy(i => i.Series.Study.StudyInstanceUid).ToList();
        
        if (studyGroups.Count > 1)
        {
            _logger.LogWarning("发现多个研究，将使用第一个研究: {StudyUid}", studyGroups.First().Key);
        }

        var firstStudyGroup = studyGroups.First();
        var study = firstStudyGroup.First().Series.Study;

        // 按序列分组并排序
        var seriesGroups = firstStudyGroup.GroupBy(i => i.Series.SeriesInstanceUid);
        
        foreach (var seriesGroup in seriesGroups)
        {
            var series = seriesGroup.First().Series;
            var sortedInstances = await SortInstancesAsync(seriesGroup);
            series.Instances = sortedInstances.ToList();
            
            if (!study.Series.Any(s => s.SeriesInstanceUid == series.SeriesInstanceUid))
            {
                study.Series.Add(series);
            }
        }

        // 检测影像方向
        study.Orientation = await DetectImageOrientationAsync(instances);

        _logger.LogInformation("DICOM 解析完成，研究: {StudyUid}, 序列数: {SeriesCount}, 实例数: {InstanceCount}", 
            study.StudyInstanceUid, study.Series.Count, study.TotalSlices);

        return study;
    }

    /// <summary>
    /// 解析单个 DICOM 文件
    /// </summary>
    public async Task<DicomInstance> ParseDicomFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"DICOM 文件不存在: {filePath}");
        }

        try
        {
            var dicomFile = await DicomFile.OpenAsync(filePath);
            var dataset = dicomFile.Dataset;

            // 验证是否为有效的 DICOM 图像文件
            if (!dataset.Contains(DicomTag.PixelData))
            {
                throw new InvalidOperationException("DICOM 文件不包含像素数据");
            }

            // 创建实例
            var instance = new DicomInstance
            {
                SopInstanceUid = dataset.GetSingleValueOrDefault(DicomTag.SOPInstanceUID, string.Empty),
                InstanceNumber = dataset.GetSingleValueOrDefault(DicomTag.InstanceNumber, 0),
                Rows = dataset.GetSingleValueOrDefault(DicomTag.Rows, (ushort)0),
                Columns = dataset.GetSingleValueOrDefault(DicomTag.Columns, (ushort)0),
                SliceLocation = dataset.GetSingleValueOrDefault(DicomTag.SliceLocation, 0.0),
                SliceThickness = dataset.GetSingleValueOrDefault(DicomTag.SliceThickness, 1.0),
                WindowWidth = dataset.GetSingleValueOrDefault(DicomTag.WindowWidth, 400.0),
                WindowCenter = dataset.GetSingleValueOrDefault(DicomTag.WindowCenter, 40.0),
                RescaleSlope = dataset.GetSingleValueOrDefault(DicomTag.RescaleSlope, 1.0),
                RescaleIntercept = dataset.GetSingleValueOrDefault(DicomTag.RescaleIntercept, 0.0),
                BitsAllocated = dataset.GetSingleValueOrDefault(DicomTag.BitsAllocated, (ushort)16),
                BitsStored = dataset.GetSingleValueOrDefault(DicomTag.BitsStored, (ushort)16),
                HighBit = dataset.GetSingleValueOrDefault(DicomTag.HighBit, (ushort)15),
                PixelRepresentation = dataset.GetSingleValueOrDefault(DicomTag.PixelRepresentation, (ushort)0),
                PhotometricInterpretation = dataset.GetSingleValueOrDefault(DicomTag.PhotometricInterpretation, "MONOCHROME2"),
                FilePath = filePath,
                FileSize = new System.IO.FileInfo(filePath).Length,
                FileHash = await CalculateFileHashAsync(filePath)
            };

            // 解析像素间距
            if (dataset.Contains(DicomTag.PixelSpacing))
            {
                var pixelSpacing = dataset.GetValues<decimal>(DicomTag.PixelSpacing);
                if (pixelSpacing.Length >= 2)
                {
                    instance.PixelSpacing = ((double)pixelSpacing[1], (double)pixelSpacing[0]); // Y, X
                }
            }

            // 解析图像位置
            if (dataset.Contains(DicomTag.ImagePositionPatient))
            {
                var position = dataset.GetValues<decimal>(DicomTag.ImagePositionPatient);
                if (position.Length >= 3)
                {
                    instance.ImagePosition = ((double)position[0], (double)position[1], (double)position[2]);
                }
            }

            // 解析图像方向
            if (dataset.Contains(DicomTag.ImageOrientationPatient))
            {
                var orientation = dataset.GetValues<decimal>(DicomTag.ImageOrientationPatient);
                if (orientation.Length >= 6)
                {
                    instance.ImageOrientationPatient = orientation.Select(o => (double)o).ToArray();
                }
            }

            // 确定像素数据类型
            instance.PixelDataType = DeterminePixelDataType(instance);

            // 创建或获取序列
            var series = await CreateOrGetSeriesAsync(dataset, instance);
            instance.Series = series;

            return instance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析 DICOM 文件失败: {FilePath}", filePath);
            throw new InvalidOperationException($"解析 DICOM 文件失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 验证 DICOM 文件的有效性
    /// </summary>
    public async Task<DicomValidationResult> ValidateDicomFileAsync(string filePath)
    {
        var result = new DicomValidationResult();

        try
        {
            if (!File.Exists(filePath))
            {
                result.Errors.Add("文件不存在");
                return result;
            }

            var fileInfo = new System.IO.FileInfo(filePath);
            result.FileSize = fileInfo.Length;

            var dicomFile = await DicomFile.OpenAsync(filePath);
            var dataset = dicomFile.Dataset;

            // 检查必需的标签
            var requiredTags = new[]
            {
                DicomTag.SOPInstanceUID,
                DicomTag.StudyInstanceUID,
                DicomTag.SeriesInstanceUID,
                DicomTag.Modality
            };

            foreach (var tag in requiredTags)
            {
                if (!dataset.Contains(tag))
                {
                    result.Errors.Add($"缺少必需的标签: {tag}");
                }
            }

            // 检查像素数据
            result.HasPixelData = dataset.Contains(DicomTag.PixelData);
            if (!result.HasPixelData)
            {
                result.Warnings.Add("文件不包含像素数据");
            }

            // 获取 SOP 类和传输语法
            result.SopClassUid = dataset.GetSingleValueOrDefault(DicomTag.SOPClassUID, string.Empty);
            result.TransferSyntaxUid = dicomFile.FileMetaInfo.TransferSyntax?.UID?.UID ?? string.Empty;

            // 验证图像相关标签
            if (result.HasPixelData)
            {
                var imageTags = new[] { DicomTag.Rows, DicomTag.Columns, DicomTag.BitsAllocated };
                foreach (var tag in imageTags)
                {
                    if (!dataset.Contains(tag))
                    {
                        result.Errors.Add($"图像文件缺少必需的标签: {tag}");
                    }
                }
            }

            result.IsValid = !result.Errors.Any();
        }
        catch (Exception ex)
        {
            result.Errors.Add($"验证过程中发生错误: {ex.Message}");
            result.IsValid = false;
        }

        return result;
    }

    /// <summary>
    /// 提取 DICOM 文件的元数据
    /// </summary>
    public async Task<Dictionary<string, object>> ExtractMetadataAsync(string filePath)
    {
        var metadata = new Dictionary<string, object>();

        try
        {
            var dicomFile = await DicomFile.OpenAsync(filePath);
            var dataset = dicomFile.Dataset;

            // 提取常用元数据
            var commonTags = new Dictionary<DicomTag, string>
            {
                { DicomTag.PatientName, "PatientName" },
                { DicomTag.PatientID, "PatientID" },
                { DicomTag.PatientAge, "PatientAge" },
                { DicomTag.PatientSex, "PatientSex" },
                { DicomTag.StudyDate, "StudyDate" },
                { DicomTag.StudyTime, "StudyTime" },
                { DicomTag.StudyDescription, "StudyDescription" },
                { DicomTag.SeriesDescription, "SeriesDescription" },
                { DicomTag.Modality, "Modality" },
                { DicomTag.BodyPartExamined, "BodyPartExamined" },
                { DicomTag.SliceThickness, "SliceThickness" },
                { DicomTag.PixelSpacing, "PixelSpacing" },
                { DicomTag.WindowWidth, "WindowWidth" },
                { DicomTag.WindowCenter, "WindowCenter" }
            };

            foreach (var kvp in commonTags)
            {
                if (dataset.Contains(kvp.Key))
                {
                    var value = dataset.GetValueOrDefault(kvp.Key, 0, string.Empty);
                    metadata[kvp.Value] = value;
                }
            }

            // 添加文件信息
            var fileInfo = new System.IO.FileInfo(filePath);
            metadata["FileSize"] = fileInfo.Length;
            metadata["LastModified"] = fileInfo.LastWriteTime;
            metadata["FilePath"] = filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取元数据失败: {FilePath}", filePath);
            throw;
        }

        return metadata;
    }

    /// <summary>
    /// 获取 DICOM 实例的像素数据
    /// </summary>
    public async Task<PixelData> GetPixelDataAsync(DicomInstance instance, bool applyModalityLut = true, CancellationToken cancellationToken = default)
    {
        try
        {
            var dicomFile = await DicomFile.OpenAsync(instance.FilePath);
            var dicomImage = new DicomImage(dicomFile.Dataset);

            // 获取原始像素数据
            var pixelData = DicomPixelData.Create(dicomFile.Dataset);
            var frame = pixelData.GetFrame(0);

            var result = new PixelData
            {
                Width = instance.Columns,
                Height = instance.Rows,
                BitsPerPixel = instance.BitsAllocated,
                IsSigned = instance.PixelRepresentation == 1,
                PhotometricInterpretation = instance.PhotometricInterpretation,
                DataType = DetermineArrayType(instance)
            };

            // 根据像素表示转换数据
            if (instance.BitsAllocated == 8)
            {
                result.Data = frame.Data;
            }
            else if (instance.BitsAllocated == 16)
            {
                if (instance.PixelRepresentation == 1)
                {
                    // 有符号 16 位
                    var shortData = new short[instance.Rows * instance.Columns];
                    Buffer.BlockCopy(frame.Data, 0, shortData, 0, frame.Data.Length);
                    result.Data = shortData;
                }
                else
                {
                    // 无符号 16 位
                    var ushortData = new ushort[instance.Rows * instance.Columns];
                    Buffer.BlockCopy(frame.Data, 0, ushortData, 0, frame.Data.Length);
                    result.Data = ushortData;
                }
            }
            else
            {
                throw new NotSupportedException($"不支持的位深度: {instance.BitsAllocated}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取像素数据失败: {InstanceUid}", instance.SopInstanceUid);
            throw;
        }
    }

    /// <summary>
    /// 将像素数据转换为 Hounsfield 单位
    /// </summary>
    public async Task<double[]> ConvertToHounsfieldUnitsAsync(PixelData pixelData, DicomInstance instance)
    {
        await Task.CompletedTask; // 异步占位符

        var totalPixels = pixelData.Width * pixelData.Height;
        var huValues = new double[totalPixels];

        for (int i = 0; i < totalPixels; i++)
        {
            double rawValue = 0;

            // 根据数据类型获取原始值
            switch (pixelData.Data)
            {
                case byte[] byteData:
                    rawValue = byteData[i];
                    break;
                case short[] shortData:
                    rawValue = shortData[i];
                    break;
                case ushort[] ushortData:
                    rawValue = ushortData[i];
                    break;
                default:
                    throw new NotSupportedException("不支持的像素数据类型");
            }

            // 应用重缩放公式: HU = slope * raw + intercept
            huValues[i] = instance.RescaleSlope * rawValue + instance.RescaleIntercept;
        }

        return huValues;
    }

    /// <summary>
    /// 应用窗宽窗位调整
    /// </summary>
    public async Task<byte[]> ApplyWindowLevelAsync(double[] pixelData, double windowWidth, double windowCenter)
    {
        await Task.CompletedTask; // 异步占位符

        var result = new byte[pixelData.Length];
        var windowMin = windowCenter - windowWidth / 2.0;
        var windowMax = windowCenter + windowWidth / 2.0;

        for (int i = 0; i < pixelData.Length; i++)
        {
            var value = pixelData[i];

            if (value <= windowMin)
            {
                result[i] = 0;
            }
            else if (value >= windowMax)
            {
                result[i] = 255;
            }
            else
            {
                // 线性映射到 0-255 范围
                var normalized = (value - windowMin) / windowWidth;
                result[i] = (byte)(normalized * 255);
            }
        }

        return result;
    }

    /// <summary>
    /// 检测影像方向
    /// </summary>
    public async Task<ImageOrientation> DetectImageOrientationAsync(IEnumerable<DicomInstance> instances)
    {
        await Task.CompletedTask; // 异步占位符

        var firstInstance = instances.FirstOrDefault();
        if (firstInstance?.ImageOrientationPatient == null || firstInstance.ImageOrientationPatient.Length < 6)
        {
            return ImageOrientation.Unknown;
        }

        var orientation = firstInstance.ImageOrientationPatient;

        // 计算法向量 (叉积)
        var normal = new double[3];
        normal[0] = orientation[1] * orientation[5] - orientation[2] * orientation[4];
        normal[1] = orientation[2] * orientation[3] - orientation[0] * orientation[5];
        normal[2] = orientation[0] * orientation[4] - orientation[1] * orientation[3];

        // 根据法向量的主要方向确定切面方向
        var absNormal = normal.Select(Math.Abs).ToArray();
        var maxIndex = Array.IndexOf(absNormal, absNormal.Max());

        return maxIndex switch
        {
            0 => ImageOrientation.Sagittal,  // X 方向为主 -> 矢状位
            1 => ImageOrientation.Coronal,   // Y 方向为主 -> 冠状位
            2 => ImageOrientation.Axial,     // Z 方向为主 -> 轴位
            _ => ImageOrientation.Unknown
        };
    }

    /// <summary>
    /// 排序 DICOM 实例
    /// </summary>
    public async Task<IEnumerable<DicomInstance>> SortInstancesAsync(IEnumerable<DicomInstance> instances)
    {
        await Task.CompletedTask; // 异步占位符

        var instanceList = instances.ToList();

        // 首先按实例号排序
        instanceList.Sort((a, b) => a.InstanceNumber.CompareTo(b.InstanceNumber));

        // 如果有切片位置信息，按切片位置进行二次排序
        var hasSliceLocation = instanceList.All(i => i.SliceLocation != 0);
        if (hasSliceLocation)
        {
            instanceList.Sort((a, b) => a.SliceLocation.CompareTo(b.SliceLocation));
        }
        // 如果有图像位置信息，按 Z 坐标排序
        else if (instanceList.All(i => i.ImagePosition.Z != 0))
        {
            instanceList.Sort((a, b) => a.ImagePosition.Z.CompareTo(b.ImagePosition.Z));
        }

        return instanceList;
    }

    /// <summary>
    /// 计算研究的统计信息
    /// </summary>
    public async Task<StudyStatistics> CalculateStudyStatisticsAsync(DicomStudy study)
    {
        await Task.CompletedTask; // 异步占位符

        var statistics = new StudyStatistics
        {
            TotalSeries = study.Series.Count,
            TotalInstances = study.TotalSlices,
            TotalFileSize = study.Series.SelectMany(s => s.Instances).Sum(i => i.FileSize)
        };

        var allInstances = study.Series.SelectMany(s => s.Instances).ToList();

        if (allInstances.Any())
        {
            // 计算切片厚度统计
            var sliceThicknesses = allInstances.Select(i => i.SliceThickness).Where(t => t > 0);
            if (sliceThicknesses.Any())
            {
                statistics.AverageSliceThickness = sliceThicknesses.Average();
            }

            // 计算像素间距范围
            var pixelSpacings = allInstances.Select(i => i.PixelSpacing).ToList();
            if (pixelSpacings.Any())
            {
                statistics.PixelSpacingRange = (
                    pixelSpacings.Min(p => p.X),
                    pixelSpacings.Max(p => p.X),
                    pixelSpacings.Min(p => p.Y),
                    pixelSpacings.Max(p => p.Y)
                );
            }

            // 计算图像尺寸范围
            statistics.ImageSizeRange = (
                allInstances.Min(i => i.Columns),
                allInstances.Max(i => i.Columns),
                allInstances.Min(i => i.Rows),
                allInstances.Max(i => i.Rows)
            );

            // 统计模态类型
            statistics.Modalities = study.Series.Select(s => s.Modality).Where(m => !string.IsNullOrEmpty(m)).ToHashSet();

            // 统计检查部位
            if (!string.IsNullOrEmpty(study.BodyPart))
            {
                statistics.BodyParts.Add(study.BodyPart);
            }

            // 统计影像方向分布
            statistics.OrientationDistribution[study.Orientation] = allInstances.Count;
        }

        return statistics;
    }

    #region 私有辅助方法

    /// <summary>
    /// 创建或获取序列
    /// </summary>
    private async Task<DicomSeries> CreateOrGetSeriesAsync(DicomDataset dataset, DicomInstance instance)
    {
        await Task.CompletedTask; // 异步占位符

        var seriesInstanceUid = dataset.GetSingleValueOrDefault(DicomTag.SeriesInstanceUID, string.Empty);
        var studyInstanceUid = dataset.GetSingleValueOrDefault(DicomTag.StudyInstanceUID, string.Empty);

        // 创建序列
        var series = new DicomSeries
        {
            SeriesInstanceUid = seriesInstanceUid,
            SeriesNumber = dataset.GetSingleValueOrDefault(DicomTag.SeriesNumber, 0),
            SeriesDescription = dataset.GetSingleValueOrDefault(DicomTag.SeriesDescription, string.Empty),
            Modality = dataset.GetSingleValueOrDefault(DicomTag.Modality, string.Empty),
            SeriesDateTime = ParseDicomDateTime(
                dataset.GetSingleValueOrDefault(DicomTag.SeriesDate, string.Empty),
                dataset.GetSingleValueOrDefault(DicomTag.SeriesTime, string.Empty)
            )
        };

        // 创建研究
        var study = new DicomStudy
        {
            StudyInstanceUid = studyInstanceUid,
            StudyDescription = dataset.GetSingleValueOrDefault(DicomTag.StudyDescription, string.Empty),
            BodyPart = dataset.GetSingleValueOrDefault(DicomTag.BodyPartExamined, string.Empty),
            StudyDateTime = ParseDicomDateTime(
                dataset.GetSingleValueOrDefault(DicomTag.StudyDate, string.Empty),
                dataset.GetSingleValueOrDefault(DicomTag.StudyTime, string.Empty)
            )
        };

        // 解析患者信息
        study.Patient = new Patient
        {
            PatientId = dataset.GetSingleValueOrDefault(DicomTag.PatientID, string.Empty),
            PatientName = dataset.GetSingleValueOrDefault(DicomTag.PatientName, string.Empty),
            PatientAge = dataset.GetSingleValueOrDefault(DicomTag.PatientAge, string.Empty),
            PatientSex = dataset.GetSingleValueOrDefault(DicomTag.PatientSex, string.Empty),
            PatientBirthDate = ParseDicomDate(dataset.GetSingleValueOrDefault(DicomTag.PatientBirthDate, string.Empty))
        };

        series.Study = study;
        return series;
    }

    /// <summary>
    /// 确定像素数据类型
    /// </summary>
    private static PixelDataType DeterminePixelDataType(DicomInstance instance)
    {
        return (instance.BitsAllocated, instance.PixelRepresentation) switch
        {
            (8, 0) => PixelDataType.UnsignedInt8,
            (8, 1) => PixelDataType.SignedInt8,
            (16, 0) => PixelDataType.UnsignedInt16,
            (16, 1) => PixelDataType.SignedInt16,
            (32, 0) => PixelDataType.UnsignedInt32,
            (32, 1) => PixelDataType.SignedInt32,
            _ => PixelDataType.Unknown
        };
    }

    /// <summary>
    /// 确定数组类型
    /// </summary>
    private static Type DetermineArrayType(DicomInstance instance)
    {
        return (instance.BitsAllocated, instance.PixelRepresentation) switch
        {
            (8, 0) => typeof(byte),
            (8, 1) => typeof(sbyte),
            (16, 0) => typeof(ushort),
            (16, 1) => typeof(short),
            (32, 0) => typeof(uint),
            (32, 1) => typeof(int),
            _ => typeof(ushort)
        };
    }

    /// <summary>
    /// 解析 DICOM 日期时间
    /// </summary>
    private static DateTime ParseDicomDateTime(string date, string time)
    {
        try
        {
            var dateTime = DateTime.UtcNow;

            if (!string.IsNullOrEmpty(date) && date.Length >= 8)
            {
                var year = int.Parse(date.Substring(0, 4));
                var month = int.Parse(date.Substring(4, 2));
                var day = int.Parse(date.Substring(6, 2));
                dateTime = new DateTime(year, month, day);
            }

            if (!string.IsNullOrEmpty(time) && time.Length >= 6)
            {
                var hour = int.Parse(time.Substring(0, 2));
                var minute = int.Parse(time.Substring(2, 2));
                var second = int.Parse(time.Substring(4, 2));

                var millisecond = 0;
                if (time.Length > 6 && time[6] == '.')
                {
                    var fractionStr = time.Substring(7);
                    if (fractionStr.Length >= 3)
                    {
                        millisecond = int.Parse(fractionStr.Substring(0, 3));
                    }
                }

                dateTime = dateTime.AddHours(hour).AddMinutes(minute).AddSeconds(second).AddMilliseconds(millisecond);
            }

            return dateTime;
        }
        catch
        {
            return DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 解析 DICOM 日期
    /// </summary>
    private static DateTime? ParseDicomDate(string date)
    {
        try
        {
            if (string.IsNullOrEmpty(date) || date.Length < 8)
                return null;

            var year = int.Parse(date.Substring(0, 4));
            var month = int.Parse(date.Substring(4, 2));
            var day = int.Parse(date.Substring(6, 2));

            return new DateTime(year, month, day);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 解析性别
    /// </summary>
    private static Gender ParseGender(string sex)
    {
        return sex?.ToUpperInvariant() switch
        {
            "M" => Gender.Male,
            "F" => Gender.Female,
            "O" => Gender.Other,
            _ => Gender.Unknown
        };
    }

    /// <summary>
    /// 计算文件哈希值
    /// </summary>
    private static async Task<string> CalculateFileHashAsync(string filePath)
    {
        using var sha256 = SHA256.Create();
        using var stream = File.OpenRead(filePath);
        var hash = await sha256.ComputeHashAsync(stream);
        return Convert.ToHexString(hash);
    }

    #endregion
}
