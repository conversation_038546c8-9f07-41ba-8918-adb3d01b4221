using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 智能标注服务接口
/// </summary>
public interface ISmartAnnotationService
{
    /// <summary>
    /// 智能标注生成
    /// </summary>
    Task<SmartAnnotationResult> GenerateSmartAnnotationsAsync(
        DicomInstance instance, 
        SmartAnnotationConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 自适应标注优化
    /// </summary>
    Task<List<Annotation>> AdaptiveAnnotationOptimizationAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        AdaptiveOptimizationConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 标注质量自动评估
    /// </summary>
    Task<AnnotationQualityReport> AutoEvaluateAnnotationQualityAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        QualityEvaluationConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 智能标注推荐
    /// </summary>
    Task<List<AnnotationRecommendation>> GenerateSmartRecommendationsAsync(
        DicomInstance instance, 
        List<Annotation> existingAnnotations,
        SmartRecommendationConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 标注数据增强
    /// </summary>
    Task<AnnotationAugmentationResult> AugmentAnnotationDataAsync(
        List<Annotation> annotations, 
        DicomInstance instance,
        AnnotationAugmentationConfig config,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 智能标注配置
/// </summary>
public class SmartAnnotationConfig
{
    public List<AutoAnnotationConfig> ModelConfigs { get; set; } = new();
    public AnnotationFusionConfig FusionConfig { get; set; } = new();
    public PostProcessingConfig PostProcessingConfig { get; set; } = new();
    public SmartFilterConfig FilterConfig { get; set; } = new();
    public bool EnableMultiModelFusion { get; set; } = true;
    public bool EnableQualityAssessment { get; set; } = true;
    public bool EnableSmartFiltering { get; set; } = true;
}

/// <summary>
/// 标注融合配置
/// </summary>
public class AnnotationFusionConfig
{
    public FusionMethod Method { get; set; } = FusionMethod.WeightedAverage;
    public double OverlapThreshold { get; set; } = 0.5;
    public Dictionary<string, double> ModelWeights { get; set; } = new();
    public bool EnableConsensusFiltering { get; set; } = true;
    public int MinConsensusCount { get; set; } = 2;
}

/// <summary>
/// 后处理配置
/// </summary>
public class PostProcessingConfig
{
    public bool OptimizeBoundingBoxes { get; set; } = true;
    public bool CalibrateConfidence { get; set; } = true;
    public bool RemoveOverlaps { get; set; } = true;
    public bool ApplyNMS { get; set; } = true;
    public double NMSThreshold { get; set; } = 0.5;
    public Dictionary<string, object> CalibrationParameters { get; set; } = new();
}

/// <summary>
/// 智能过滤配置
/// </summary>
public class SmartFilterConfig
{
    public double MinQualityThreshold { get; set; } = 0.5;
    public double MinConfidenceThreshold { get; set; } = 0.3;
    public bool EnableSizeFiltering { get; set; } = true;
    public (double Min, double Max) SizeRange { get; set; } = (0.01, 0.8);
    public bool EnableAspectRatioFiltering { get; set; } = true;
    public (double Min, double Max) AspectRatioRange { get; set; } = (0.1, 10.0);
}

/// <summary>
/// 自适应优化配置
/// </summary>
public class AdaptiveOptimizationConfig
{
    public bool EnableEdgeRefinement { get; set; } = true;
    public bool EnableShapeOptimization { get; set; } = true;
    public bool EnableContextualAdjustment { get; set; } = true;
    public double OptimizationStrength { get; set; } = 0.5;
    public int MaxIterations { get; set; } = 10;
}

/// <summary>
/// 质量评估配置
/// </summary>
public class QualityEvaluationConfig
{
    public bool EvaluateGeometry { get; set; } = true;
    public bool EvaluateSemantics { get; set; } = true;
    public bool EvaluateConsistency { get; set; } = true;
    public bool EvaluateCompleteness { get; set; } = true;
    public Dictionary<string, double> QualityWeights { get; set; } = new()
    {
        ["geometry"] = 0.3,
        ["semantics"] = 0.3,
        ["consistency"] = 0.2,
        ["completeness"] = 0.2
    };
}

/// <summary>
/// 智能推荐配置
/// </summary>
public class SmartRecommendationConfig
{
    public bool EnableContextRecommendations { get; set; } = true;
    public bool EnablePatternRecommendations { get; set; } = true;
    public bool EnableAnatomyRecommendations { get; set; } = true;
    public bool EnableHistoryRecommendations { get; set; } = true;
    public int MaxRecommendations { get; set; } = 10;
    public double MinRecommendationScore { get; set; } = 0.5;
}

/// <summary>
/// 标注增强配置
/// </summary>
public class AnnotationAugmentationConfig
{
    public bool EnableGeometricAugmentation { get; set; } = true;
    public bool EnableImageAugmentation { get; set; } = true;
    public bool EnableNoiseAugmentation { get; set; } = true;
    public bool EnableSyntheticGeneration { get; set; } = false;
    public GeometricAugmentationConfig GeometricConfig { get; set; } = new();
    public ImageAugmentationConfig ImageConfig { get; set; } = new();
    public NoiseAugmentationConfig NoiseConfig { get; set; } = new();
    public SyntheticGenerationConfig SyntheticConfig { get; set; } = new();
    public int AugmentationFactor { get; set; } = 5;
}



/// <summary>
/// 图像增强配置
/// </summary>
public class ImageAugmentationConfig
{
    public bool EnableBrightnessAdjustment { get; set; } = true;
    public (double Min, double Max) BrightnessRange { get; set; } = (0.8, 1.2);
    public bool EnableContrastAdjustment { get; set; } = true;
    public (double Min, double Max) ContrastRange { get; set; } = (0.8, 1.2);
    public bool EnableGammaCorrection { get; set; } = true;
    public (double Min, double Max) GammaRange { get; set; } = (0.8, 1.2);
    public bool EnableHistogramEqualization { get; set; } = false;
}



/// <summary>
/// 合成生成配置
/// </summary>
public class SyntheticGenerationConfig
{
    public bool EnableGANGeneration { get; set; } = false;
    public string GANModelPath { get; set; } = "";
    public bool EnableTemplateMatching { get; set; } = true;
    public List<string> TemplatePaths { get; set; } = new();
    public int SyntheticSamplesPerOriginal { get; set; } = 2;
}

/// <summary>
/// 融合方法枚举
/// </summary>
public enum FusionMethod
{
    WeightedAverage = 1,
    MaxConfidence = 2,
    Consensus = 3,
    BayesianFusion = 4,
    EnsembleVoting = 5
}

/// <summary>
/// 智能标注结果
/// </summary>
public class SmartAnnotationResult
{
    public Guid InstanceId { get; set; }
    public DateTime StartTime { get; set; }
    public double ProcessingTimeMs { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    
    public List<List<Annotation>> RawAnnotations { get; set; } = new();
    public List<Annotation> FusedAnnotations { get; set; } = new();
    public List<Annotation> OptimizedAnnotations { get; set; } = new();
    public List<Annotation> FinalAnnotations { get; set; } = new();
    public List<AnnotationQualityScore> QualityScores { get; set; } = new();
}

/// <summary>
/// 标注质量分数
/// </summary>
public class AnnotationQualityScore
{
    public Guid AnnotationId { get; set; }
    public double GeometricScore { get; set; }
    public double SemanticScore { get; set; }
    public double ConfidenceScore { get; set; }
    public double ConsistencyScore { get; set; }
    public double OverallScore { get; set; }
}

/// <summary>
/// 标注质量报告
/// </summary>
public class AnnotationQualityReport
{
    public Guid InstanceId { get; set; }
    public DateTime EvaluationTime { get; set; }
    public int TotalAnnotations { get; set; }
    public double OverallQualityScore { get; set; }
    
    public List<AnnotationQualityScore> GeometricQualityScores { get; set; } = new();
    public List<AnnotationQualityScore> SemanticQualityScores { get; set; } = new();
    public List<AnnotationQualityScore> ConsistencyScores { get; set; } = new();
    public double CompletenessScore { get; set; }
    
    public List<string> ImprovementSuggestions { get; set; } = new();
}

/// <summary>
/// 标注增强结果
/// </summary>
public class AnnotationAugmentationResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public List<Annotation> OriginalAnnotations { get; set; } = new();
    public List<AugmentedAnnotationData> AugmentedData { get; set; } = new();
    public int TotalAugmentedSamples { get; set; }
    public AnnotationAugmentationConfig AugmentationConfig { get; set; } = new();
}

/// <summary>
/// 增强标注数据
/// </summary>
public class AugmentedAnnotationData
{
    public Guid OriginalAnnotationId { get; set; }
    public Annotation AugmentedAnnotation { get; set; } = new();
    public string AugmentationType { get; set; } = "";
    public Dictionary<string, object> AugmentationParameters { get; set; } = new();
    public byte[]? AugmentedImageData { get; set; }
}
