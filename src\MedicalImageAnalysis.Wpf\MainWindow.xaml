﻿<Window x:Class="MedicalImageAnalysis.Wpf.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MedicalImageAnalysis.Wpf"
        mc:Ignorable="d"
        Title="医学影像解析系统"
        Height="900"
        Width="1400"
        MinHeight="600"
        MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        FontSize="13"
        Background="White">

    <!-- 移除Window.Resources，使用App.xaml中的全局资源 -->

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <Border Grid.Row="0" Background="DodgerBlue" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🏥"
                             FontSize="32"
                             VerticalAlignment="Center"
                             Margin="0,0,12,0"/>
                    <TextBlock Text="医学影像解析系统"
                             FontSize="24"
                             FontWeight="Medium"
                             VerticalAlignment="Center"
                             Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="设置"
                            ToolTip="设置"
                            Margin="8,0"
                            Padding="8,4"
                            Background="Transparent"
                            Foreground="White"
                            BorderBrush="White"
                            Click="SettingsButton_Click"/>
                    <Button Content="帮助"
                            ToolTip="帮助"
                            Margin="8,0"
                            Padding="8,4"
                            Background="Transparent"
                            Foreground="White"
                            BorderBrush="White"
                            Click="HelpButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航栏 -->
            <Border Grid.Column="0"
                    Background="LightGray"
                    Margin="8,8,4,8"
                    BorderBrush="Gray"
                    BorderThickness="1">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <TextBlock Text="功能导航"
                                 Style="{StaticResource HeaderTextStyle}"
                                 Margin="16,16,16,8"/>

                        <!-- 导航菜单项 -->
                        <ListBox x:Name="NavigationListBox"
                               SelectionChanged="NavigationListBox_SelectionChanged"
                               Background="Transparent"
                               BorderThickness="0">

                            <ListBoxItem x:Name="HomeMenuItem">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🏠"
                                             FontSize="16"
                                             Margin="0,0,12,0"/>
                                    <TextBlock Text="首页"/>
                                </StackPanel>
                            </ListBoxItem>

                            <ListBoxItem x:Name="DicomUploadMenuItem">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📤"
                                             FontSize="16"
                                             Margin="0,0,12,0"/>
                                    <TextBlock Text="DICOM 上传"/>
                                </StackPanel>
                            </ListBoxItem>

                            <ListBoxItem x:Name="GdcmViewerMenuItem">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🔬"
                                             FontSize="16"
                                             Margin="0,0,12,0"/>
                                    <TextBlock Text="GDCM 查看器"/>
                                </StackPanel>
                            </ListBoxItem>

                            <ListBoxItem x:Name="ImageProcessingMenuItem">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🖼️"
                                             FontSize="16"
                                             Margin="0,0,12,0"/>
                                    <TextBlock Text="影像处理"/>
                                </StackPanel>
                            </ListBoxItem>

                            <ListBoxItem x:Name="AnnotationMenuItem">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🏷️"
                                             FontSize="16"
                                             Margin="0,0,12,0"/>
                                    <TextBlock Text="智能标注"/>
                                </StackPanel>
                            </ListBoxItem>

                            <ListBoxItem x:Name="ModelTrainingMenuItem">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🧠"
                                             FontSize="16"
                                             Margin="0,0,12,0"/>
                                    <TextBlock Text="模型训练"/>
                                </StackPanel>
                            </ListBoxItem>

                            <ListBoxItem x:Name="StatisticsMenuItem">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📊"
                                             FontSize="16"
                                             Margin="0,0,12,0"/>
                                    <TextBlock Text="统计分析"/>
                                </StackPanel>
                            </ListBoxItem>

                            <ListBoxItem x:Name="DirectoryMenuItem">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📁"
                                             FontSize="16"
                                             Margin="0,0,12,0"/>
                                    <TextBlock Text="目录管理"/>
                                </StackPanel>
                            </ListBoxItem>
                        </ListBox>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- 右侧内容区域 -->
            <Border Grid.Column="1"
                    Background="White"
                    Margin="4,8,8,8"
                    BorderBrush="Gray"
                    BorderThickness="1">
                <Grid>
                    <ContentControl x:Name="MainContentControl"/>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部状态栏 -->
        <Border Grid.Row="2"
                Background="LightBlue"
                Padding="16,8"
                BorderBrush="Gray"
                BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="StatusTextBlock"
                         Grid.Column="0"
                         Text="就绪"
                         VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1"
                          Orientation="Horizontal">
                    <TextBlock Text="版本: 1.0.0"
                             VerticalAlignment="Center"
                             Margin="0,0,16,0"/>
                    <TextBlock Text="●"
                             Foreground="Green"
                             VerticalAlignment="Center"/>
                    <TextBlock Text="在线"
                             VerticalAlignment="Center"
                             Margin="4,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
