<UserControl x:Class="MedicalImageAnalysis.WpfClient.Views.DicomViewerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,8">
            <StackPanel Orientation="Horizontal">
                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding OpenDicomCommand}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FolderOpen" Width="16" Height="16"/>
                        <TextBlock Text="打开DICOM" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding SaveImageCommand}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16"/>
                        <TextBlock Text="保存图像" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>

                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0"/>

                <Button Style="{StaticResource MaterialDesignToolButton}"
                        Command="{Binding ZoomInCommand}"
                        ToolTip="放大">
                    <materialDesign:PackIcon Kind="MagnifyPlus" Width="20" Height="20"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignToolButton}"
                        Command="{Binding ZoomOutCommand}"
                        ToolTip="缩小">
                    <materialDesign:PackIcon Kind="MagnifyMinus" Width="20" Height="20"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignToolButton}"
                        Command="{Binding ResetZoomCommand}"
                        ToolTip="重置缩放">
                    <materialDesign:PackIcon Kind="MagnifyRemoveOutline" Width="20" Height="20"/>
                </Button>

                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0"/>

                <TextBlock Text="窗宽:" VerticalAlignment="Center" Margin="8,0,4,0"/>
                <TextBox Text="{Binding WindowWidth}" Width="80" Margin="0,0,8,0"/>

                <TextBlock Text="窗位:" VerticalAlignment="Center" Margin="8,0,4,0"/>
                <TextBox Text="{Binding WindowCenter}" Width="80" Margin="0,0,8,0"/>

                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ApplyWindowLevelCommand}"
                        Margin="8,0,0,0">
                    <TextBlock Text="应用"/>
                </Button>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="250"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧文件列表 -->
            <materialDesign:Card Grid.Column="0" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="DICOM文件" 
                             Style="{StaticResource SubtitleText}" 
                             Margin="16,16,16,8"/>

                    <ListBox Grid.Row="1" 
                           ItemsSource="{Binding DicomFiles}"
                           SelectedItem="{Binding SelectedDicomFile}"
                           Style="{StaticResource CustomListBox}"
                           Margin="8">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Margin="8">
                                    <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                    <TextBlock Text="{Binding Size, Converter={StaticResource ByteSizeConverter}}" 
                                             Style="{StaticResource CaptionText}"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </materialDesign:Card>

            <!-- 中间图像显示区域 -->
            <materialDesign:Card Grid.Column="1" Margin="4,8" materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <ScrollViewer x:Name="ImageScrollViewer"
                                HorizontalScrollBarVisibility="Auto"
                                VerticalScrollBarVisibility="Auto">
                        <Image x:Name="DicomImage" 
                             Source="{Binding CurrentImage}"
                             Stretch="Uniform"
                             RenderOptions.BitmapScalingMode="HighQuality"/>
                    </ScrollViewer>

                    <!-- 图像信息覆盖层 -->
                    <StackPanel HorizontalAlignment="Left" 
                              VerticalAlignment="Top" 
                              Background="#80000000" 
                              Margin="16"
                              Visibility="{Binding ShowImageInfo, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding ImageInfo}" 
                                 Foreground="White" 
                                 FontFamily="{StaticResource MonospaceFont}"
                                 FontSize="12" 
                                 Margin="8"/>
                    </StackPanel>

                    <!-- 加载指示器 -->
                    <Grid Background="#80FFFFFF" 
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="Loading" 
                                                   Width="48" Height="48"
                                                   HorizontalAlignment="Center"
                                                   Foreground="{StaticResource PrimaryBrush}"/>
                            <TextBlock Text="加载中..." 
                                     HorizontalAlignment="Center"
                                     Margin="0,16,0,0"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- 右侧属性面板 -->
            <materialDesign:Card Grid.Column="2" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <TextBlock Text="DICOM信息" Style="{StaticResource SubtitleText}"/>

                        <!-- 患者信息 -->
                        <Expander Header="患者信息" IsExpanded="True" Margin="0,8,0,0">
                            <StackPanel Margin="16,8,0,8">
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="姓名:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding PatientName}"/>
                                </Grid>
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="ID:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding PatientId}"/>
                                </Grid>
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="性别:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding PatientSex}"/>
                                </Grid>
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="年龄:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding PatientAge}"/>
                                </Grid>
                            </StackPanel>
                        </Expander>

                        <!-- 检查信息 -->
                        <Expander Header="检查信息" IsExpanded="True" Margin="0,8,0,0">
                            <StackPanel Margin="16,8,0,8">
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="模态:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding Modality}"/>
                                </Grid>
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="日期:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding StudyDate, StringFormat=yyyy-MM-dd}"/>
                                </Grid>
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="描述:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding StudyDescription}" TextWrapping="Wrap"/>
                                </Grid>
                            </StackPanel>
                        </Expander>

                        <!-- 图像信息 -->
                        <Expander Header="图像信息" IsExpanded="True" Margin="0,8,0,0">
                            <StackPanel Margin="16,8,0,8">
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="尺寸:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding ImageDimensions}"/>
                                </Grid>
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="像素:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding PixelSpacing}"/>
                                </Grid>
                                <Grid Margin="0,4">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="层厚:" FontWeight="Medium" Width="60"/>
                                    <TextBlock Grid.Column="1" Text="{Binding SliceThickness}"/>
                                </Grid>
                            </StackPanel>
                        </Expander>

                        <!-- 操作按钮 -->
                        <StackPanel Margin="0,16,0,0">
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Command="{Binding ShowTagsCommand}"
                                    HorizontalAlignment="Stretch"
                                    Margin="0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Tag" Width="16" Height="16"/>
                                    <TextBlock Text="查看标签" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Command="{Binding ExportImageCommand}"
                                    HorizontalAlignment="Stretch"
                                    Margin="0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Export" Width="16" Height="16"/>
                                    <TextBlock Text="导出图像" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Command="{Binding RunAnalysisCommand}"
                                    HorizontalAlignment="Stretch"
                                    Margin="0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Brain" Width="16" Height="16"/>
                                    <TextBlock Text="AI分析" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>

        <!-- 状态栏 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="16,4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                         Text="{Binding StatusMessage}" 
                         VerticalAlignment="Center"
                         Foreground="White"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding ZoomLevel, StringFormat=缩放: {0:P0}}" 
                             VerticalAlignment="Center"
                             Foreground="White"
                             Margin="0,0,16,0"/>
                    <TextBlock Text="{Binding MousePosition}" 
                             VerticalAlignment="Center"
                             Foreground="White"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</UserControl>
