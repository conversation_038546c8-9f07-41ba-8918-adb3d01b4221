<UserControl x:Class="MedicalImageAnalysis.WpfClient.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <ScrollViewer Style="{StaticResource CustomScrollViewer}">
        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,24">
                <TextBlock Text="医学影像解析系统" Style="{StaticResource TitleText}"/>
                <TextBlock Text="欢迎使用医学影像解析系统，这是一个基于YOLOv11的智能医学影像分析平台。" 
                         Style="{StaticResource BodyText}"/>
            </StackPanel>

            <!-- 统计卡片区域 -->
            <Grid Grid.Row="1" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- DICOM文件数量 -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="DICOM文件" Style="{StaticResource CaptionText}"/>
                                <TextBlock Text="1,234" FontSize="28" FontWeight="Bold" 
                                         Foreground="{StaticResource PrimaryBrush}"/>
                            </StackPanel>
                            
                            <materialDesign:PackIcon Grid.Column="1" Kind="FileImage" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource PrimaryBrush}"
                                                   VerticalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="本月新增 +156" Style="{StaticResource CaptionText}" 
                                 Foreground="{StaticResource SuccessBrush}" Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 训练模型数量 -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="训练模型" Style="{StaticResource CaptionText}"/>
                                <TextBlock Text="42" FontSize="28" FontWeight="Bold" 
                                         Foreground="{StaticResource SecondaryBrush}"/>
                            </StackPanel>
                            
                            <materialDesign:PackIcon Grid.Column="1" Kind="Brain" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource SecondaryBrush}"
                                                   VerticalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="本月新增 +8" Style="{StaticResource CaptionText}" 
                                 Foreground="{StaticResource SuccessBrush}" Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 标注数量 -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="智能标注" Style="{StaticResource CaptionText}"/>
                                <TextBlock Text="5,678" FontSize="28" FontWeight="Bold" 
                                         Foreground="{StaticResource AccentBrush}"/>
                            </StackPanel>
                            
                            <materialDesign:PackIcon Grid.Column="1" Kind="Draw" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource AccentBrush}"
                                                   VerticalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="本月新增 +892" Style="{StaticResource CaptionText}" 
                                 Foreground="{StaticResource SuccessBrush}" Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 系统状态 -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="系统状态" Style="{StaticResource CaptionText}"/>
                                <TextBlock Text="正常" FontSize="28" FontWeight="Bold" 
                                         Foreground="{StaticResource SuccessBrush}"/>
                            </StackPanel>
                            
                            <materialDesign:PackIcon Grid.Column="1" Kind="CheckCircle" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource SuccessBrush}"
                                                   VerticalAlignment="Center"/>
                        </Grid>
                        <TextBlock Text="运行时间 24天" Style="{StaticResource CaptionText}" 
                                 Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- 功能区域 -->
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧功能卡片 -->
                <StackPanel Grid.Column="0" Margin="0,0,12,0">
                    <TextBlock Text="主要功能" Style="{StaticResource SubtitleText}"/>
                    
                    <UniformGrid Columns="2" Margin="0,16,0,0">
                        <!-- DICOM查看器 -->
                        <materialDesign:Card Style="{StaticResource FeatureCard}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileImage" 
                                                       Width="48" Height="48"
                                                       Foreground="{StaticResource PrimaryBrush}"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock Text="DICOM查看器" FontWeight="Medium" FontSize="16"/>
                                    <TextBlock Text="查看和分析DICOM医学影像文件" 
                                             Style="{StaticResource CaptionText}" TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 模型训练 -->
                        <materialDesign:Card Style="{StaticResource FeatureCard}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="School" 
                                                       Width="48" Height="48"
                                                       Foreground="{StaticResource SecondaryBrush}"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock Text="模型训练" FontWeight="Medium" FontSize="16"/>
                                    <TextBlock Text="训练YOLOv11深度学习模型" 
                                             Style="{StaticResource CaptionText}" TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 智能标注 -->
                        <materialDesign:Card Style="{StaticResource FeatureCard}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Draw" 
                                                       Width="48" Height="48"
                                                       Foreground="{StaticResource AccentBrush}"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock Text="智能标注" FontWeight="Medium" FontSize="16"/>
                                    <TextBlock Text="自动生成和优化图像标注" 
                                             Style="{StaticResource CaptionText}" TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 数据管理 -->
                        <materialDesign:Card Style="{StaticResource FeatureCard}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Database" 
                                                       Width="48" Height="48"
                                                       Foreground="{StaticResource WarningBrush}"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock Text="数据管理" FontWeight="Medium" FontSize="16"/>
                                    <TextBlock Text="管理医学影像数据和元数据" 
                                             Style="{StaticResource CaptionText}" TextWrapping="Wrap"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </UniformGrid>
                </StackPanel>

                <!-- 右侧系统信息 -->
                <StackPanel Grid.Column="1" Margin="12,0,0,0">
                    <TextBlock Text="系统信息" Style="{StaticResource SubtitleText}"/>
                    
                    <!-- 系统监控 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}" Margin="0,16,0,0">
                        <StackPanel>
                            <TextBlock Text="系统监控" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <!-- CPU使用率 -->
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="CPU" VerticalAlignment="Center"/>
                                <ProgressBar Grid.Column="1" Value="45" Maximum="100" 
                                           Style="{StaticResource CustomProgressBar}" Margin="8,0"/>
                                <TextBlock Grid.Column="2" Text="45%" VerticalAlignment="Center"/>
                            </Grid>
                            
                            <!-- 内存使用率 -->
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="内存" VerticalAlignment="Center"/>
                                <ProgressBar Grid.Column="1" Value="68" Maximum="100" 
                                           Style="{StaticResource CustomProgressBar}" Margin="8,0"/>
                                <TextBlock Grid.Column="2" Text="68%" VerticalAlignment="Center"/>
                            </Grid>
                            
                            <!-- 磁盘使用率 -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="磁盘" VerticalAlignment="Center"/>
                                <ProgressBar Grid.Column="1" Value="32" Maximum="100" 
                                           Style="{StaticResource CustomProgressBar}" Margin="8,0"/>
                                <TextBlock Grid.Column="2" Text="32%" VerticalAlignment="Center"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 最近活动 -->
                    <materialDesign:Card Style="{StaticResource InfoCard}">
                        <StackPanel>
                            <TextBlock Text="最近活动" FontWeight="Medium" Margin="0,0,0,16"/>
                            
                            <StackPanel>
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Ellipse Grid.Column="0" Style="{StaticResource StatusIndicator}" 
                                           Fill="{StaticResource SuccessBrush}"/>
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="模型训练完成" FontSize="12" FontWeight="Medium"/>
                                        <TextBlock Text="2分钟前" Style="{StaticResource CaptionText}"/>
                                    </StackPanel>
                                </Grid>
                                
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Ellipse Grid.Column="0" Style="{StaticResource StatusIndicator}" 
                                           Fill="{StaticResource PrimaryBrush}"/>
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="DICOM文件上传" FontSize="12" FontWeight="Medium"/>
                                        <TextBlock Text="5分钟前" Style="{StaticResource CaptionText}"/>
                                    </StackPanel>
                                </Grid>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Ellipse Grid.Column="0" Style="{StaticResource StatusIndicator}" 
                                           Fill="{StaticResource AccentBrush}"/>
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="智能标注生成" FontSize="12" FontWeight="Medium"/>
                                        <TextBlock Text="10分钟前" Style="{StaticResource CaptionText}"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
