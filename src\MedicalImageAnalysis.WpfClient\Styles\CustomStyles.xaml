<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 自定义按钮样式 -->
    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
    </Style>

    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 自定义卡片样式 -->
    <Style x:Key="InfoCard" TargetType="materialDesign:Card">
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
    </Style>

    <Style x:Key="FeatureCard" TargetType="materialDesign:Card" BasedOn="{StaticResource InfoCard}">
        <Setter Property="Cursor" Value="Hand"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 自定义文本样式 -->
    <Style x:Key="TitleText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <Style x:Key="SubtitleText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <Style x:Key="BodyText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>

    <Style x:Key="CaptionText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
    </Style>

    <!-- 自定义输入框样式 -->
    <Style x:Key="CustomTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 自定义列表样式 -->
    <Style x:Key="CustomListBox" TargetType="ListBox" BasedOn="{StaticResource MaterialDesignListBox}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
    </Style>

    <!-- 自定义进度条样式 -->
    <Style x:Key="CustomProgressBar" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="Height" Value="6"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicator" TargetType="Ellipse">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="Margin" Value="4"/>
    </Style>

    <!-- 工具栏样式 -->
    <Style x:Key="ToolbarButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignToolButton}">
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- 数据网格样式 -->
    <Style x:Key="CustomDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource MaterialDesignDivider}"/>
    </Style>

    <!-- 分组框样式 -->
    <Style x:Key="CustomGroupBox" TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignGroupBox}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="PrimaryLight"/>
    </Style>

    <!-- 选项卡样式 -->
    <Style x:Key="CustomTabControl" TargetType="TabControl" BasedOn="{StaticResource MaterialDesignTabControl}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="PrimaryMid"/>
    </Style>

    <!-- 菜单样式 -->
    <Style x:Key="CustomMenu" TargetType="Menu" BasedOn="{StaticResource MaterialDesignMenu}">
        <Setter Property="Background" Value="Transparent"/>
    </Style>

    <!-- 工具提示样式 -->
    <Style x:Key="CustomToolTip" TargetType="ToolTip" BasedOn="{StaticResource MaterialDesignToolTip}">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="FontSize" Value="12"/>
    </Style>

    <!-- 滚动条样式 -->
    <Style x:Key="CustomScrollViewer" TargetType="ScrollViewer" BasedOn="{StaticResource MaterialDesignScrollViewer}">
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="Padding" Value="0"/>
    </Style>

    <!-- 分隔符样式 -->
    <Style x:Key="CustomSeparator" TargetType="Separator" BasedOn="{StaticResource MaterialDesignSeparator}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
    </Style>

    <!-- 复选框样式 -->
    <Style x:Key="CustomCheckBox" TargetType="CheckBox" BasedOn="{StaticResource MaterialDesignCheckBox}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 单选按钮样式 -->
    <Style x:Key="CustomRadioButton" TargetType="RadioButton" BasedOn="{StaticResource MaterialDesignRadioButton}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 滑块样式 -->
    <Style x:Key="CustomSlider" TargetType="Slider" BasedOn="{StaticResource MaterialDesignSlider}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Height" Value="20"/>
    </Style>

    <!-- 组合框样式 -->
    <Style x:Key="CustomComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 日期选择器样式 -->
    <Style x:Key="CustomDatePicker" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 时间选择器样式 -->
    <Style x:Key="CustomTimePicker" TargetType="materialDesign:TimePicker" BasedOn="{StaticResource MaterialDesignOutlinedTimePicker}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

</ResourceDictionary>
