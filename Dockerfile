# 医学影像解析系统 - 多阶段Docker构建

# 第一阶段：Python环境准备
FROM python:3.11-slim AS python-base

# 安装Python系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgtk-3-0 \
    && rm -rf /var/lib/apt/lists/*

# 安装Python包
RUN pip install --no-cache-dir \
    ultralytics>=8.3.0 \
    opencv-python-headless \
    pillow \
    numpy \
    torch \
    torchvision \
    torchaudio \
    --index-url https://download.pytorch.org/whl/cpu

# 第二阶段：.NET运行时基础镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 5000 5001

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgdiplus \
    libc6-dev \
    python3 \
    python3-pip \
    python3-venv \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 创建Python虚拟环境
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 第三阶段：构建环境
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制项目文件
COPY ["src/MedicalImageAnalysis.Api/MedicalImageAnalysis.Api.csproj", "src/MedicalImageAnalysis.Api/"]
COPY ["src/MedicalImageAnalysis.Web/MedicalImageAnalysis.Web.csproj", "src/MedicalImageAnalysis.Web/"]
COPY ["src/MedicalImageAnalysis.Application/MedicalImageAnalysis.Application.csproj", "src/MedicalImageAnalysis.Application/"]
COPY ["src/MedicalImageAnalysis.Infrastructure/MedicalImageAnalysis.Infrastructure.csproj", "src/MedicalImageAnalysis.Infrastructure/"]
COPY ["src/MedicalImageAnalysis.Core/MedicalImageAnalysis.Core.csproj", "src/MedicalImageAnalysis.Core/"]

# 还原NuGet包
RUN dotnet restore "src/MedicalImageAnalysis.Api/MedicalImageAnalysis.Api.csproj"
RUN dotnet restore "src/MedicalImageAnalysis.Web/MedicalImageAnalysis.Web.csproj"

# 复制所有源代码
COPY . .

# 构建API项目
WORKDIR "/src/src/MedicalImageAnalysis.Api"
RUN dotnet build "MedicalImageAnalysis.Api.csproj" -c Release -o /app/build/api

# 构建Web项目
WORKDIR "/src/src/MedicalImageAnalysis.Web"
RUN dotnet build "MedicalImageAnalysis.Web.csproj" -c Release -o /app/build/web

# 第四阶段：发布环境
FROM build AS publish

# 发布API项目
WORKDIR "/src/src/MedicalImageAnalysis.Api"
RUN dotnet publish "MedicalImageAnalysis.Api.csproj" -c Release -o /app/publish/api --no-restore

# 发布Web项目
WORKDIR "/src/src/MedicalImageAnalysis.Web"
RUN dotnet publish "MedicalImageAnalysis.Web.csproj" -c Release -o /app/publish/web --no-restore

# 第五阶段：最终运行时镜像
FROM base AS final
WORKDIR /app

# 从Python阶段复制已安装的包
COPY --from=python-base /usr/local/lib/python3.11/site-packages /opt/venv/lib/python3.11/site-packages
COPY --from=python-base /usr/local/bin /opt/venv/bin

# 创建必要的目录
RUN mkdir -p /app/data/dicom \
    && mkdir -p /app/data/models \
    && mkdir -p /app/data/temp \
    && mkdir -p /app/data/output \
    && mkdir -p /app/data/logs \
    && mkdir -p /app/scripts

# 复制发布的应用程序
COPY --from=publish /app/publish/api ./api
COPY --from=publish /app/publish/web ./web

# 复制Python脚本
COPY src/MedicalImageAnalysis.Api/scripts/ ./scripts/

# 设置权限
RUN chmod +x ./scripts/yolo/*.py

# 创建启动脚本
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 启动API服务\n\
echo "启动医学影像解析API服务..."\n\
cd /app/api\n\
dotnet MedicalImageAnalysis.Api.dll --urls "http://0.0.0.0:5000" &\n\
API_PID=$!\n\
\n\
# 启动Web服务\n\
echo "启动医学影像解析Web服务..."\n\
cd /app/web\n\
dotnet MedicalImageAnalysis.Web.dll --urls "http://0.0.0.0:5001" &\n\
WEB_PID=$!\n\
\n\
# 等待服务启动\n\
sleep 10\n\
\n\
# 健康检查\n\
echo "执行健康检查..."\n\
curl -f http://localhost:5000/health || exit 1\n\
curl -f http://localhost:5001/health || exit 1\n\
\n\
echo "所有服务已启动"\n\
\n\
# 等待进程\n\
wait $API_PID $WEB_PID\n\
' > /app/start.sh && chmod +x /app/start.sh

# 环境变量
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:5000;http://+:5001
ENV PYTHONPATH=/opt/venv/lib/python3.11/site-packages
ENV PATH="/opt/venv/bin:$PATH"

# 数据卷
VOLUME ["/app/data"]

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health && curl -f http://localhost:5001/health || exit 1

# 启动命令
CMD ["/app/start.sh"]
