<UserControl x:Class="MedicalImageAnalysis.WpfClient.Views.AnnotationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,8">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Draw" Width="24" Height="24" VerticalAlignment="Center"/>
                <TextBlock Text="智能标注系统" 
                         FontSize="18" 
                         FontWeight="Medium"
                         VerticalAlignment="Center"
                         Margin="12,0,24,0"/>

                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding LoadImagesCommand}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FolderOpen" Width="16" Height="16"/>
                        <TextBlock Text="加载图像" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding LoadModelCommand}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Brain" Width="16" Height="16"/>
                        <TextBlock Text="加载模型" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>

                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0"/>

                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding AutoAnnotateCommand}"
                        IsEnabled="{Binding CanAutoAnnotate}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AutoFix" Width="16" Height="16"/>
                        <TextBlock Text="自动标注" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>

                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding SaveAnnotationsCommand}"
                        Margin="0,0,8,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16"/>
                        <TextBlock Text="保存标注" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧图像列表 -->
            <materialDesign:Card Grid.Column="0" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="图像列表" 
                             Style="{StaticResource SubtitleText}" 
                             Margin="16,16,16,8"/>

                    <ListBox Grid.Row="1" 
                           ItemsSource="{Binding Images}"
                           SelectedItem="{Binding SelectedImage}"
                           Style="{StaticResource CustomListBox}"
                           Margin="8">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Margin="8">
                                    <Image Source="{Binding Thumbnail}" 
                                         Width="150" Height="100" 
                                         Stretch="UniformToFill"/>
                                    <TextBlock Text="{Binding Name}" 
                                             FontWeight="Medium"
                                             TextTrimming="CharacterEllipsis"
                                             Margin="0,4,0,0"/>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Tag" 
                                                               Width="12" Height="12"
                                                               VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding AnnotationCount}" 
                                                 Style="{StaticResource CaptionText}"
                                                 Margin="4,0,0,0"/>
                                    </StackPanel>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <StackPanel Grid.Row="2" Margin="8">
                        <TextBlock Text="{Binding ImageCount, StringFormat=总计: {0} 张图像}" 
                                 Style="{StaticResource CaptionText}"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding AnnotatedCount, StringFormat=已标注: {0} 张}" 
                                 Style="{StaticResource CaptionText}"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- 中间标注区域 -->
            <materialDesign:Card Grid.Column="1" Margin="4,8" materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标注工具栏 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="16,8">
                        <ToggleButton Style="{StaticResource MaterialDesignActionToggleButton}"
                                    IsChecked="{Binding IsSelectMode}"
                                    ToolTip="选择模式">
                            <materialDesign:PackIcon Kind="CursorDefault"/>
                        </ToggleButton>

                        <ToggleButton Style="{StaticResource MaterialDesignActionToggleButton}"
                                    IsChecked="{Binding IsDrawMode}"
                                    ToolTip="绘制模式"
                                    Margin="4,0,0,0">
                            <materialDesign:PackIcon Kind="Draw"/>
                        </ToggleButton>

                        <ToggleButton Style="{StaticResource MaterialDesignActionToggleButton}"
                                    IsChecked="{Binding IsRectangleMode}"
                                    ToolTip="矩形标注"
                                    Margin="4,0,0,0">
                            <materialDesign:PackIcon Kind="RectangleOutline"/>
                        </ToggleButton>

                        <ToggleButton Style="{StaticResource MaterialDesignActionToggleButton}"
                                    IsChecked="{Binding IsPolygonMode}"
                                    ToolTip="多边形标注"
                                    Margin="4,0,0,0">
                            <materialDesign:PackIcon Kind="VectorPolygon"/>
                        </ToggleButton>

                        <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="8,0"/>

                        <ComboBox ItemsSource="{Binding AnnotationClasses}"
                                SelectedItem="{Binding SelectedClass}"
                                DisplayMemberPath="Name"
                                materialDesign:HintAssist.Hint="标注类别"
                                Width="120"
                                Margin="8,0"/>

                        <Slider Value="{Binding BrushSize}"
                              Minimum="1" Maximum="20"
                              Width="100"
                              ToolTip="画笔大小"
                              Margin="16,0,8,0"/>

                        <Button Style="{StaticResource MaterialDesignToolButton}"
                              Command="{Binding UndoCommand}"
                              ToolTip="撤销">
                            <materialDesign:PackIcon Kind="Undo"/>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignToolButton}"
                              Command="{Binding RedoCommand}"
                              ToolTip="重做">
                            <materialDesign:PackIcon Kind="Redo"/>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignToolButton}"
                              Command="{Binding ClearAnnotationsCommand}"
                              ToolTip="清除标注">
                            <materialDesign:PackIcon Kind="Delete"/>
                        </Button>
                    </StackPanel>

                    <!-- 图像标注画布 -->
                    <ScrollViewer Grid.Row="1"
                                HorizontalScrollBarVisibility="Auto"
                                VerticalScrollBarVisibility="Auto">
                        <Grid>
                            <!-- 背景图像 -->
                            <Image Source="{Binding CurrentImage}" 
                                 Stretch="Uniform"
                                 RenderOptions.BitmapScalingMode="HighQuality"/>

                            <!-- 标注层 -->
                            <Canvas x:Name="AnnotationCanvas"
                                  Background="Transparent"
                                  MouseLeftButtonDown="AnnotationCanvas_MouseLeftButtonDown"
                                  MouseMove="AnnotationCanvas_MouseMove"
                                  MouseLeftButtonUp="AnnotationCanvas_MouseLeftButtonUp">
                                <!-- 标注元素将在这里动态添加 -->
                            </Canvas>

                            <!-- AI预测结果覆盖层 -->
                            <Canvas x:Name="PredictionCanvas"
                                  IsHitTestVisible="False"
                                  Opacity="0.7"
                                  Visibility="{Binding ShowPredictions, Converter={StaticResource BooleanToVisibilityConverter}}">
                                <!-- AI预测结果将在这里显示 -->
                            </Canvas>
                        </Grid>
                    </ScrollViewer>

                    <!-- 加载指示器 -->
                    <Grid Grid.Row="1" 
                        Background="#80FFFFFF" 
                        Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="Loading" 
                                                   Width="48" Height="48"
                                                   HorizontalAlignment="Center"
                                                   Foreground="{StaticResource PrimaryBrush}"/>
                            <TextBlock Text="{Binding ProcessingMessage}" 
                                     HorizontalAlignment="Center"
                                     Margin="0,16,0,0"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- 右侧属性面板 -->
            <materialDesign:Card Grid.Column="2" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <!-- 标注类别管理 -->
                        <TextBlock Text="标注类别" Style="{StaticResource SubtitleText}"/>
                        
                        <ListBox ItemsSource="{Binding AnnotationClasses}"
                               SelectedItem="{Binding SelectedClass}"
                               Style="{StaticResource CustomListBox}"
                               Height="120"
                               Margin="0,8">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="16"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Rectangle Grid.Column="0" 
                                                 Fill="{Binding Color}" 
                                                 Width="12" Height="12"/>
                                        <TextBlock Grid.Column="1" 
                                                 Text="{Binding Name}" 
                                                 Margin="8,0,0,0"
                                                 VerticalAlignment="Center"/>
                                        <TextBlock Grid.Column="2" 
                                                 Text="{Binding Count}" 
                                                 Style="{StaticResource CaptionText}"
                                                 VerticalAlignment="Center"/>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>

                        <StackPanel Orientation="Horizontal" Margin="0,8">
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding AddClassCommand}"
                                  Margin="0,0,4,0">
                                <materialDesign:PackIcon Kind="Plus"/>
                            </Button>
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding EditClassCommand}"
                                  Margin="2,0">
                                <materialDesign:PackIcon Kind="Edit"/>
                            </Button>
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding DeleteClassCommand}"
                                  Margin="4,0,0,0">
                                <materialDesign:PackIcon Kind="Delete"/>
                            </Button>
                        </StackPanel>

                        <!-- 当前标注信息 -->
                        <TextBlock Text="当前标注" Style="{StaticResource SubtitleText}" Margin="0,16,0,8"/>
                        
                        <ListBox ItemsSource="{Binding CurrentAnnotations}"
                               SelectedItem="{Binding SelectedAnnotation}"
                               Style="{StaticResource CustomListBox}"
                               Height="150"
                               Margin="0,8">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding ClassName}" FontWeight="Medium"/>
                                            <TextBlock Text="{Binding Confidence, StringFormat=置信度: {0:P1}}" 
                                                     Style="{StaticResource CaptionText}"/>
                                        </StackPanel>
                                        
                                        <Button Grid.Column="1" 
                                              Style="{StaticResource MaterialDesignToolButton}"
                                              Command="{Binding DataContext.DeleteAnnotationCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              CommandParameter="{Binding}">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>

                        <!-- AI辅助设置 -->
                        <TextBlock Text="AI辅助" Style="{StaticResource SubtitleText}" Margin="0,16,0,8"/>
                        
                        <CheckBox Content="显示AI预测"
                                IsChecked="{Binding ShowPredictions}"
                                Style="{StaticResource CustomCheckBox}"/>
                        
                        <CheckBox Content="自动建议标注"
                                IsChecked="{Binding AutoSuggest}"
                                Style="{StaticResource CustomCheckBox}"/>
                        
                        <StackPanel Margin="0,8">
                            <TextBlock Text="置信度阈值" FontWeight="Medium"/>
                            <Slider Value="{Binding ConfidenceThreshold}"
                                  Minimum="0" Maximum="1"
                                  TickFrequency="0.1"
                                  IsSnapToTickEnabled="True"
                                  Style="{StaticResource CustomSlider}"/>
                            <TextBlock Text="{Binding ConfidenceThreshold, StringFormat={}{0:P0}}" 
                                     Style="{StaticResource CaptionText}"
                                     HorizontalAlignment="Center"/>
                        </StackPanel>

                        <!-- 质量评估 -->
                        <TextBlock Text="质量评估" Style="{StaticResource SubtitleText}" Margin="0,16,0,8"/>
                        
                        <Grid Margin="0,8">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="完整性:" FontWeight="Medium"/>
                            <ProgressBar Grid.Row="0" Grid.Column="1" 
                                       Value="{Binding CompletenessScore}" 
                                       Maximum="100"
                                       Style="{StaticResource CustomProgressBar}"
                                       Margin="8,0,0,4"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="准确性:" FontWeight="Medium"/>
                            <ProgressBar Grid.Row="1" Grid.Column="1" 
                                       Value="{Binding AccuracyScore}" 
                                       Maximum="100"
                                       Style="{StaticResource CustomProgressBar}"
                                       Margin="8,0,0,4"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="一致性:" FontWeight="Medium"/>
                            <ProgressBar Grid.Row="2" Grid.Column="1" 
                                       Value="{Binding ConsistencyScore}" 
                                       Maximum="100"
                                       Style="{StaticResource CustomProgressBar}"
                                       Margin="8,0,0,4"/>
                        </Grid>

                        <!-- 操作按钮 -->
                        <StackPanel Margin="0,16,0,0">
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding OptimizeAnnotationsCommand}"
                                  HorizontalAlignment="Stretch"
                                  Margin="0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AutoFix" Width="16" Height="16"/>
                                    <TextBlock Text="优化标注" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding ExportAnnotationsCommand}"
                                  HorizontalAlignment="Stretch"
                                  Margin="0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Export" Width="16" Height="16"/>
                                    <TextBlock Text="导出标注" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding GenerateReportCommand}"
                                  HorizontalAlignment="Stretch"
                                  Margin="0,4">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16"/>
                                    <TextBlock Text="生成报告" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>

        <!-- 状态栏 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="16,4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                         Text="{Binding StatusMessage}" 
                         VerticalAlignment="Center"
                         Foreground="White"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding CurrentImageIndex, StringFormat=图像: {0}}" 
                             VerticalAlignment="Center"
                             Foreground="White"
                             Margin="0,0,16,0"/>
                    <TextBlock Text="{Binding AnnotationCount, StringFormat=标注: {0}}" 
                             VerticalAlignment="Center"
                             Foreground="White"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</UserControl>
