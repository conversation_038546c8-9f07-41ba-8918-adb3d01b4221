using Microsoft.AspNetCore.Mvc;
using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Api.Models;

namespace MedicalImageAnalysis.Api.Controllers;

/// <summary>
/// 标注管理控制器，提供智能标注和标注管理 API
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AnnotationController : ControllerBase
{
    private readonly IAnnotationService _annotationService;
    private readonly IDicomService _dicomService;
    private readonly ILogger<AnnotationController> _logger;

    public AnnotationController(
        IAnnotationService annotationService,
        IDicomService dicomService,
        ILogger<AnnotationController> logger)
    {
        _annotationService = annotationService;
        _dicomService = dicomService;
        _logger = logger;
    }

    /// <summary>
    /// 自动生成标注
    /// </summary>
    /// <param name="dicomFile">DICOM 文件</param>
    /// <param name="config">标注配置</param>
    /// <returns>生成的标注</returns>
    [HttpPost("generate")]
    [ProducesResponseType(typeof(List<Annotation>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<List<Annotation>>> GenerateAnnotations(
        [FromForm] IFormFile dicomFile,
        [FromForm] AutoAnnotationConfig? config = null)
    {
        try
        {
            if (dicomFile == null || dicomFile.Length == 0)
            {
                return BadRequest("DICOM 文件不能为空");
            }

            // 保存临时文件
            var tempFilePath = Path.GetTempFileName();
            using (var stream = new FileStream(tempFilePath, FileMode.Create))
            {
                await dicomFile.CopyToAsync(stream);
            }

            try
            {
                // 解析 DICOM 文件
                var instance = await _dicomService.ParseDicomFileAsync(tempFilePath, HttpContext.RequestAborted);

                // 使用默认配置如果没有提供
                config ??= new AutoAnnotationConfig
                {
                    ModelPath = "models/default.pt",
                    ConfidenceThreshold = 0.5,
                    IouThreshold = 0.45,
                    EnablePostProcessing = true,
                    AutoAdjustWindowLevel = true
                };

                // 生成标注
                var annotations = await _annotationService.GenerateAnnotationsAsync(instance, config, HttpContext.RequestAborted);
                return Ok(annotations);
            }
            finally
            {
                // 清理临时文件
                if (System.IO.File.Exists(tempFilePath))
                {
                    System.IO.File.Delete(tempFilePath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成标注失败");
            return StatusCode(500, new { error = "生成失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 验证标注质量
    /// </summary>
    /// <param name="request">验证请求</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(AnnotationValidationResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<AnnotationValidationResult>> ValidateAnnotations(
        [FromBody] ValidateAnnotationsRequest request)
    {
        try
        {
            if (request?.Annotations == null || !request.Annotations.Any())
            {
                return BadRequest("标注数据不能为空");
            }

            var validationRules = request.ValidationRules ?? new AnnotationValidationRules
            {
                MinConfidence = 0.1,
                MaxConfidence = 1.0,
                MinBoundingBoxArea = 0.0001,
                MaxBoundingBoxArea = 0.9,
                CheckBoundingBoxBounds = true,
                CheckDuplicateAnnotations = true,
                DuplicateIouThreshold = 0.8
            };

            var result = await _annotationService.ValidateAnnotationsAsync(request.Annotations, validationRules);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证标注失败");
            return StatusCode(500, new { error = "验证失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 转换标注格式
    /// </summary>
    /// <param name="request">转换请求</param>
    /// <returns>转换后的标注数据</returns>
    [HttpPost("convert")]
    [ProducesResponseType(typeof(string), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<string>> ConvertAnnotationFormat(
        [FromBody] ConvertAnnotationFormatRequest request)
    {
        try
        {
            if (request?.Annotations == null || !request.Annotations.Any())
            {
                return BadRequest("标注数据不能为空");
            }

            if (request.ImageWidth <= 0 || request.ImageHeight <= 0)
            {
                return BadRequest("图像尺寸必须大于 0");
            }

            var convertedData = await _annotationService.ConvertAnnotationFormatAsync(
                request.Annotations,
                request.TargetFormat,
                (request.ImageWidth, request.ImageHeight),
                request.ClassMapping);

            return Ok(convertedData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换标注格式失败");
            return StatusCode(500, new { error = "转换失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 生成标注统计信息
    /// </summary>
    /// <param name="annotations">标注数据</param>
    /// <returns>统计信息</returns>
    [HttpPost("statistics")]
    [ProducesResponseType(typeof(AnnotationStatistics), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<AnnotationStatistics>> GenerateStatistics([FromBody] List<Annotation> annotations)
    {
        try
        {
            if (annotations == null)
            {
                return BadRequest("标注数据不能为空");
            }

            var statistics = await _annotationService.GenerateAnnotationStatisticsAsync(annotations);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成统计信息失败");
            return StatusCode(500, new { error = "生成失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 检测标注异常
    /// </summary>
    /// <param name="request">检测请求</param>
    /// <returns>异常检测结果</returns>
    [HttpPost("detect-anomalies")]
    [ProducesResponseType(typeof(List<AnnotationAnomaly>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<List<AnnotationAnomaly>>> DetectAnomalies(
        [FromBody] DetectAnomaliesRequest request)
    {
        try
        {
            if (request?.Annotations == null || !request.Annotations.Any())
            {
                return BadRequest("标注数据不能为空");
            }

            var detectionConfig = request.DetectionConfig ?? new AnomalyDetectionConfig
            {
                ConfidenceThreshold = 0.1,
                SizeAnomalySensitivity = 2.0,
                OverlapThreshold = 0.7
            };

            var anomalies = await _annotationService.DetectAnnotationAnomaliesAsync(request.Annotations, detectionConfig);
            return Ok(anomalies);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检测标注异常失败");
            return StatusCode(500, new { error = "检测失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 合并重叠标注
    /// </summary>
    /// <param name="request">合并请求</param>
    /// <returns>合并后的标注</returns>
    [HttpPost("merge-overlapping")]
    [ProducesResponseType(typeof(List<Annotation>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<List<Annotation>>> MergeOverlappingAnnotations(
        [FromBody] MergeOverlappingAnnotationsRequest request)
    {
        try
        {
            if (request?.Annotations == null || !request.Annotations.Any())
            {
                return BadRequest("标注数据不能为空");
            }

            var mergeConfig = request.MergeConfig ?? new AnnotationMergeConfig
            {
                OverlapThreshold = 0.5,
                Strategy = MergeStrategy.HighestConfidence,
                KeepOriginalAnnotations = false
            };

            var mergedAnnotations = await _annotationService.MergeOverlappingAnnotationsAsync(request.Annotations, mergeConfig);
            return Ok(mergedAnnotations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "合并重叠标注失败");
            return StatusCode(500, new { error = "合并失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取支持的标注格式
    /// </summary>
    /// <returns>支持的格式列表</returns>
    [HttpGet("supported-formats")]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetSupportedFormats()
    {
        var formats = new
        {
            formats = new[]
            {
                new { name = "YOLO", value = "YOLO", description = "YOLO 格式 (归一化坐标)" },
                new { name = "COCO", value = "COCO", description = "COCO JSON 格式" },
                new { name = "Pascal VOC", value = "PascalVOC", description = "Pascal VOC XML 格式" },
                new { name = "CVAT", value = "CVAT", description = "CVAT XML 格式" },
                new { name = "LabelMe", value = "LabelMe", description = "LabelMe JSON 格式" }
            }
        };

        return Ok(formats);
    }

    /// <summary>
    /// 获取默认标注配置
    /// </summary>
    /// <returns>默认配置</returns>
    [HttpGet("default-config")]
    [ProducesResponseType(typeof(AutoAnnotationConfig), 200)]
    public ActionResult<AutoAnnotationConfig> GetDefaultConfig()
    {
        var config = new AutoAnnotationConfig
        {
            ModelPath = "models/default.pt",
            ConfidenceThreshold = 0.5,
            IouThreshold = 0.45,
            TargetClasses = new List<string>(),
            MinBoundingBoxSize = (10, 10),
            MaxBoundingBoxSize = (1000, 1000),
            EnablePostProcessing = true,
            AutoAdjustWindowLevel = true,
            PreprocessingOptions = new ImagePreprocessingOptions
            {
                Normalize = true,
                NormalizationRange = (0.0, 1.0),
                Resize = false,
                TargetSize = (640, 640),
                HistogramEqualization = false,
                ApplyClahe = false
            }
        };

        return Ok(config);
    }

    /// <summary>
    /// 获取默认验证规则
    /// </summary>
    /// <returns>默认验证规则</returns>
    [HttpGet("default-validation-rules")]
    [ProducesResponseType(typeof(AnnotationValidationRules), 200)]
    public ActionResult<AnnotationValidationRules> GetDefaultValidationRules()
    {
        var rules = new AnnotationValidationRules
        {
            MinConfidence = 0.1,
            MaxConfidence = 1.0,
            MinBoundingBoxArea = 0.0001,
            MaxBoundingBoxArea = 0.9,
            AllowedLabels = new HashSet<string>(),
            CheckBoundingBoxBounds = true,
            CheckDuplicateAnnotations = true,
            DuplicateIouThreshold = 0.8
        };

        return Ok(rules);
    }
}
