<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />

    <PackageReference Include="Blazorise.Bootstrap5" Version="1.4.2" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.4.2" />
    <PackageReference Include="Microsoft.AspNetCore.Server.IISIntegration" Version="2.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MedicalImageAnalysis.Application\MedicalImageAnalysis.Application.csproj" />
    <ProjectReference Include="..\MedicalImageAnalysis.Infrastructure\MedicalImageAnalysis.Infrastructure.csproj" />
  </ItemGroup>

</Project>
