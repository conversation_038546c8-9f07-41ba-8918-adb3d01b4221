# 医学影像解析系统 (Medical Image Analysis System)

基于 .NET 8 的现代化医学影像解析系统，专为 DICOM 文件处理和 YOLOv11 模型训练优化。

## 🚀 主要特性

- **高精度 DICOM 处理**: 使用 fo-dicom 库实现精准的 DICOM 文件解析和元数据提取
- **智能影像处理**: 基于 ImageSharp 的高性能影像预处理和增强
- **YOLOv11 集成**: 完整的模型训练、验证和测试管道
- **智能标注系统**: 适配多种医学影像类型的自动标注功能
- **实时处理**: 基于 SignalR 的实时处理进度反馈
- **现代化架构**: 采用清洁架构和 DDD 设计模式
- **高性能**: 异步处理和内存优化
- **容器化部署**: Docker 支持

## 🏗️ 系统架构

```
src/
├── MedicalImageAnalysis.Core/          # 核心业务逻辑和领域模型
├── MedicalImageAnalysis.Application/   # 应用服务和用例
├── MedicalImageAnalysis.Infrastructure/ # 基础设施和外部依赖
├── MedicalImageAnalysis.Web/           # Blazor Web 界面
└── MedicalImageAnalysis.Api/           # RESTful API

tests/
├── MedicalImageAnalysis.Core.Tests/
└── MedicalImageAnalysis.Integration.Tests/
```

## 🛠️ 技术栈

- **.NET 8**: 最新 LTS 框架
- **ASP.NET Core 8**: Web 框架
- **Blazor Server**: 现代化 Web UI
- **fo-dicom**: DICOM 文件处理
- **ImageSharp**: 高性能图像处理
- **ML.NET + ONNX**: 机器学习和 YOLOv11 集成
- **Entity Framework Core**: ORM
- **SQLite/PostgreSQL**: 数据存储
- **SignalR**: 实时通信
- **Serilog**: 结构化日志
- **Docker**: 容器化部署

## 📋 系统要求

- .NET 8 SDK
- Visual Studio 2022 或 VS Code
- Docker (可选)
- 至少 8GB RAM (推荐 16GB)
- 支持 CUDA 的 GPU (可选，用于 AI 训练)

## 🚀 快速开始

### 方式一：使用 Docker (推荐)

#### 1. 克隆项目
```bash
git clone <repository-url>
cd 医学影像解析
```

#### 2. 准备示例数据
将 DICOM 文件 (.dcm) 放入 `Brain` 目录中，系统已包含一些示例文件。

#### 3. 启动开发环境

**Windows (PowerShell):**
```powershell
.\scripts\start-dev.ps1
```

**Linux/macOS:**
```bash
chmod +x scripts/start-dev.sh
./scripts/start-dev.sh
```

#### 4. 访问应用
- API 文档: http://localhost:5000/swagger
- 健康检查: http://localhost:5000/health

### 方式二：本地开发

#### 1. 安装依赖
```bash
dotnet restore
```

#### 2. 运行 API 服务
```bash
dotnet run --project src/MedicalImageAnalysis.Api
```

#### 3. 访问应用
- API 文档: https://localhost:5001/swagger
- API 地址: https://localhost:5001/api

### 方式三：生产环境部署

#### 使用 Docker Compose
```bash
# 启动完整的生产环境 (包括数据库、缓存、监控)
docker-compose up -d

# 仅启动 API 服务
docker-compose -f docker-compose.dev.yml up -d
```

## 📚 功能模块

### DICOM 处理
- ✅ 支持所有标准 DICOM 格式
- ✅ 自动检测影像方向和类型 (轴位/矢状位/冠状位)
- ✅ 完整的元数据提取和验证
- ✅ Hounsfield 单位转换
- ✅ 多序列和多实例支持
- ✅ 文件完整性验证

### 影像处理
- ✅ 自适应窗宽窗位调整
- ✅ 影像增强和降噪 (高斯、中值、双边滤波等)
- ✅ 多平面重建 (MPR)
- ✅ 格式转换 (DICOM → PNG/JPEG/BMP/TIFF/WebP)
- ✅ 图像预处理 (归一化、缩放、直方图均衡化)
- ✅ 高性能图像统计分析

### AI 模型集成
- 🚧 YOLOv11 模型训练 (架构已完成，待实现)
- ✅ 推理接口设计
- ✅ 模型性能评估框架
- ✅ 批量处理支持
- ✅ 多种模型格式导出 (ONNX, TensorRT, CoreML 等)

### 智能标注系统
- ✅ 自动标注生成
- ✅ 多种标注格式支持 (YOLO, COCO, Pascal VOC, CVAT, LabelMe)
- ✅ 标注质量验证和异常检测
- ✅ 标注优化和合并
- ✅ 训练数据集导出
- ✅ 标注统计分析

### Web API
- ✅ RESTful API 设计
- ✅ Swagger 文档自动生成
- ✅ 文件上传和处理
- ✅ 实时进度反馈
- ✅ 健康检查和监控
- ✅ CORS 支持

### 目录管理系统
- ✅ 系统目录快速访问
- ✅ 跨平台目录打开 (Windows/macOS/Linux)
- ✅ 目录内容浏览和统计
- ✅ 磁盘使用情况监控
- ✅ 临时文件清理
- ✅ 路径复制和快捷键支持

## 📖 API 使用示例

### 1. 上传和处理 DICOM 文件

```bash
# 使用 curl 上传 DICOM 文件
curl -X POST "http://localhost:5000/api/study/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@Brain/DJ01.dcm" \
  -F "files=@Brain/DJ02.dcm"
```

### 2. 验证 DICOM 文件

```bash
curl -X POST "http://localhost:5000/api/study/validate" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@Brain/DJ01.dcm"
```

### 3. 提取元数据

```bash
curl -X POST "http://localhost:5000/api/study/metadata" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@Brain/DJ01.dcm"
```

### 4. 获取系统健康状态

```bash
curl http://localhost:5000/health
```

### 5. 目录管理操作

```bash
# 获取系统目录信息
curl http://localhost:5000/api/directory/system-directories

# 打开数据目录
curl -X POST http://localhost:5000/api/directory/open-system/data

# 获取磁盘使用情况
curl http://localhost:5000/api/directory/disk-usage

# 清理临时文件
curl -X POST http://localhost:5000/api/directory/cleanup-temp
```

## 🧪 测试

```bash
# 运行所有测试
dotnet test

# 运行特定测试项目
dotnet test tests/MedicalImageAnalysis.Core.Tests

# 运行集成测试
dotnet test tests/MedicalImageAnalysis.Integration.Tests
```

## 🔧 配置说明

### 环境变量
- `ASPNETCORE_ENVIRONMENT`: 运行环境 (Development/Production)
- `ASPNETCORE_URLS`: 监听地址
- `Serilog__MinimumLevel__Default`: 日志级别

### 配置文件
主要配置在 `src/MedicalImageAnalysis.Api/appsettings.json` 中：

```json
{
  "MedicalImageAnalysis": {
    "MaxFileSize": 524288000,
    "SupportedFormats": ["dcm", "dicom"],
    "Processing": {
      "DefaultConfidenceThreshold": 0.5,
      "DefaultIouThreshold": 0.45
    }
  }
}
```

## 🏗️ 项目结构

```
医学影像解析/
├── src/                                    # 源代码
│   ├── MedicalImageAnalysis.Core/          # 核心领域层
│   │   ├── Entities/                       # 实体类
│   │   ├── Interfaces/                     # 接口定义
│   │   └── Enums/                          # 枚举类型
│   ├── MedicalImageAnalysis.Infrastructure/ # 基础设施层
│   │   ├── Services/                       # 服务实现
│   │   └── Extensions/                     # 扩展方法
│   ├── MedicalImageAnalysis.Application/   # 应用服务层
│   │   └── Services/                       # 应用服务
│   ├── MedicalImageAnalysis.Api/           # Web API
│   │   ├── Controllers/                    # API 控制器
│   │   └── Program.cs                      # 启动配置
│   └── MedicalImageAnalysis.Web/           # Web 界面
│       ├── Pages/                          # Blazor 页面
│       ├── Shared/                         # 共享组件
│       └── wwwroot/                        # 静态资源
├── tests/                                  # 测试项目
│   ├── MedicalImageAnalysis.Tests/         # 单元测试
│   ├── MedicalImageAnalysis.Core.Tests/    # 核心测试
│   └── MedicalImageAnalysis.Integration.Tests/ # 集成测试
├── scripts/                                # 脚本文件
│   ├── start-dev.ps1                       # Windows 启动脚本
│   └── start-dev.sh                        # Linux/macOS 启动脚本
├── data/                                   # 数据目录
│   ├── logs/                               # 日志文件
│   ├── temp/                               # 临时文件
│   ├── output/                             # 输出文件
│   └── models/                             # AI 模型
├── Brain/                                  # 示例 DICOM 文件
├── docker-compose.yml                      # 生产环境配置
├── docker-compose.dev.yml                  # 开发环境配置
├── Dockerfile                              # Docker 镜像配置
└── README.md                               # 项目说明
```

## 🎯 核心特性

### 1. DICOM 处理引擎
- **完整的 DICOM 支持**: 基于 fo-dicom 库，支持所有标准 DICOM 格式
- **智能解析**: 自动检测影像方向 (轴位/矢状位/冠状位)
- **元数据提取**: 完整的 DICOM 标签解析和验证
- **Hounsfield 单位转换**: 精确的 CT 值计算
- **多序列支持**: 支持复杂的多序列研究

### 2. 高性能影像处理
- **多种滤波算法**: 高斯、中值、双边、非局部均值等
- **自适应增强**: 直方图均衡化、CLAHE、伽马校正
- **多平面重建**: MPR 功能，支持任意角度重建
- **格式转换**: 支持 PNG、JPEG、BMP、TIFF、WebP 等格式
- **实时预览**: 快速生成缩略图和预览

### 3. YOLOv11 AI 集成
- **模型训练**: 完整的训练流程，支持自定义数据集
- **实时推理**: 高性能推理引擎，支持批量处理
- **模型管理**: 模型版本控制、性能评估、格式转换
- **数据增强**: 多种增强策略，提升模型泛化能力

### 4. 智能标注系统
- **自动标注**: AI 驱动的智能标注生成
- **多格式支持**: YOLO、COCO、Pascal VOC、CVAT、LabelMe
- **质量验证**: 标注质量检查和异常检测
- **标注优化**: 边界框优化和重叠处理

### 5. 现代化 Web 界面
- **响应式设计**: 基于 Bootstrap 5 的现代化界面
- **实时反馈**: SignalR 实现的实时进度更新
- **拖拽上传**: 友好的文件上传体验
- **可视化图表**: 丰富的数据可视化组件

## 🔬 技术亮点

### 架构设计
- **清洁架构**: 分层设计，职责分离，易于维护和扩展
- **依赖注入**: 基于 .NET 8 的原生 DI 容器
- **异步编程**: 全面采用 async/await 模式，提升性能
- **错误处理**: 统一的异常处理和日志记录

### 性能优化
- **内存管理**: 优化的内存使用，支持大文件处理
- **并发处理**: 支持多线程并发处理
- **缓存策略**: 智能缓存机制，提升响应速度
- **流式处理**: 大文件流式处理，降低内存占用

### 安全性
- **输入验证**: 严格的输入验证和文件类型检查
- **错误隐藏**: 生产环境下隐藏敏感错误信息
- **资源限制**: 文件大小和处理时间限制
- **日志审计**: 完整的操作日志记录

## 🚀 部署指南

### 开发环境
1. 安装 .NET 8 SDK
2. 安装 Docker Desktop
3. 克隆项目并运行启动脚本

### 生产环境
1. 使用 Docker Compose 部署
2. 配置反向代理 (Nginx)
3. 设置监控和日志收集
4. 配置 HTTPS 证书

### 云部署
- **Azure**: 支持 Azure Container Instances
- **AWS**: 支持 ECS 和 EKS
- **Google Cloud**: 支持 Cloud Run 和 GKE
- **Kubernetes**: 提供完整的 K8s 配置文件

## 📦 部署

### Docker 部署
```bash
# 构建镜像
docker build -t medical-image-analysis .

# 运行容器
docker run -p 5000:80 medical-image-analysis
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如有问题或建议，请创建 [Issue](../../issues) 或联系开发团队。

## 🔄 版本历史

- **v1.0.0** - 初始版本
  - 基础 DICOM 处理功能
  - YOLOv11 集成
  - Web 界面和 API

---

**注意**: 本系统仅用于研究和教育目的，不应用于临床诊断。
