using MedicalImageAnalysis.Core.Entities;

namespace MedicalImageAnalysis.Core.Interfaces;

/// <summary>
/// 实时通知服务接口
/// </summary>
public interface IRealTimeNotificationService
{
    /// <summary>
    /// 发送训练进度更新
    /// </summary>
    Task SendTrainingProgressAsync(string trainingId, TrainingProgressInfo progress);

    /// <summary>
    /// 发送训练完成通知
    /// </summary>
    Task SendTrainingCompletedAsync(string trainingId, TrainingResult result);

    /// <summary>
    /// 发送训练失败通知
    /// </summary>
    Task SendTrainingFailedAsync(string trainingId, string error);

    /// <summary>
    /// 发送处理进度更新
    /// </summary>
    Task SendProcessingProgressAsync(string taskId, ProcessingProgressInfo progress);

    /// <summary>
    /// 发送处理完成通知
    /// </summary>
    Task SendProcessingCompletedAsync(string taskId, ProcessingResult result);

    /// <summary>
    /// 发送系统状态更新
    /// </summary>
    Task SendSystemStatusAsync(SystemStatusInfo status);

    /// <summary>
    /// 发送通用通知
    /// </summary>
    Task SendNotificationAsync(NotificationInfo notification);

    /// <summary>
    /// 发送给特定用户的通知
    /// </summary>
    Task SendNotificationToUserAsync(string userId, NotificationInfo notification);

    /// <summary>
    /// 发送给特定组的通知
    /// </summary>
    Task SendNotificationToGroupAsync(string groupName, NotificationInfo notification);

    /// <summary>
    /// 发送DICOM上传进度
    /// </summary>
    Task SendDicomUploadProgressAsync(string uploadId, DicomUploadProgressInfo progress);

    /// <summary>
    /// 发送标注进度更新
    /// </summary>
    Task SendAnnotationProgressAsync(string projectId, AnnotationProgressInfo progress);

    /// <summary>
    /// 发送模型推理结果
    /// </summary>
    Task SendInferenceResultAsync(string sessionId, InferenceResult result);
}

/// <summary>
/// 训练进度信息
/// </summary>
public class TrainingProgressInfo
{
    public string TrainingId { get; set; } = string.Empty;
    public int CurrentEpoch { get; set; }
    public int TotalEpochs { get; set; }
    public int CurrentBatch { get; set; }
    public int TotalBatches { get; set; }
    public double OverallProgress { get; set; }
    public double EpochProgress { get; set; }
    public double CurrentLoss { get; set; }
    public double LearningRate { get; set; }
    public double mAP50 { get; set; }
    public double Precision { get; set; }
    public double Recall { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 处理进度信息
/// </summary>
public class ProcessingProgressInfo
{
    public string TaskId { get; set; } = string.Empty;
    public string TaskType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public double ProgressPercentage { get; set; }
    public int ProcessedItems { get; set; }
    public int TotalItems { get; set; }
    public string CurrentItem { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 系统状态信息
/// </summary>
public class SystemStatusInfo
{
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double DiskUsage { get; set; }
    public double GpuUsage { get; set; }
    public double GpuMemoryUsage { get; set; }
    public int ActiveTasks { get; set; }
    public int QueuedTasks { get; set; }
    public string SystemHealth { get; set; } = "healthy";
    public List<string> Warnings { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 通知信息
/// </summary>
public class NotificationInfo
{
    public string Type { get; set; } = string.Empty; // success, error, warning, info
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public bool AutoDismiss { get; set; } = true;
    public int DismissAfter { get; set; } = 5000; // 毫秒
}

/// <summary>
/// DICOM上传进度信息
/// </summary>
public class DicomUploadProgressInfo
{
    public string UploadId { get; set; } = string.Empty;
    public int ProcessedFiles { get; set; }
    public int TotalFiles { get; set; }
    public double ProgressPercentage { get; set; }
    public string CurrentFile { get; set; } = string.Empty;
    public long ProcessedBytes { get; set; }
    public long TotalBytes { get; set; }
    public double UploadSpeed { get; set; } // bytes per second
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 标注进度信息
/// </summary>
public class AnnotationProgressInfo
{
    public string ProjectId { get; set; } = string.Empty;
    public string ProjectName { get; set; } = string.Empty;
    public int CompletedCount { get; set; }
    public int TotalCount { get; set; }
    public double ProgressPercentage { get; set; }
    public int ReviewedCount { get; set; }
    public int ApprovedCount { get; set; }
    public int RejectedCount { get; set; }
    public List<string> RecentAnnotators { get; set; } = new();
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 推理结果
/// </summary>
public class InferenceResult
{
    public string SessionId { get; set; } = string.Empty;
    public string ModelName { get; set; } = string.Empty;
    public string ImagePath { get; set; } = string.Empty;
    public List<Detection> Detections { get; set; } = new();
    public double InferenceTime { get; set; } // 毫秒
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 实时监控服务接口
/// </summary>
public interface ISystemMonitoringService
{
    /// <summary>
    /// 开始监控
    /// </summary>
    Task StartMonitoringAsync();

    /// <summary>
    /// 停止监控
    /// </summary>
    Task StopMonitoringAsync();

    /// <summary>
    /// 获取当前系统状态
    /// </summary>
    Task<SystemStatusInfo> GetCurrentStatusAsync();

    /// <summary>
    /// 系统状态更新事件
    /// </summary>
    event EventHandler<SystemStatusInfo>? StatusUpdated;
}

/// <summary>
/// 连接管理服务接口
/// </summary>
public interface IConnectionManagerService
{
    /// <summary>
    /// 获取在线用户数
    /// </summary>
    int GetOnlineUserCount();

    /// <summary>
    /// 获取活跃连接数
    /// </summary>
    int GetActiveConnectionCount();

    /// <summary>
    /// 获取用户连接信息
    /// </summary>
    Task<List<ConnectionInfo>> GetUserConnectionsAsync(string userId);

    /// <summary>
    /// 断开用户所有连接
    /// </summary>
    Task DisconnectUserAsync(string userId);

    /// <summary>
    /// 广播系统维护通知
    /// </summary>
    Task BroadcastMaintenanceNotificationAsync(string message, DateTime scheduledTime);
}

/// <summary>
/// 连接信息
/// </summary>
public class ConnectionInfo
{
    public string ConnectionId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public DateTime ConnectedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public List<string> Groups { get; set; } = new();
}
