<Window x:Class="MedicalImageAnalysis.WpfClient.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="设置" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{StaticResource PrimaryFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Settings" Width="24" Height="24" VerticalAlignment="Center" Foreground="White"/>
                <TextBlock Text="系统设置" 
                         FontSize="18" 
                         FontWeight="Medium"
                         VerticalAlignment="Center"
                         Foreground="White"
                         Margin="12,0,0,0"/>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="24">
                <!-- 连接设置 -->
                <materialDesign:Card Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <TextBlock Text="连接设置" Style="{StaticResource SubtitleText}"/>

                        <TextBox materialDesign:HintAssist.Hint="API服务地址"
                               Text="{Binding ApiBaseUrl}"
                               Style="{StaticResource CustomTextBox}"/>

                        <TextBox materialDesign:HintAssist.Hint="SignalR地址"
                               Text="{Binding SignalRUrl}"
                               Style="{StaticResource CustomTextBox}"/>

                        <TextBox materialDesign:HintAssist.Hint="连接超时(秒)"
                               Text="{Binding ConnectionTimeout}"
                               Style="{StaticResource CustomTextBox}"/>

                        <CheckBox Content="启动时自动连接"
                                IsChecked="{Binding AutoConnect}"
                                Style="{StaticResource CustomCheckBox}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 界面设置 -->
                <materialDesign:Card Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <TextBlock Text="界面设置" Style="{StaticResource SubtitleText}"/>

                        <ComboBox materialDesign:HintAssist.Hint="主题"
                                ItemsSource="{Binding Themes}"
                                SelectedItem="{Binding SelectedTheme}"
                                Style="{StaticResource CustomComboBox}"/>

                        <ComboBox materialDesign:HintAssist.Hint="语言"
                                ItemsSource="{Binding Languages}"
                                SelectedItem="{Binding SelectedLanguage}"
                                Style="{StaticResource CustomComboBox}"/>

                        <CheckBox Content="启用通知"
                                IsChecked="{Binding EnableNotifications}"
                                Style="{StaticResource CustomCheckBox}"/>

                        <CheckBox Content="保存窗口状态"
                                IsChecked="{Binding SaveWindowState}"
                                Style="{StaticResource CustomCheckBox}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 文件设置 -->
                <materialDesign:Card Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <TextBlock Text="文件设置" Style="{StaticResource SubtitleText}"/>

                        <Grid Margin="0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" 
                                   materialDesign:HintAssist.Hint="默认输出目录"
                                   Text="{Binding DefaultOutputDirectory}"
                                   Style="{StaticResource CustomTextBox}"/>
                            <Button Grid.Column="1" 
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Command="{Binding BrowseOutputDirectoryCommand}"
                                  Margin="8,8,0,8">
                                <materialDesign:PackIcon Kind="FolderOpen"/>
                            </Button>
                        </Grid>

                        <TextBox materialDesign:HintAssist.Hint="最大并发任务数"
                               Text="{Binding MaxConcurrentTasks}"
                               Style="{StaticResource CustomTextBox}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 高级设置 -->
                <materialDesign:Card Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <TextBlock Text="高级设置" Style="{StaticResource SubtitleText}"/>

                        <CheckBox Content="启用调试模式"
                                IsChecked="{Binding EnableDebugMode}"
                                Style="{StaticResource CustomCheckBox}"/>

                        <CheckBox Content="启用性能监控"
                                IsChecked="{Binding EnablePerformanceMonitoring}"
                                Style="{StaticResource CustomCheckBox}"/>

                        <CheckBox Content="自动清理临时文件"
                                IsChecked="{Binding AutoCleanupTempFiles}"
                                Style="{StaticResource CustomCheckBox}"/>

                        <StackPanel Margin="0,8">
                            <TextBlock Text="日志级别" FontWeight="Medium"/>
                            <ComboBox ItemsSource="{Binding LogLevels}"
                                    SelectedItem="{Binding SelectedLogLevel}"
                                    Style="{StaticResource CustomComboBox}"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮栏 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="16,8">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding ResetToDefaultCommand}"
                        Margin="0,0,8,0"
                        Foreground="White"
                        BorderBrush="White">
                    <TextBlock Text="恢复默认"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding CancelCommand}"
                        Margin="0,0,8,0"
                        Foreground="White"
                        BorderBrush="White">
                    <TextBlock Text="取消"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding SaveCommand}"
                        Background="White"
                        Foreground="{StaticResource PrimaryBrush}">
                    <TextBlock Text="保存"/>
                </Button>
            </StackPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>
