@inherits LayoutComponentBase

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="text-primary mb-0">医学影像解析系统</h4>
                <div class="d-flex align-items-center">
                    <span class="badge bg-success me-2">在线</span>
                    <small class="text-muted">@DateTime.Now.ToString("yyyy-MM-dd HH:mm")</small>
                </div>
            </div>
        </div>

        <article class="content px-4">
            <ErrorBoundary>
                <ChildContent>
                    @Body
                </ChildContent>
                <ErrorContent Context="exception">
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">系统错误</h4>
                        <p>抱歉，系统遇到了一个错误。请刷新页面重试。</p>
                        <hr>
                        <p class="mb-0">
                            <small class="text-muted">错误详情：@exception.Message</small>
                        </p>
                    </div>
                </ErrorContent>
            </ErrorBoundary>
        </article>
    </main>
</div>
