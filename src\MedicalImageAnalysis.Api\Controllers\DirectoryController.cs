using Microsoft.AspNetCore.Mvc;
using MedicalImageAnalysis.Core.Interfaces;

namespace MedicalImageAnalysis.Api.Controllers;

/// <summary>
/// 目录管理控制器，提供文件夹操作和系统目录访问 API
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class DirectoryController : ControllerBase
{
    private readonly IDirectoryService _directoryService;
    private readonly ILogger<DirectoryController> _logger;

    public DirectoryController(IDirectoryService directoryService, ILogger<DirectoryController> logger)
    {
        _directoryService = directoryService;
        _logger = logger;
    }

    /// <summary>
    /// 获取系统目录信息
    /// </summary>
    /// <returns>系统目录信息</returns>
    [HttpGet("system-directories")]
    [ProducesResponseType(typeof(SystemDirectories), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<SystemDirectories>> GetSystemDirectories()
    {
        try
        {
            var directories = await _directoryService.GetSystemDirectoriesAsync();
            return Ok(directories);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统目录信息失败");
            return StatusCode(500, new { error = "获取失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 打开指定目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>操作结果</returns>
    [HttpPost("open")]
    [ProducesResponseType(typeof(bool), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<bool>> OpenDirectory([FromForm] string directoryPath)
    {
        try
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                return BadRequest("目录路径不能为空");
            }

            var result = await _directoryService.OpenDirectoryAsync(directoryPath);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开目录失败: {DirectoryPath}", directoryPath);
            return StatusCode(500, new { error = "打开失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 打开系统预定义目录
    /// </summary>
    /// <param name="directoryType">目录类型</param>
    /// <returns>操作结果</returns>
    [HttpPost("open-system/{directoryType}")]
    [ProducesResponseType(typeof(bool), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<bool>> OpenSystemDirectory(string directoryType)
    {
        try
        {
            var directories = await _directoryService.GetSystemDirectoriesAsync();
            
            var directoryPath = directoryType.ToLowerInvariant() switch
            {
                "data" => directories.DataDirectory,
                "logs" => directories.LogsDirectory,
                "temp" => directories.TempDirectory,
                "output" => directories.OutputDirectory,
                "models" => directories.ModelsDirectory,
                "sample" or "sampledata" => directories.SampleDataDirectory,
                "config" => directories.ConfigDirectory,
                "backup" => directories.BackupDirectory,
                "cache" => directories.CacheDirectory,
                "scripts" => directories.ScriptsDirectory,
                _ => null
            };

            if (string.IsNullOrEmpty(directoryPath))
            {
                return BadRequest($"不支持的目录类型: {directoryType}");
            }

            var result = await _directoryService.OpenDirectoryAsync(directoryPath);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开系统目录失败: {DirectoryType}", directoryType);
            return StatusCode(500, new { error = "打开失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取目录内容
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="includeSubdirectories">是否包含子目录</param>
    /// <returns>目录内容</returns>
    [HttpGet("content")]
    [ProducesResponseType(typeof(DirectoryContent), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<DirectoryContent>> GetDirectoryContent(
        [FromQuery] string directoryPath,
        [FromQuery] bool includeSubdirectories = false)
    {
        try
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                return BadRequest("目录路径不能为空");
            }

            var content = await _directoryService.GetDirectoryContentAsync(directoryPath, includeSubdirectories);
            return Ok(content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取目录内容失败: {DirectoryPath}", directoryPath);
            return StatusCode(500, new { error = "获取失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 创建目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>操作结果</returns>
    [HttpPost("create")]
    [ProducesResponseType(typeof(bool), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<bool>> CreateDirectory([FromForm] string directoryPath)
    {
        try
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                return BadRequest("目录路径不能为空");
            }

            var result = await _directoryService.CreateDirectoryAsync(directoryPath);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建目录失败: {DirectoryPath}", directoryPath);
            return StatusCode(500, new { error = "创建失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 删除目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="recursive">是否递归删除</param>
    /// <returns>操作结果</returns>
    [HttpDelete("delete")]
    [ProducesResponseType(typeof(bool), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<bool>> DeleteDirectory(
        [FromQuery] string directoryPath,
        [FromQuery] bool recursive = false)
    {
        try
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                return BadRequest("目录路径不能为空");
            }

            var result = await _directoryService.DeleteDirectoryAsync(directoryPath, recursive);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除目录失败: {DirectoryPath}", directoryPath);
            return StatusCode(500, new { error = "删除失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取目录大小
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>目录大小（字节）</returns>
    [HttpGet("size")]
    [ProducesResponseType(typeof(long), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<long>> GetDirectorySize([FromQuery] string directoryPath)
    {
        try
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                return BadRequest("目录路径不能为空");
            }

            var size = await _directoryService.GetDirectorySizeAsync(directoryPath);
            return Ok(size);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取目录大小失败: {DirectoryPath}", directoryPath);
            return StatusCode(500, new { error = "获取失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 清理临时文件
    /// </summary>
    /// <param name="olderThanDays">清理多少天前的文件</param>
    /// <returns>清理的文件数量</returns>
    [HttpPost("cleanup-temp")]
    [ProducesResponseType(typeof(int), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<int>> CleanupTempFiles([FromForm] int olderThanDays = 7)
    {
        try
        {
            var cleanedCount = await _directoryService.CleanupTempFilesAsync(olderThanDays);
            return Ok(cleanedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理临时文件失败");
            return StatusCode(500, new { error = "清理失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取磁盘使用情况
    /// </summary>
    /// <returns>磁盘使用情况</returns>
    [HttpGet("disk-usage")]
    [ProducesResponseType(typeof(DiskUsage), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<DiskUsage>> GetDiskUsage()
    {
        try
        {
            var diskUsage = await _directoryService.GetDiskUsageAsync();
            return Ok(diskUsage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取磁盘使用情况失败");
            return StatusCode(500, new { error = "获取失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取支持的目录类型
    /// </summary>
    /// <returns>支持的目录类型列表</returns>
    [HttpGet("supported-types")]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult GetSupportedDirectoryTypes()
    {
        var types = new
        {
            types = new[]
            {
                new { name = "数据目录", value = "data", description = "存储处理后的数据文件" },
                new { name = "日志目录", value = "logs", description = "系统运行日志文件" },
                new { name = "临时目录", value = "temp", description = "临时文件存储" },
                new { name = "输出目录", value = "output", description = "处理结果输出" },
                new { name = "模型目录", value = "models", description = "AI 模型文件" },
                new { name = "示例数据", value = "sample", description = "示例 DICOM 文件" },
                new { name = "配置目录", value = "config", description = "配置文件" },
                new { name = "备份目录", value = "backup", description = "数据备份" },
                new { name = "缓存目录", value = "cache", description = "缓存文件" },
                new { name = "脚本目录", value = "scripts", description = "脚本文件" }
            }
        };

        return Ok(types);
    }
}
