using System.IO;
using System.Windows;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;

namespace MedicalImageAnalysis.Wpf
{
    /// <summary>
    /// SettingsWindow.xaml 的交互逻辑
    /// </summary>
    public partial class SettingsWindow : Window
    {
        private readonly ILogger<SettingsWindow> _logger;

        public SettingsWindow()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<SettingsWindow>.Instance;
            LoadSettings();
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // 从配置文件或注册表加载设置
                // 这里使用默认值作为示例
                // 使用默认值
                ApiUrlTextBox.Text = "http://localhost:5000";
                TimeoutSlider.Value = 30;
                ConfidenceSlider.Value = 0.5;
                ConcurrencySlider.Value = 2;
                DataDirectoryTextBox.Text = "./data";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载设置时发生错误");
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                // 简化保存逻辑，暂时只显示消息
                // 实际项目中可以保存到配置文件或数据库
                var settings = new Settings
                {
                    ApiUrl = ApiUrlTextBox.Text,
                    TimeoutSeconds = (int)TimeoutSlider.Value,
                    ConfidenceThreshold = ConfidenceSlider.Value,
                    MaxConcurrency = (int)ConcurrencySlider.Value,
                    DataDirectory = DataDirectoryTextBox.Text
                };
                settings.Save();

                WpfMessageBox.Show("设置已保存成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存设置时发生错误");
                MessageBox.Show($"保存设置时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 浏览数据目录
        /// </summary>
        private void BrowseDataDirectory_Click(object sender, RoutedEventArgs e)
        {
            // 使用简单的输入对话框代替文件夹浏览器
            var folderPath = Microsoft.VisualBasic.Interaction.InputBox(
                "请输入数据存储目录路径:",
                "选择数据存储目录",
                DataDirectoryTextBox.Text);

            if (!string.IsNullOrEmpty(folderPath))
            {
                DataDirectoryTextBox.Text = folderPath;
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        private void ResetDefaults_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置为默认设置吗？", "确认", 
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                ApiUrlTextBox.Text = "http://localhost:5000";
                TimeoutSlider.Value = 30;
                ConfidenceSlider.Value = 0.5;
                ConcurrencySlider.Value = 2;
                DataDirectoryTextBox.Text = "./data";
            }
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void Save_Click(object sender, RoutedEventArgs e)
        {
            SaveSettings();
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}

/// <summary>
/// 应用程序设置类
/// </summary>
public static class Properties
{
    public static Settings Default { get; } = new Settings();
}

/// <summary>
/// 设置存储类
/// </summary>
public class Settings
{
    public string? ApiUrl { get; set; }
    public int TimeoutSeconds { get; set; }
    public double ConfidenceThreshold { get; set; }
    public int MaxConcurrency { get; set; }
    public string? DataDirectory { get; set; }

    /// <summary>
    /// 构造函数，设置默认值
    /// </summary>
    public Settings()
    {
        ApiUrl = "http://localhost:5000";
        TimeoutSeconds = 30;
        ConfidenceThreshold = 0.5;
        MaxConcurrency = 2;
        DataDirectory = "./data";
    }

    public void Save()
    {
        // 这里可以实现保存到配置文件或注册表的逻辑
        // 为了简化，暂时只在内存中保存
    }
}
